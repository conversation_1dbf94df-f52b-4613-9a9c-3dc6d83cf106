*** Settings ***
Library     ${EXECDIR}${/}resources${/}libraries${/}mailosaur_helper.py


*** Variables ***
${default_phone_number}             +35800000
${mailosaur_number}                 +3584573966771
${mailosaur_sent_to_number}         3584573966771
${mailosaur_sms_api_key}            ZBzuprq4roLdfwNg
${mailosaur_sms_server_id}          tiazhwes
${ext-b_mailosaur-url}              https://mailosaur.com/app/servers/w9woemno
${ext-b_server_id}                  w9woemno
${ext-b_server_domain}              w9woemno.mailosaur.net
${f04ca06-ext-b_email}              <EMAIL>
@{f01n09_mailosaur_keys}            brnqcoco    9TBtMlVkWifXcFsf
@{f03p02_email_keys}                oedetdhc    J2CKEjol22TChpIl
@{f01p03_email_keys}                toym8fbr    8twfTBiAli6ybEbt
@{time_constraint_keys}             djglkkoj    G2Tod0ogjEJmzpMa
@{f07u01_email_keys}                azix1qfe    LYeMGQKeqkkOacZ7e5CgkrsDvSU4fwY5
@{smart_symptom_tracking_keys}      cfpopi2p    A6GbKY8YBj5cJGPocTJZ4ejbYIyp7uP9
${smart_symptom_email_content}      ${EMPTY}
@{f07u02_f07p02_keys}               zpenchpz    bwhKxj7460qmlVLBoev5a8U9NaTkRMUM
@{f07u05_email_keys}                id865huu    bQhjNcauNctgsSZ5
@{settings_email_keys}              zq0od94c    CGb4PNpTgSyvEISa
@{f07p08_email_keys}                0xeu260q    ZUKI3Zs1EcXR7WWE
@{f13p02_1_email_keys}              ygimdhzc    w2zJ9oaMnhs9bP73D9f6LsLDACq7DOV5
@{f01n14_email_keys}                u43s4oof    Nggf7nHdkKGwG5bMrDHjKeZbjkq1FtJu
@{f07p17_email_keys}                cfdc2bcz    Jlrm2pU86N0dNZbD
@{f13p02_email_keys}                k7a2cm2s    fNpbVwfCPkFjbm7eFyT7cL04Ag2F5oRQ
${reset_password_subject}           Reset password for
@{f07u06_email_keys}                rzuhc1jq    bZaxcCeDGZCC0c4JdaegLpvG3TcUQPt8
@{f07p18_email_keys}                re70bcwf    copJJAqEqRqFRQbtXjq9Pdg3rSpFxzdb
@{f07u05_1_email_keys}              89hpnhjq    qaBepKHPXM9HVPdp1NF75je20YDlN2RA
@{f01n07_email_keys}                reepcebx    6XF2YwdPzZQotMzI
@{f14p04_email_keys}                cg5kasyz    8FiacC4Ei8ntN0P84MhyduRzvQ1HuomI


*** Keywords ***
Patient Received Invitation To Use Noona
    [Documentation]    spmc=yes if checking email for additional clinic activation
    [Arguments]    ${new_email}    ${clinic_name}    ${spmc}=no
    ${email_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    @{message_data}    Create List    ${link}    ${email_id}
    Should Be Equal    ${email_subject}    ${clinic_name} invites you to use Noona
    IF    '${spmc}'=='no'
        Should Contain    ${email_body}    Invitation to Noona
    ELSE
        Should Contain    ${email_body}    Connect your Noona account to ${clinic_name}
    END
    RETURN    @{message_data}

Patient Received Invitation To Answer AEQ
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    @{message_data}    Create List    ${link}    ${email_id}
    Should Be Equal    ${email_subject}    New Symptom Questionnaire from ${clinic_name}
    Should Contain    ${html_body}    Your care team at ${clinic_name} has sent you a symptom questionnaire
    Should Contain
    ...    ${email_body}
    ...    Your care team will use this information to understand your current symptoms and prepare for any upcoming visits.
    ...    ${SPACE}Please respond to the questionnaire by the end of the day.
    RETURN    @{message_data}

Patient Received Invitation To Answer QoL
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    New Quality of Life Questionnaire from ${clinic_name}
    Should Contain    ${html_body}    New questionnaire
    Should Contain
    ...    ${email_body}
    ...    Your care team at ${clinic_name} has sent you a questionnaire in Noona to keep track of your progress.
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Your care team at ${clinic_name}
    Should Contain
    ...    ${html_body}
    ...    If the button does not display correctly, you can copy the web address below to your browser.
    RETURN    ${link}

Patient Received Email About Announcement
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}    timeout=80
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    New message from ${clinic_name}
    Should Contain    ${html_body}    You have received a new message from ${clinic_name}
    Should Contain    ${email_body}    Please log in to read the message.
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Noona team
    RETURN    ${link}

Patient Received Email About Follow-Up Symptom
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message
    ...    ${smart_symptom_tracking_keys}[0]
    ...    ${smart_symptom_tracking_keys}[1]
    ...    ${new_email}
    ...    timeout=80
    ${email_subject}    Get Email Subject
    ...    ${smart_symptom_tracking_keys}[0]
    ...    ${smart_symptom_tracking_keys}[1]
    ...    ${new_email}
    ${link}    Get Link In Email
    ...    ${smart_symptom_tracking_keys}[0]
    ...    ${smart_symptom_tracking_keys}[1]
    ...    ${new_email}
    Should Be Equal    ${email_subject}    How are you feeling today?
    Should Contain
    ...    ${email_body}
    ...    How are you feeling today?\n\nYou can help your care team at ${clinic_name} keep track of your progress during treatment by updating your symptom and wellbeing information in your diary.
    ...    This information will be automatically added to the next questionnaire provided by your care team ? all you need to do is check and confirm that the information is correct.\n\n${link}\n\nBest regards\nYour care team at ${clinic_name}\n\nIf the button does not display correctly,
    ...    you can copy the web address below to your browser.\n\n
    RETURN    ${link}

Patient Received Successful Account Activation Email
    [Arguments]    ${new_email}    ${clinic_name}    ${custom_clinic}=Noona
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    Welcome to ${custom_clinic}
    Should Contain    ${html_body}    Congratulations, your account is now ready!
    Should Contain
    ...    ${email_body}
    ...    You can log in to Noona with your email address and password here:
    Should Contain    ${email_body}    For easy access, please bookmark Noona in your browser.
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Your care team at ${clinic_name}
    Should Contain
    ...    ${html_body}
    ...    If the button does not display correctly, you can copy the web address below to your browser.
    Should Contain    ${html_body}    The Noona privacy statement is available at the Noona service.
    Should Contain    ${html_body}    Download the Noona App
    Should Contain
    ...    ${html_body}
    ...    For an easy login, we recommend that you use the Noona App. You can download it from the App store or Google Play.
    RETURN    ${link}

Patient Received Welcome To Custom Clinic Email
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    Welcome to ${clinic_name}
    Should Contain    ${html_body}    Congratulations, you can now access ${clinic_name} in Noona
    Should Contain
    ...    ${email_body}
    ...    All you need is the email address and password that you used when you set up your Noona account.
    Should Contain    ${email_body}    For easy access, please bookmark Noona in your browser.
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Your care team at ${clinic_name}
    Should Contain
    ...    ${html_body}
    ...    If the button does not display correctly, you can copy the web address below to your browser.
    Should Contain    ${html_body}    The Noona privacy statement is available at the Noona service.
    RETURN    ${link}

Patient Received An Email About A New Message
    [Arguments]    ${new_email}    ${subject}    ${text_body}    ${check_link}=no
    @{message_data}    Create List
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Append To List    ${message_data}    ${email_id}
    Append To List    ${message_data}    ${email_body}
    Should Be Equal    ${email_subject}    ${subject}
    Should Contain    ${email_body}    ${text_body}
    IF    "${check_link}"!="no"
        ${link_in_email}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
        Should Contain    ${link_in_email}    ${check_link}
        Append To List    ${message_data}    ${link_in_email}
    END
    RETURN    @{message_data}

Patient Received An Email About Reset Password
    [Arguments]    ${new_email}    ${check_link}=no    ${clinic}=Noona    ${request_from}=patient   ${expiry_time_format}=%d.%m.%Y
    ${today}    Get Current Date
    ${expires}    Add Time To Date    ${today}    14 days    result_format=${expiry_time_format}
    @{message_data}    Create List
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Append To List    ${message_data}    ${email_id}
    IF    '${request_from}' == 'clinic user'
        Should Be Equal    ${email_subject}    Login link to ${clinic}
    # If the request was made by patient: from "Problems logging in?" or "Forgot password?" (on password update page)
    ELSE
        Should Be Equal    ${email_subject}    Reset password for ${clinic}
    END
    Should Contain    ${email_body}    New password for ${clinic}
    Should Contain    ${email_body}    A password change has been requested for your account. If this was you, please click the link to reset your password before the link expires on ${expires}.
    Should Contain    ${email_body}    If you didn't make the request, just ignore this message.
    Should Contain    ${email_body}    If the problem persists, please contact your clinic for assistance.
    IF    '${clinic}' != 'Noona'
        Should Contain    ${email_body}    With kind regards,\n${clinic}
        Should Contain    ${email_body}    The ${clinic} privacy statement is available at the ${clinic} service.
    ELSE
        Should Contain    ${email_body}    With kind regards,\nNoona team
        Should Contain    ${email_body}    The Noona privacy statement is available at the Noona service.
    END
    IF    "${check_link}"!="no"
        ${link_in_email}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
        Append To List    ${message_data}    ${link_in_email}
    END
    IF    "${check_link}"!="no"
        ${link_in_email}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
        Append To List    ${message_data}    ${link_in_email}
    END
    RETURN    @{message_data}

Patient Received An Email About Questionnaire Review
    [Arguments]    ${new_email}    ${clinic}
    @{message_data}    Create List
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Append To List    ${message_data}    ${email_id}
    Should Be Equal    ${email_subject}    Thank you for submitting your questionnaire
    Should Contain    ${email_body}    Your care team at ${clinic} has reviewed your questionnaire

Patient Received An Email About Case Consultation
    [Arguments]    ${new_email}    ${clinic}
    @{message_data}    Create List
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Append To List    ${message_data}    ${email_id}
    Should Be Equal    ${email_subject}    Your care team at ${clinic} is now discussing your question
    Should Contain
    ...    ${email_body}
    ...    is now discussing your question with your care team at ${clinic}, and will get back to you soon.

Patient Is Notified About Login Instructions
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    @{message_data}    Create List    ${link}    ${email_id}
    Should Be Equal    ${email_subject}    Instructions to log into Noona
    Should Contain    ${html_body}    Log in
    Should Contain    ${email_body}    Please log into Noona and set a password before this login link expires
    Should Contain    ${email_body}    If you continue having problems with logging in, please contact your clinic.
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Your care team at ${clinic_name}
    Should Contain
    ...    ${html_body}
    ...    If the button does not display correctly, you can copy the web address below to your browser.
    Should Contain    ${html_body}    The Noona privacy statement is available at the Noona service.
    RETURN    @{message_data}

Patient Is Notified About Locked Account
    [Arguments]    ${new_email}    ${clinic_name}
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    Your Noona account has been deactivated
    Should Contain    ${html_body}    Your Noona account has been deactivated
    Should Contain
    ...    ${email_body}
    ...    Your Noona account has been deactivated since it is no longer needed for your
    ...    If you think that you still need an activated Noona account, please contact your care team at ${automated_tests_clinic}[name].
    Should Contain    ${html_body}    With kind regards,
    Should Contain    ${html_body}    Your care team at ${clinic_name}

Patient Is Notified About Account Temporarily Locked
    [Arguments]    ${new_email}    ${timeout}=120
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}    ${timeout}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    Your Noona account was temporarily locked
    Should Contain    ${html_body}    Account temporarily locked
    Should Contain    ${email_body}    There were multiple failed login attempts on your account on
    Should Contain
    ...    ${email_body}
    ...    We have temporarily locked your account to ensure it is safe. If this was you, you can try logging in again once we unlock your account in 10 minutes.
    Should Contain
    ...    ${email_body}
    ...    If you would like to access your account now or if you forgot your password, click the button below.
    Should Contain
    ...    ${email_body}
    ...    If you did not make the request, just ignore the message. If you continue to get these emails in error, consider changing your password or enabling two-factor authentication for added security.
    Should Contain    ${email_body}    If the problem persists, please contact your clinic for assistance.

Patient Is Notified About Account Being Locked
    [Arguments]    ${new_email}    ${timeout}=120
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}    ${timeout}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    ${html_body}    Get HTML Body    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${new_email}
    Should Be Equal    ${email_subject}    Your Noona account was locked
    Should Contain    ${html_body}    Locked account
    Should Contain    ${email_body}    There were multiple failed login attempts on your account on
    Should Contain    ${email_body}    We have locked your account to ensure it is safe
    Should Contain    ${email_body}    Unlock your account by clicking the button below.

User Received SMS
    [Documentation]    User can be patient or nurse
    ${sms_message}    Wait Until Keyword Succeeds
    ...    10x
    ...    5s
    ...    Get SMS Message
    ...    ${mailosaur_sms_server_id}
    ...    ${mailosaur_sms_api_key}
    ...    ${mailosaur_sent_to_number}
    RETURN    ${sms_message}

Patient Opens SMS Link From Mailosaur Server
    Sleep    3s
    ${invitation_link}    Get Link In Sms
    ...    ${mailosaur_sms_server_id}
    ...    ${mailosaur_sms_api_key}
    ...    ${mailosaur_sent_to_number}
    Set Test Variable    ${invitation_link}

Patient Received SMS About A New Message
    [Arguments]    ${clinic}
    ${sms}    User Received SMS
    Should Contain    ${sms}    Your care team at ${clinic} sent you a message in Noona. Login to read the message.
    Should Contain    ${sms}    ${PATIENT_URL_IN_MESSAGE}/patient/

Patient Received SMS Invitation To Use Noona
    [Arguments]    ${clinic}
    ${sms}    User Received SMS
    Should Contain
    ...    ${sms}
    ...    Hello. ${clinic} would like to invite you to use Noona. The tools available in Noona can empower you to take an active role in your treatment.
    Should Contain    ${sms}    ${PATIENT_URL_IN_MESSAGE}
    ${link}    Get Link In SMS    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}    ${mailosaur_sent_to_number}
    Set Test Variable    ${link}

Patient Received SMS Invitation Reminder
    [Arguments]    ${clinic}
    ${sms}    User Received SMS
    Should Contain
    ...    ${sms}
    ...    Hi, please remember to activate your account in the Noona online service provided by ${clinic}. Your invitation expires at
    Should Contain    ${sms}    ${PATIENT_URL_IN_MESSAGE}

Patient Received An Email About New Lab Results
    [Arguments]    ${patient_email}    ${clinic}    ${check_link}=no
    @{message_data}    Create List
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
    ${email_id}    Get Message Id    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
    Append To List    ${message_data}    ${email_body}
    Should Be Equal    ${email_subject}    New lab results from ${clinic}
    Should Contain    ${email_body}    Your results from your laboratory visit on
    Should Contain    ${email_body}    are now available in your ${clinic} library.
    Should Contain
    ...    ${email_body}
    ...    Lab results are released automatically as soon as they become available, and they may not have been reviewed by the care team yet.
    IF    "${check_link}"!="no"
        ${link_in_email}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
        Should Contain    ${link_in_email}    ${check_link}
        Append To List    ${message_data}    ${link_in_email}
    END
    RETURN    @{message_data}

Care Team User Received An Email About A New Case
    [Arguments]    ${server_id}    ${api_key}    ${email}    ${priority}=Medium
    ${email_body}    Get Email Message    ${server_id}    ${api_key}    ${email}
    ${todays_date}    Get Current Date    result_format=%d.%m.%Y
    ${email_subject}    Get Email Subject    ${server_id}    ${api_key}    ${email}
    Set Test Variable    ${todays_date}
    Should Be Equal    ${email_subject}    ${priority} priority case created
    Should Contain    ${email_body}    You have a new case in the Noona work queue
    ${priority}    Convert To Lower Case    ${priority}
    Should Contain    ${email_body}    Case with ${priority} priority was added to work queue on ${todays_date}
    Should Contain    ${email_body}    View case
    Should Contain    ${email_body}    Do not reply to this email.
    Should Contain
    ...    ${email_body}
    ...    If the button does not display correctly, copy the web address into your browser.
    ${view_case_link}    Get Link In Email    ${server_id}    ${api_key}    ${email}
    RETURN    ${view_case_link}

Set Email And Delete Previous Messages
    [Arguments]    ${server_id}    ${api_key}
    Set Test Variable    @{mailosaur_keys}    ${server_id}    ${api_key}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

User Received An Email About A New Message
    [Arguments]    ${email}    ${subject}    ${text_body}    ${check_link}=no
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
    Should Be Equal    ${email_subject}    ${subject}
    Should Contain    ${email_body}    ${text_body}
    IF    "${check_link}"!="no"
        ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
        Should Contain    ${link}    ${check_link}
        RETURN    ${link}
    END

Email Notification Is Not Received
    [Arguments]    ${mailosaur_server}    ${mailosaur_api_key}    ${email}
    ${status}    ${failure_message}    Run Keyword And Ignore Error
    ...    Get Email Message
    ...    ${mailosaur_server}
    ...    ${mailosaur_api_key}
    ...    ${email}
    ...    54
    Should Be Equal    ${status}    FAIL
    Should Contain    ${failure_message}    No matching messages found in time.

Nurse Received Consultation Request Email
    [Arguments]    ${email}    ${care_team}    ${nurse_name}
    User Received An Email About A New Message
    ...    ${email}
    ...    Noona consultation request
    ...    Consultation request to ${care_team} / ${nurse_name}.

Delegate User Opens Password Reset Link Via Email
    [Arguments]    ${delegate_user_email}
    @{message_data}    Patient Received An Email About Reset Password    ${delegate_user_email}    yes
    Set Test Variable    ${link}    ${message_data}[1]
    Set Test Variable    ${email_id}    ${message_data}[0]

Patient Received New Login Link Email
    [Arguments]    ${email}    ${check_link}=no
    ${email_body}    Get Email Message    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
    ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
    ${date}    Get Current Date
    ${date}    Add Time To Date    ${date}    14 days    result_format=%d.%m.%Y
    Should Be Equal    ${email_subject}    Login link to Noona
    Should Contain    ${email_body}    A password change has been requested for your account. If this was you, please click the link to reset your password before the link expires on ${date}.
    IF    "${check_link}"!="no"
        ${link}    Get Link In Email    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${email}
        Should Contain    ${link}    ${check_link}
        RETURN    ${link}
    END