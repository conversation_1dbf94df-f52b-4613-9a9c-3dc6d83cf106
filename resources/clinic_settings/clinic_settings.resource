*** Settings ***
Documentation    Common settings for all clinic settings resources
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${patient_messaging}        Patient messaging
${clinical_settings_tab}    //*[@id='clinical-settings' and contains(text(), 'Clinical settings')]
${clinical_settings_tab_selected}    //*[@data-testid='clinical-settings' and contains(@class, 'selected')]
${clinic_settings_link}     clinic-settings-link
${clinic_settings_save_button}              save


*** Keywords ***
Select Case management tab
    Try To Click Element    ${case_management_tab}
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains Element    ${case_management_tab_selected}

Select Clinical settings tab
    Try To Click Element    ${clinical_settings_tab}
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains Element    ${clinical_settings_tab_selected}

Open "Clinic settings", Basic settings tab is selected by default
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Should Contain    ${patient_messaging}    Patient messaging

Select Clinic Settings Radio Button
    [Arguments]    ${question}    ${answer}
    [Documentation]    Doesn't select if already selected
    ${status}    Run Keyword And Return Status    Should Contain    ${question}    '
    ${element}    Set Variable If    ${status}    //*[contains(text(),"${question}")]/../..//span[text()="${answer}"]
    ...    //*[contains(text(),'${question}')]/../..//span[text()='${answer}']
    Wait Until Element Is Visible    ${element}    timeout=25s
    ${attr}    Get Element Attribute    ${element}/../../..    class
    IF    'radio-checked' not in '${attr}'
        Try To Click Element    ${element}
        ${attr}    Get Element Attribute    ${element}/../../..    class
        Should Contain    ${attr}    radio-checked
    END

Select Clinic Settings Checkbox
    [Arguments]    ${checkbox_name}    ${in_group}=Yes
    ${checkbox}  Set Variable If    '${in_group}'=='Yes'
     ...    //*[contains(text()," ${checkbox_name}")]//ancestor::formly-field[@class='repeat-section']//mat-checkbox
     ...    //*[contains(text()," ${checkbox_name} ")]/../../div[@class='mdc-checkbox']
    Wait Until Element Is Visible    ${checkbox}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' not in '${attr}'
        Try To Click Element    ${checkbox}
    END

Unselect Clinic Settings Checkbox
    [Arguments]    ${checkbox_name}    ${in_group}=Yes
    ${checkbox}  Set Variable If    '${in_group}'=='Yes'
     ...    //*[contains(text()," ${checkbox_name}")]//ancestor::formly-field[@class='repeat-section']//mat-checkbox
     ...    //*[contains(text()," ${checkbox_name} ")]/../../div[@class='mdc-checkbox']/../..
    Wait Until Element Is Visible    ${checkbox}
    ${attr}    Get Element Attribute    ${checkbox}//ancestor::mat-checkbox    class
    IF    'checkbox-checked' in '${attr}'
        Try To Click Element    ${checkbox}
    END

Select Radio Button In Group
    [Arguments]    ${identifier}
    ${radio_button}    Set Variable    //*[contains(text(),"${identifier}")]//ancestor::formly-field[2]//mat-radio-button/div
    Wait Until Element Is Visible    ${radio_button}
    Try To Click Element    ${radio_button}

Click Settings Dropdown
    [Arguments]    ${dropdown_name}
    ${dropdown}    Set Variable    //*[contains(text(),"${dropdown_name}")]//ancestor::formly-field[1]//noona-ng-select/div
    Wait Until Element Is Visible     ${dropdown}
    Try To Click Element    ${dropdown}

Select From Settings Dropdown
    [Arguments]    ${dropdown_name}    ${dropdown_option}
    VAR    ${dropdown_element}    //*[contains(text(),"${dropdown_name}")]//ancestor::formly-field[1]
    Wait Until Element Is Visible  ${dropdown_element}//noona-ng-select/div
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${dropdown_element}//div[@title='${dropdown_option}']/..
    IF    '${status}'!='${TRUE}'
        Click Settings Dropdown    ${dropdown_name}
        Select Dropdown Option    ${dropdown_option}
    END

Select Dropdown Option
    [Arguments]    ${dropdown_option}
    Try To Click Element    (//div[contains(text(),"${dropdown_option}")])[1]

Save Settings
    Wait Until Element Is Visible    ${clinic_settings_save_button}
    ${loc}    Get Location
    Try To Click Element    ${clinic_settings_save_button}
    IF    'clinical-settings' in '${loc}'    # the page reloads automatically after saving (as designed) so it cannot see the banner
        Sleep    1
        Wait Until Noona Loader Is Not Visible    # wait until save button disappears while page is loading
        Wait Until Element Is Visible    ${clinic_settings_save_button}    # wait until save button is visible again after reload
        Sleep    1
    ELSE
        Wait Until Page Contains    Clinic settings are updated
        Try To Click Banner Message
    END

Verify If Clinic Settings Radio Button Is Selected Or Not
    [Arguments]    ${question}    ${enabled}
    ${status}    Run Keyword And Return Status    Should Contain    ${question}    '
    ${element}    Set Variable If    ${status}    //*[contains(text(),"${question}")]/../..//span[text()=Yes"]
    ...    //*[contains(text(),'${question}')]/../..//span[text()='Yes']
    Wait Until Element Is Visible    ${element}    timeout=25s
    ${attr}    Get Element Attribute    ${element}/../../..    class
    IF    '${enabled}'=='enabled'
        Should Contain    ${attr}    radio-checked
    ELSE
        Should Not Contain    ${attr}    radio-checked
    END