*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${clinic_settings_link}                             clinic-settings-link
${integration_settings_tab}                         integration-settings
${laboratory_results_label}                         //h4[contains(text(),'Laboratory Results')]
${lab_results_visible_yes}                          //*[@data-testid="lab-results-enabled"]//label
${lab_results_visible_no}                           //*[@data-testid="lab-results-disabled"]//label
${lab_results_notifications_yes}                    //*[@data-testid="lab-result-notifications-enabled"]
${lab_results_notifications_no}                     //*[@data-testid="lab-result-notifications-disabled"]
${lab_results_multi_notif_no}                       //*[@data-testid="lab-result-notify-once-per-day-disabled"]
${lab_results_multi_notif_yes}                      //*[@data-testid="lab-result-notify-once-per-day-enabled"]
${integration_save_button}                          //*[@data-testid="save"]
${save_toast_message}                               //div[@id="toast-container"]
${integration_header}                               //h4[text()="Integration"]
${patient_messaging_header_text}                    Patient messaging
${medication_list_setting_label}                    //*[contains(text(), " Should the medication list be available to the patient? ")]
${medication_list_setting_enabled_btn}              //*[@data-testid="medication-list-enabled"]//label
${medication_list_setting_disabled_btn}             //*[@data-testid="medication-list-disabled"]//label
${common_fhir_integration_settings}                 //*[@class='noona-fhir-integration-form-inner']
${customer_tenant_id}                               //*[@data-testid='tenant-id']
${data_integrations_checkbox}                       //*[contains(text(),"{}")]//ancestor::formly-field[1]/../formly-field[1]//mat-checkbox
${fhir_integration_settings_enabled_text}           Is the FHIR integration enabled for the customer?
${integration_enabled_for_clinic_text}              Is integration enabled for the clinic?
${aria_site_id_mandatory_id_text}                   ARIA Site ID is a mandatory patient identifier.
${fhir_integration_enabled_btn}                     //*[@data-testid='fhir-integration-enabled']//label
${fhir_integration_disabled_btn}                    //*[@data-testid='fhir-integration-disabled']//label
${integration_for_clinic_enabled_btn}               //*[@data-testid='integration-enabled']//label
${integration_for_clinic_disabled_btn}              //*[@data-testid='integration-disabled']//label
${patient_status_change_enabled_text}               Is patient status change integration enabled?
${status_change_integration_endpoint}               //*[@data-testid='statusChangeIntegrationEndpoint']
${status_change_integration_endpoint_value}         noonatest.com
${status_change_integration_username}               //*[@data-testid='statusChangeIntegrationUsername']
${status_change_integration_username_value}         username
${status_change_integration_password}               //*[@data-testid='statusChangeIntegrationPassword']
${status_change_integration_password_value}         password-1
${mapping_ariaid1_text}                             Mapping of ARIAID1
${mrn_ssn_ins_integration_text}                     Allow MRN/SSN/INS update through integration
${aria_care_team_initialisation_text}               Is ARIA care team initialisation enabled?
${patientmessage_questionnaire_casesummery_text}    Patient message, questionnaire and case summary export integration enabled?
${data_lake_integration_label}                      //*[contains(text(), " Is data lake integration enabled? ")]
${data_lake_integration_enabled_btn}                //*[@data-testid="datalake-integration-enabled"]//label
${data_lake_integration_disabled_btn}                //*[@data-testid="datalake-integration-disabled"]//label
${dli_account_name_text}          //*[contains(text(), " Account name ")]
${dli_account_name_input}         //input[@data-testid="datalakeConfiguration-accountName"]
${dli_account_name_value}         ddata_lake_integration_account_name
${dli_container_name_text}          //*[contains(text(), " Container name ")]
${dli_container_name_input}         //input[@data-testid="datalakeConfiguration-containerName"]
${dli_container_name_value}         data_lake_integration_container_name
${dli_encryption_key_url_text}          //*[contains(text(), " Encryption key url ")]
${dli_encryption_key_url_input}         //input[@data-testid="datalakeConfiguration-encryptionKey"]
${dli_encryption_key_url_value}         data_lake_integration_encryption_key_url
${dli_public_key_text}          //*[contains(text(), " Public key ")]
${dli_public_key_input}         //input[@data-testid="datalakeConfiguration-publicKey"]
${dli_public_key_value}         data_lake_integration_public_key
${dli_share_access_signature_token_text}          //*[contains(text(), " Shared Access Signature token ")]
${dli_share_access_signature_token_input}         //input[@data-testid="datalakeConfiguration-sasToken"]
${dli_share_access_signature_token_value}         data_lake_integration_share_access_signature_token



*** Keywords ***
Open "Clinic settings" And Select Integration Settings Tab
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Noona Loader Is Not Visible
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${integration_header}
    WHILE    ${status}!=${TRUE}    limit=10
        Wait Until Element Is Visible    ${integration_settings_tab}
        Wait Until Noona Loader Is Not Visible
        Try To Click Element    ${integration_settings_tab}    wait_in_seconds=7s
        ${status}    Run Keyword And Return Status    Element Should Be Visible    ${integration_header}
    END

Enable Laboratory Results Settings
    Wait Until Element Is Visible    ${laboratory_results_label}
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Try To Click Element    ${lab_results_visible_no}
    Try To Click Element    ${lab_results_visible_yes}
    Try To Click Element    ${lab_results_notifications_yes}
    Try To Click Element    ${lab_results_multi_notif_yes}

Disable Laboratory Results Settings
    Wait Until Element Is Visible    ${laboratory_results_label}
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Try To Click Element    ${lab_results_visible_yes}
    Try To Click Element    ${lab_results_visible_no}

Save Integration Settings
    Wait Until Page Contains Element    ${integration_save_button}
    Scroll Element Into View    ${integration_save_button}
    Wait until keyword succeeds    3x    1s    Try To Click Element    ${integration_save_button}
    Wait Until Element Is Visible    ${save_toast_message}
    Try To Click Banner Message
    Sleep    1    # add sleep to make sure that setting is saved before reloading the page
    Reload Page
    Sleep    3s
    Wait Until Element Is Visible    ${laboratory_results_label}    timeout=20s
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Close Browser

Check Status Of Medication List Settings
    Wait Until Element Is Visible    ${medication_list_setting_label}
    Scroll Element Into View    ${medication_list_setting_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${medication_list_setting_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Medication List Settings
    Wait Until Page Contains Element    ${medication_list_setting_enabled_btn}
    Try To Click Element    ${medication_list_setting_enabled_btn}

Disable Medication List Settings
    Wait Until Page Contains Element    ${medication_list_setting_disabled_btn}
    Try To Click Element    ${medication_list_setting_disabled_btn}

Clinic Admin Enables Lab Results In Clinic Settings
    [Arguments]    ${clinic}
    Login As Nurse    clinic=${clinic}    user_type=${USER_TYPE}[noona_admin]
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Open "Clinic settings" And Select Integration Settings Tab
    Enable Laboratory Results Settings
    Save Integration Settings

Disable Data Integrations
    [Arguments]    ${data_type}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' in '${attr}'
        Try To Click Element    ${checkbox}/div
    ELSE
        Try To Click Element    ${checkbox}/div    # enable to disable to make sure save button is clickable
        Try To Click Element    ${checkbox}/div
    END
    ${attr}    Get Element Attribute    ${checkbox}    class
    Should Not Contain    ${attr}    checkbox-checked

Enable Data Integrations
    [Arguments]    ${data_type}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' in '${attr}'
        Try To Click Element    ${checkbox}/div
        Try To Click Element    ${checkbox}/div    # enable to disable to make sure save button is clickable
    ELSE
        Try To Click Element    ${checkbox}/div
    END
    ${attr}    Get Element Attribute    ${checkbox}    class
    Should Contain    ${attr}    checkbox-checked

Verify Data Integration Setting Status
    [Arguments]    ${data_type}    ${expected_status}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    Wait Until Element Is Visible    ${checkbox}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    '${expected_status}'=='enabled'
        Should Contain    ${attr}    checkbox-checked
    ELSE
        Should Not Contain    ${attr}    checkbox-checked
    END

Check Status Of FHIR Integration Setting
    Wait Until Page Contains    ${fhir_integration_settings_enabled_text}
    ${enabled_btn_attibute}    Get Element Attribute    ${fhir_integration_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable FHIR Integration Settings
    Try To Click Element    ${fhir_integration_enabled_btn}

Disable FHIR Integration Settings
    Try To Click Element    ${fhir_integration_disabled_btn}

Enable FHIR Integration And Save The Settings
    Enable FHIR Integration Settings
    Save Settings

Check Status Of Integration For Clinic Setting
    Wait Until Page Contains    ${integration_enabled_for_clinic_text}
    ${enabled_btn_attibute}    Get Element Attribute    ${integration_for_clinic_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Integration For Clinic Settings
    Try To Click Element    ${integration_for_clinic_enabled_btn}

Disable Integration For Clinic Settings
    Try To Click Element    ${integration_for_clinic_disabled_btn}

Enable Integration For Clinic And Save The Settings
    Enable Integration For Clinic Settings
    Save Settings

Input Required Fields Value For Patient Status Change Integration Settings
    Input Text    ${status_change_integration_endpoint}    ${status_change_integration_endpoint_value}
    Input Text    ${status_change_integration_username}    ${status_change_integration_username_value}
    Input Text    ${status_change_integration_password}    ${status_change_integration_password_value}

Check Status Of Data Lake Integration
    Wait Until Element Is Visible    ${data_lake_integration_label}
    Scroll Element Into View    ${data_lake_integration_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${data_lake_integration_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Data Lake Integration And Save Settings
    Try To Click Element    ${data_lake_integration_enabled_btn}
    Save Settings

Disable Data Lake Integration And Save Settings
    Try To Click Element    ${data_lake_integration_disabled_btn}
    Save Settings

Input Data Lake Account Name
    Input Text    ${dli_account_name_input}    ${dli_account_name_value}

Input Data Lake Container Name
    Input Text    ${dli_container_name_input}    ${dli_container_name_value}

Input Data Lake Encryption Key URL
    Input Text    ${dli_encryption_key_url_input}    ${dli_encryption_key_url_value}

Input Data Lake Public Key
    Input Text    ${dli_public_key_input}    ${dli_public_key_value}

Input Data Lake Shared Access Signature Token
    Input Text    ${dli_share_access_signature_token_input}    ${dli_share_access_signature_token_value}

Remove Values From Data Lake Setting Fields
    Clear Element Text    ${dli_account_name_input}
    Clear Element Text    ${dli_container_name_input}
    Clear Element Text    ${dli_encryption_key_url_input}
    Clear Element Text    ${dli_public_key_input}
    Clear Element Text    ${dli_share_access_signature_token_input}
