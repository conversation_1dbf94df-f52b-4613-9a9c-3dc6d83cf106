*** Settings ***
Library     OperatingSystem
Library     String
Library     RequestsLibrary
Library     JSONLibrary
Library     DateTime
Library     Collections
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource


*** Variables ***
${treatment_unit}               666
${date_of_birth}                1990-10-25
${phone}                        +3580000
${baseline_quest}               baseline
${treatment_visit_quest}        treatmentVisit
${clinic_appointment_quest}     clinicAppointment
${follow_up_quest}              followUp
${status_check_quest}           toPostTreatment


*** Keywords ***
Send SMS Password For Clinic User
    [Documentation]    The user's profile has enabled 2fa authentication
    [Arguments]    ${2fa_clinic_user}    ${password}    ${clinic_id}
    &{header}    Create Dictionary    Content-Type=application/json    x-request-method=sendSMSPassword
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendSMSPassword.json
    ${body_mod}    Replace String    ${body}    clinic_user_email    ${2fa_clinic_user}
    ${body_mod}    Replace String    ${body_mod}    clinic_user_password    ${password}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    Delete All Sessions

Check SMS Password For Clinic User
    [Documentation]   Use this keyword to verify 2fa's sms code validity. It might be useful in a conditional step. If sms code is invalid, we might need to generate a new one.
    [Arguments]    ${2fa_clinic_user}    ${password}    ${clinic_id}    ${sms_code}
    &{header}    Create Dictionary    Content-Type=application/json    x-request-method=checkSMSPassword
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}checkSMSPassword.json
    ${body_mod}    Replace String    ${body}    clinic_user_email    ${2fa_clinic_user}
    ${body_mod}    Replace String    ${body_mod}    clinic_user_password    ${password}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    ${body_mod}    Replace String    ${body_mod}    sms_code    ${sms_code}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    Delete All Sessions

Generate Clinic Token
    [Arguments]    ${nurse}    ${password}    ${clinic_id}    ${is_2fa_user}=no    ${sms_password}=${EMPTY}
    &{header}    Create Dictionary    Content-Type=application/json-rpc    x-request-method=login
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}generateClinicTokenRequestBody.json
    ${body_mod}    Replace String    ${body}    nurse_account    ${nurse}
    ${body_mod}    Replace String    ${body_mod}    nurse_password    ${password}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    IF    '${is_2fa_user}' == 'yes'
        ${body_mod}    Replace String    ${body_mod}    sms_code    ${sms_password}
    END
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${login_token}    Get From Dictionary    ${result_json}    loginToken
    Set Test Variable    ${login_token}
    Delete All Sessions

Generate Clinic Token Suite Level
    [Arguments]    ${nurse}    ${password}    ${clinic_id}
    &{header}    Create Dictionary    Content-Type=application/json-rpc    x-request-method=login
    Set Suite Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}generateClinicTokenRequestBody.json
    ${body_mod}    Replace String    ${body}    nurse_account    ${nurse}
    ${body_mod}    Replace String    ${body_mod}    nurse_password    ${password}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    Set Suite Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${login_token}    Get From Dictionary    ${result_json}    loginToken
    Set Suite Variable    ${login_token}
    Delete All Sessions

Get Patient UserID
    [Arguments]    ${patient_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}getPatientModelRequestBody.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${user_id}    Get From Dictionary    ${result_json}    userId
    Set Test Variable    ${user_id}
    Delete All Sessions

Get Patient UserID Suite Level
    [Arguments]    ${patient_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Suite Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}getPatientModelRequestBody.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    Set Suite Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${user_id}    Get From Dictionary    ${result_json}    userId
    Set Suite Variable    ${user_id}
    Delete All Sessions

Send Change User Password Request
    Get Patient UserID    ${patient_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}changeUserPasswordRequestBody.json
    ${body_mod}    Replace String    ${body}    user_id    ${user_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send Change User Password Request Suite Level
    Get Patient UserID Suite Level    ${patient_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Suite Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}changeUserPasswordRequestBody.json
    ${body_mod}    Replace String    ${body}    user_id    ${user_id}
    Set Suite Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send Password Reset Request Via API
    [Documentation]    Problems logging in action
    [Arguments]    ${patient_email}    ${login_token}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}passwordRequestBody.json
    ${body_mod}    Replace String    ${body}    patient_email    ${patient_email}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient   headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send Announcement To Patient Via API
    [Documentation]    Announcement sent is valid only until today. Default content message is used for this keyword.
    [Arguments]    ${careteam_name}    ${careteam_id}    ${title}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendAnnouncementRequestBody.json
    ${body_mod}    Replace String    ${body}    careteam_id    ${careteam_id}
    ${body_mod}    Replace String    ${body_mod}    careteam_name    ${careteam_name}
    ${date_time}    Get Current Date    result_format=%Y-%m-%d
    Set Test Variable    ${content_text}    This is a test announcement
    ${body_mod}    Replace String    ${body_mod}    content_text    ${content_text}
    ${body_mod}    Replace String    ${body_mod}    expiration_date    ${date_time}
    ${body_mod}    Replace String    ${body_mod}    title_text    ${title}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send Contact Patient Request
    [Documentation]    topicTypes: 18 - "Question", 11 - "Instructions", 9 - "Invitation for physical examination", 10 - "Invitation for clinical tests", 38 - "Patient education"
    [Arguments]    ${contact_type}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}contactPatientRequestBody.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    ${content_text}    Generate Random String    32
    Set Test Variable    ${content_text}
    ${body_mod}    Replace String    ${body_mod}    message_content    ${content_text}
    ${date_time}    Get Current Date    UTC
    ${date_time}    Replace String    ${date_time}    ${SPACE}    T
    ${body_mod}    Replace String    ${body_mod}    message_date_time    ${date_time}
    ${body_mod}    Replace String    ${body_mod}    contact_type    ${contact_type}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send Contact Patient Request With Attachment
    [Documentation]    Attachment_is is equal to idAtOrigin
    [Arguments]    ${contact_title}    ${attachment_title}    ${attachment_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}contactPatientWithMessageAttachment.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    ${content_text}    Generate Random String    32
    Set Test Variable    ${content_text}
    ${body_mod}    Replace String    ${body_mod}    title_text    ${contact_title}
    ${body_mod}    Replace String    ${body_mod}    attachment_title    ${attachment_title}
    ${body_mod}    Replace String    ${body_mod}    attachment_id    ${attachment_id}
    ${body_mod}    Replace String    ${body_mod}    message_content    ${content_text}
    ${date_time}    Get Current Date    UTC
    ${date_time}    Replace String    ${date_time}    ${SPACE}    T
    ${body_mod}    Replace String    ${body_mod}    message_date_time    ${date_time}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Add An Activated Patient Via API
    [Arguments]    ${ehr_token}    ${tunit}    ${clinic_user}    ${clinic_id}
    Generate Random Patient Data
    Invite Patient Via API    ${ehr_token}    ${tunit}
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Default Clinic
    [Documentation]    There are several tcs that will need this keyword so placed it
    ...    Patients will be added under TA clinic Automated_tests
    ...    tunit determines which care team the patient will be under, by default it is Care Team 2
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}    # care team 2 by default
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under All Settings Clinic
    [Documentation]    There are several tcs that will need this keyword so placed it
    ...    Patients will be added under TA clinic Automated_tests
    ...    tunit determines which care team the patient will be under, by default it is Care Team 2
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${TEST_CLINIC_SETTING_2_SUB_ID_CARE_TEAM_1}
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Patient Education Clinic
    [Documentation]    There are several tcs that will need this keyword so placed it
    ...    Patients will be added under TA clinic Automated_tests
    ...    tunit determines which care team the patient will be under, by default it is Care Team 2
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${PATIENT_EDUCATION_SUB_ID_CARETEAM_2}
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${patient_education_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${patient_education_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Clinic Settings 3
    [Documentation]    Creates only 1 patient for the entire suite
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${TEST_CLINIC_SETTING_3_SUB_ID_CARE_TEAM_1}
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data In Suite Level    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token Suite Level
    ...    ${test_clinic_setting_3}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TEST_CLINIC_SETTING_3_CLINIC_ID}
    Add Patient To New Clinic Via API Suite Level
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Send Change User Password Request Suite Level
    Log    ${patient_email}

Add An Activated Patient Under Time Constraint Clinic
    [Arguments]
    ...    ${usecase}
    ...    ${subscriber_id}
    ...    ${mailosaur}=no
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${time_constraint_testing}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TIME_CONSTRAINT_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${time_constraint_testing}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TIME_CONSTRAINT_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Appointment Clinic
    [Documentation]    Adds new patient under TA clinic Appointment_Clinic
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}    # care team 2 by default
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${appointment_clinic}[user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${APPOINTMENT_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${appointment_clinic}[user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${APPOINTMENT_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Native App Clinic
    [Documentation]    Adds new patient under TA clinic Native App
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${NATIVE_APP_CLINIC_SUB_ID_CARETEAM_2}    # care team 2 by default
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${native_app_automated_tests}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${NATIVE_APP_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${native_app_automated_tests}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${NATIVE_APP_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Pre Treatment Clinic
    [Documentation]    Adds new patient under Pre Treatment Clinic
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${PRE_TREATMENT_SUB_ID_CARE_TEAM_1}
    ...    ${gender}=male
    ...    ${module}=${PRE_POST_TREATMENT}
    Generate Random Patient Data
    Generate Clinic Token
    ...    ${pre_treatment_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PRE_TREATMENT_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${pre_treatment_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PRE_TREATMENT_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add An Activated Patient Under Pendo Clinic
    [Documentation]    Adds new patient under Pendo Clinic
    [Arguments]
    ...    ${usecase}
    ...    ${mailosaur}=no
    ...    ${subscriber_id}=${PENDO_TEST_SUB_ID_CARETEAM_1}    # care team 2 by default
    ...    ${gender}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Generate Random Patient Data    mailosaur=${mailosaur}    name=${usecase}
    Generate Clinic Token
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PENDO_TEST_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    ...    module=${module}
    Generate Clinic Token
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PENDO_TEST_CLINIC_ID}
    Send Change User Password Request
    Log    ${patient_email}

Add A Patient Under 2FA Enabled Clinic To Activate
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    phone_number=${mailosaur_number}
    Generate Clinic Token
    ...    ${2fa_enabled_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${2FA_ENABLED_CLINIC_ID}
    Add Patient To New Clinic Via API
    ...    ${2FA_ENABLED_SUB_ID_CARE_TEAM_1}
    Log    ${patient_email}

Add A Not Activated Patient Via API
    [Arguments]    ${ehr_token}    ${tunit}    ${clinic_user}    ${clinic_id}
    Generate Random Patient Data
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Invite Patient Via API    ${ehr_token}    ${tunit}
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}

Format DOB
    ${dob}    FakerLibrary.Date_Of_Birth    maximum_age=100    minimum_age=10
    ${dob}    Convert To String    ${dob}
    ${year}    Get Substring    ${dob}    0    4
    ${month}    Get Substring    ${dob}    5    7
    ${day}    Get Substring    ${dob}    8    10
    Set Test Variable    ${dob}    ${year}-${month}-${day}

Generate Random Patient Data
    [Documentation]    Supply mailosaur server id if using mailosaur email
    [Arguments]    ${mailosaur}=no    ${phone_number}=default    ${name}=default
    ${patient_mrn}    Generate Random String    8
    Set Test Variable    ${patient_mrn}
    ${patient_ssn}    Generate Random String    8
    Set Test Variable    ${patient_ssn}
    ${hipaacode}    Generate Random String    8
    Set Test Variable    ${hipaacode}
    ${external_id}    Generate Random String    8
    Set Test Variable    ${external_id}
    ${family_name}    Generate Random String    8    [LOWER]
    Set Test Variable    ${family_name}
    IF    '${name}'=='default'
        ${first_name}    Generate Random String    8    [LOWER]
        Set Test Variable    ${first_name}
    ELSE
        Set Test Variable    ${first_name}    ${name}
    END
    IF    '${mailosaur}'!='no'
        Set Test Variable    ${patient_email}    ${first_name}.${family_name}@${mailosaur}.mailosaur.net
    ELSE
        Set Test Variable    ${patient_email}    ${first_name}.${family_name}@noona.fi
    END
    Format DOB
    IF    '${phone_number}'=='default'
        Set Test Variable    ${phone_number}    +3580000
    ELSE
        Set Test Variable    ${phone_number}
    END

Generate Random Patient Data In Suite Level
    [Documentation]    Supply mailosaur server id if using mailosaur email
    [Arguments]    ${mailosaur}=no    ${phone_number}=default    ${name}=default
    ${patient_mrn}    Generate Random String    8
    Set Suite Variable    ${patient_mrn}
    ${patient_ssn}    Generate Random String    8
    Set Suite Variable    ${patient_ssn}
    ${hipaacode}    Generate Random String    8
    Set Suite Variable    ${hipaacode}
    ${external_id}    Generate Random String    8
    Set Suite Variable    ${external_id}
    ${family_name}    Generate Random String    8    [LOWER]
    Set Suite Variable    ${family_name}
    IF    '${name}'=='default'
        ${first_name}    Generate Random String    8    [LOWER]
        Set Suite Variable    ${first_name}
    ELSE
        Set Suite Variable    ${first_name}    ${name}
    END
    IF    '${mailosaur}'!='no'
        Set Suite Variable    ${patient_email}    ${first_name}.${family_name}@${mailosaur}.mailosaur.net
    ELSE
        Set Suite Variable    ${patient_email}    ${first_name}.${family_name}@noona.fi
    END
    ${dob}    Get Current Date    result_format=%Y-%m-%d
    Set Suite Variable    ${dob}
    IF    '${phone_number}'=='default'
        Set Suite Variable    ${phone_number}    +3580000
    ELSE
        Set Suite Variable    ${phone_number}
    END

Invite Patient Via API
    [Documentation]    Generate Random Patient Data needs to be run first
    ...    NOTE: Only use this if you need patient with Invitation sent status
    [Arguments]    ${integration_user_token}    ${tunit}    ${module}=default    ${gender}=male
    Set Test Variable    ${integration_user_token}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientViaSMSInvite.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    hipaa_value    ${hipaacode}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    first_name_value    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${dob}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${phone_number}
    IF    '${module}'!='default'
        ${body_mod}    Replace String    ${body_mod}    cytostaticsGeneral    ${module}
    END
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Set Test Variable    ${first_name}
    Delete All Sessions
    RETURN    ${body_json}

Create An Activated Patient Via API
    [Documentation]    Run generate random patient data if random data is needed
    ...    Includes default values for module and gender
    [Arguments]    ${clinic_user}    ${clinic_id}    ${subscriber_id}    ${with_provider}=no
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Add Patient To New Clinic Via API    ${subscriber_id}    with_provider=${with_provider}
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Send Change User Password Request
    Log    ${patient_email}

Create Proxy Patient Via API
    [Documentation]    Generate Random Patient Data and Generate Clinic Token needs to be run first
    [Arguments]    ${subscriber_id}
#    Set Test Variable    ${integration_user_token}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createProxyPatientRequestBody.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${dob}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    subscriber_id    ${subscriber_id}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${phone_number}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${patient_id}    Get From Dictionary    ${response_json}    result
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Create Candidate Request
    [Arguments]    ${integration_user_token}    ${tunit}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${tunit}    Convert To String    ${tunit}
    ${body}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientAsCandidateWithCareProvider.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    Testy    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    Middle    ${EMPTY}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number    0000000
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Add Candidate Patient Via API
    [Arguments]    ${integration_user_token}    ${tunit}
    Generate Random Patient Data
    Create Candidate Request    ${integration_user_token}    ${tunit}

Add Proxy Patient Via API
    [Arguments]    ${ehr_token}    ${clinic_user}    ${clinic_id}    ${subscriber_id}
    Generate Random Patient Data
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Create Proxy Patient Via API    ${subscriber_id}

Decline Proxy Patient Via API
    [Documentation]    Get Patient UserID keyword should be executed prior to this to get the user_id and Get Login Token
    [Arguments]    ${patient_id}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}declineProxyRequestBody.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Add A Declined Patient Via API
    [Arguments]    ${ehr_token}    ${clinic_user}    ${clinic_id}    ${subscriber_id}
    Add Proxy Patient Via API    ${ehr_token}    ${clinic_user}    ${clinic_id}    ${subscriber_id}
    Decline Proxy Patient Via API    ${patient_id}

Lock Patient Via API
    [Documentation]    Get Patient UserID keyword should be executed prior to this to get the user_id and Get Login Token
    [Arguments]    ${patient_id}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}lockPatientRequestBody.json
    ${body_mod}    Replace String    ${body}    user_id    ${patient_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Add A Locked Patient Via API
    [Arguments]    ${ehr_token}    ${tunit}    ${clinic_user}    ${clinic_id}
    Add An Activated Patient Via API    ${ehr_token}    ${tunit}    ${clinic_user}    ${clinic_id}
    Get Patient UserID    ${patient_id}
    Lock Patient Via API    ${user_id}

Add Patient To New Clinic Via API
    [Documentation]    It is required to set as pre-requisite the content of the data=${body_mod} in a previous keyword and to generate the clinic token
    ...    Note: Patient's status should be Username sent at this point
    [Arguments]
    ...    ${subscriber_id}
    ...    ${gender_option}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    ...    ${with_provider}=no
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${login_token}
    Set Test Variable    &{header}
    IF    '${with_provider}'=='no'
        ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createPatientRequestBody.json
    ELSE
        ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createPatientWithProvider.json
        IF    '${ENVIRONMENT}' == 'staging'
            ${body}    Replace String    ${body}    provider_id    1ea7f0a6-018a-4a27-b171-c315b4116d4d
        ELSE
            ${body}    Replace String    ${body}    provider_id    4b7be7c3-2653-4803-ab1e-b5adf5365da4
        END
    END
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    subscriber_id    ${subscriber_id}
    ${body_mod}    Replace String    ${body_mod}    first_name_value    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${dob}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender_option}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${phone_number}
    ${body_mod}    Replace String    ${body_mod}    module_id    ${module}[id]
    ${body_mod}    Replace String    ${body_mod}    module_type    ${module}[code]
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${patient_id}    Get From Dictionary    ${response_json}    result
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Set Test Variable    ${patient_ssn}
    Delete All Sessions
    RETURN    ${body_json}

Invite New Patient Via API As Clinic User
    [Documentation]    patient is invited with username sent status
    [Arguments]    ${mailosaur}    ${clinic_user}    ${clinic_id}    ${subscriber_id}
    Generate Random Patient Data    mailosaur=${mailosaur}
    Generate Clinic Token
    ...    ${clinic_user}
    ...    ${DEFAULT_PASSWORD}
    ...    ${clinic_id}
    Add Patient To New Clinic Via API
    ...    ${subscriber_id}

Add Patient To New Clinic Via API Suite Level
    [Documentation]    It is required to set as pre-requisite the content of the data=${body_mod} in a previous keyword and to generate the clinic token
    ...    Note: Patient's status should be Username sent at this point
    [Arguments]
    ...    ${subscriber_id}
    ...    ${gender_option}=male
    ...    ${module}=${ABDOMINAL_RADIOTHERAPY}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${login_token}
    Set Suite Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createPatientRequestBody.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    subscriber_id    ${subscriber_id}
    ${body_mod}    Replace String    ${body_mod}    first_name_value    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${dob}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender_option}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${phone_number}
    ${body_mod}    Replace String    ${body_mod}    module_id    ${module}[id]
    ${body_mod}    Replace String    ${body_mod}    module_type    ${module}[code]
    Set Suite Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${patient_id}    Get From Dictionary    ${response_json}    result
    Set Suite Variable    ${patient_id}
    Set Suite Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Create Invited/Invited Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add A Not Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}

Create Activated/Activated Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}
    Send Change User Password Request

Create An Activated/Activated Patient On Multi-Clinic Via API With Mailosaur Email
    [Documentation]    Keyword was added for tcs like nms9-ver-136-3 where mailosaur email is needed
    ...                The patient's multiclinics are in this order; default clinic and appointment clinic
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Generate Random Patient Data     mailosaur=${mailosaur_keys}[0]
    Invite Patient Via API    ${ehr_token1}    ${t_unit1}
    Generate Clinic Token    ${clinic_user1}    ${DEFAULT_PASSWORD}    ${clinic_id1}
    Send Change User Password Request
    Log    ${patient_email}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}
    Send Change User Password Request

Create Activated/Invited Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}

Create Activated/Locked Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}
    Get Patient UserID    ${patient_id}
    Lock Patient Via API    ${user_id}

Create Locked/Locked Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add A Locked Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Add Patient To New Clinic Via API    ${subscriber_id2}
    Get Patient UserID    ${patient_id}
    Lock Patient Via API    ${user_id}

Create Activated/Proxy Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${ehr_token2}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id2}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Create Proxy Patient Via API    ${subscriber_id2}

Create Proxy/Proxy Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${ehr_token2}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id1}
    ...    ${subscriber_id2}
    Add Proxy Patient Via API    ${ehr_token1}    ${clinic_user1}    ${clinic_id1}    ${subscriber_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Create Proxy Patient Via API    ${subscriber_id2}

Create Activated/Candidate Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${ehr_token2}
    ...    ${t_unit1}
    ...    ${t_unit2}
    ...    ${clinic_user1}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Create Candidate Request    ${ehr_token2}    ${t_unit2}

Create Candidate/Candidate Patient On Multi-Clinic Via API
    [Arguments]    ${ehr_token1}    ${ehr_token2}    ${t_unit1}    ${t_unit2}
    Generate Random Patient Data
    Create Candidate Request    ${ehr_token1}    ${t_unit1}
    Create Candidate Request    ${ehr_token2}    ${t_unit2}

Create Activated/Declined Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${ehr_token2}
    ...    ${t_unit1}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id}
    Add An Activated Patient Via API    ${ehr_token1}    ${t_unit1}    ${clinic_user1}    ${clinic_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Create Proxy Patient Via API    ${subscriber_id}
    Decline Proxy Patient Via API    ${patient_id}

Create Declined/Declined Patient On Multi-Clinic Via API
    [Arguments]
    ...    ${ehr_token1}
    ...    ${ehr_token2}
    ...    ${clinic_user1}
    ...    ${clinic_user2}
    ...    ${clinic_id1}
    ...    ${clinic_id2}
    ...    ${subscriber_id1}
    ...    ${subscriber_id2}
    Add A Declined Patient Via API
    ...    ${ehr_token1}
    ...    ${clinic_user1}
    ...    ${clinic_id1}
    ...    ${subscriber_id1}
    Generate Clinic Token    ${clinic_user2}    ${DEFAULT_PASSWORD}    ${clinic_id2}
    Create Proxy Patient Via API    ${subscriber_id2}
    Decline Proxy Patient Via API    ${patient_id}

Get Patient Main Treatment Module Id
    [Arguments]    ${patient_id}    ${module_index}=0
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}getPatientUpdateModel.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_all_dictionary}    Evaluate    json.loads("""${response.content}""")    json
    ${response_all_dictionary}    Convert To Dictionary    ${response_all_dictionary}
    ${result_dict}    Get From Dictionary    ${response_all_dictionary}    result
    ${patientTreatmentModules}    Get From Dictionary    ${result_dict}    patientTreatmentModules
    ${patientTreatmentModules}    Convert To List    ${patientTreatmentModules}
    ${main_module}    Get From List    ${patientTreatmentModules}    ${module_index}
    ${main_module}    Convert To Dictionary    ${main_module}
    ${patient_treatment_module_id}    Get From Dictionary    ${main_module}    id
    ${patient_treatment_module_id}    Convert To String    ${patient_treatment_module_id}
    ${treatment_module}    Get From Dictionary    ${main_module}    treatmentModule
    ${treatment_module}    Convert To Dictionary    ${treatment_module}
    ${treatment_module_id}    Get From Dictionary    ${treatment_module}    id
    ${treatment_module_id}    Convert To String    ${treatment_module_id}
    ${symptom_types_list}    Get From Dictionary    ${treatment_module}    symptomTypes
    ${symptom_types_list}    Convert To List    ${symptom_types_list}
    ${treatment_module_type}    Get From Dictionary    ${treatment_module}    treatmentModuleType
    ${treatment_module_type}    Convert To String    ${treatment_module_type}
    ${subscriptions}    Get From Dictionary    ${main_module}    subscriptions
    ${subscriptions_list}    Convert To List    ${subscriptions}
    Set Test Variable    ${treatment_module}
    Set Test Variable    ${symptom_types_list}
    Set Test Variable    ${subscriptions_list}
    Set Test Variable    ${treatment_module_type}
    Set Test Variable    ${treatment_module_id}
    Set Test Variable    ${patient_treatment_module_id}
    Delete All Sessions

Send Symptom Questionnaire Via API
    [Arguments]    ${patient_id}    ${sending_date}    ${treatment_date}    ${symptom_questionnaire_type}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendQuestionnaireRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    symptomTypes    ${symptom_types_list}
    Set To Dictionary    ${body_dict["params"][0]["symptomInquiries"][0]}    symptomTypes    ${symptom_types_list}
    Set To Dictionary    ${body_dict["params"][0]}    subscriptions    ${subscriptions_list}
    Set To Dictionary    ${body_dict["params"][0]}    id    ${patient_treatment_module_id}
    Set To Dictionary
    ...    ${body_dict["params"][0]["treatmentModule"]}
    ...    treatmentModuleType
    ...    ${treatment_module_type}
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    id    ${treatment_module_id}
    Set To Dictionary    ${body_dict["params"][0]["symptomInquiries"][0]}    treatmentDate    ${treatment_date}
    Set To Dictionary    ${body_dict["params"][0]["symptomInquiries"][0]}    sendingDate    ${sending_date}
    # Sets questionnaire type
    Set To Dictionary
    ...    ${body_dict["params"][0]["symptomInquiries"][0]}
    ...    type
    ...    ${symptom_questionnaire_type}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File    ${EXECDIR}${/}resources${/}test_data${/}sendQuestionnaireRequestBody_updated.json    ${updated_body}
    ${body_updated}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendQuestionnaireRequestBody_updated.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Remove File    ${EXECDIR}${/}resources${/}test_data${/}sendQuestionnaireRequestBody_updated.json
    Delete All Sessions

Send Symptom Questionnaire Via Api To Patient
    [Documentation]    Keyword should be run after 'Add An Activated Patient Under Default Clinic' or "Add Patient To New Clinic Via API'
    ...    this keyword works for sending all five symptom ques to patients via api by passing the corresponsing argument
    ...    Argument options are;
    ...    'baseline' == Baseline Questionnaire
    ...    'treatmentVisit' == Treatment Visit Questionnaire
    ...    'clinicAppointment' == Clinic Appointment Questionnaire
    ...    'followUp' == Follow-up Questionnaire
    ...    'toPostTreatment' == Status Check Questionnaire
    ...    Please Note: all these values have been defined as variables above, so use please use that in your tc
    ...    Sent Questionnaire is immediately available for patient to answer
    ...    90s Sleep was added so that new msg notification arrives on the patient side
    [Arguments]    ${symptom_questionnaire_type}    ${review_date}=default    ${send_date}=default
    IF    '${review_date}'=='default' and '${send_date}'=='default'
        ${todays_date}    Get Current Date    result_format=%Y-%m-%d
        Set Test Variable    ${send_date}    ${todays_date}
        Set Test Variable    ${review_date}    ${todays_date}
    END
    Get Patient Main Treatment Module Id    ${patient_id}
    Send Symptom Questionnaire Via API
    ...    ${patient_id}
    ...    ${send_date}
    ...    ${review_date}
    ...    ${symptom_questionnaire_type}
    Sleep    90s

Send QOL 15D Questionnaire Via API
    [Arguments]    ${patient_id}    ${sending_date}    ${treatment_date}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    symptomTypes    ${symptom_types_list}
    Set To Dictionary    ${body_dict["params"][0]}    subscriptions    ${subscriptions_list}
    Set To Dictionary    ${body_dict["params"][0]}    id    ${patient_treatment_module_id}
    Set To Dictionary
    ...    ${body_dict["params"][0]["treatmentModule"]}
    ...    treatmentModuleType
    ...    ${treatment_module_type}
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    id    ${treatment_module_id}
    Set To Dictionary    ${body_dict["params"][0]["questionnaireInquiries"][0]}    treatmentDate    ${treatment_date}
    Set To Dictionary    ${body_dict["params"][0]["questionnaireInquiries"][0]}    sendingDate    ${sending_date}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File
    ...    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    ...    ${updated_body}
    ${body_updated}    Get File
    ...    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Remove File    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    Delete All Sessions

Send Quality Of Life Questionnaire Via API
    [Documentation]    Should accept any QOL questionnaire. Please add qol type (code) if using one not in the list.
    [Arguments]    ${patient_id}    ${sending_date}    ${treatment_date}    ${qol}
    ${qol_type}    Set Variable If    '${qol}'=='${QUALITY_OF_LIFE_QUESTIONNAIRE}'    qol15D
    ...    '${qol}'=='${TENONC_DISTRESS_SCREENING}'    tenoncDistressBaseline
    ...    '${qol}'=='${SUPPORT_AND_GUIDANCE}'    distressAndProblemList
    ...    '${qol}'=='${SUPPORT_AND_GUIDANCE_FOLLOW_UP}'    distressFollowUp
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    symptomTypes    ${symptom_types_list}
    Set To Dictionary    ${body_dict["params"][0]}    subscriptions    ${subscriptions_list}
    Set To Dictionary    ${body_dict["params"][0]}    id    ${patient_treatment_module_id}
    Set To Dictionary
    ...    ${body_dict["params"][0]["treatmentModule"]}
    ...    treatmentModuleType
    ...    ${treatment_module_type}
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    id    ${treatment_module_id}
    Set To Dictionary    ${body_dict["params"][0]["questionnaireInquiries"][0]}    treatmentDate    ${treatment_date}
    Set To Dictionary    ${body_dict["params"][0]["questionnaireInquiries"][0]}    sendingDate    ${sending_date}
    Set To Dictionary    ${body_dict["params"][0]["questionnaireInquiries"][0]}    type    ${qol_type}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File
    ...    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    ...    ${updated_body}
    ${body_updated}    Get File
    ...    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Remove File    ${EXECDIR}${/}resources${/}test_data${/}send-qoL15D-QuestionnaireRequestBody_updated.json
    Delete All Sessions

Send Scheduled Message Via API
    [Arguments]    ${patient_id}    ${sending_date}    ${topic}    ${message}
    ${today}    Get Current Date    exclude_millis=true
    ${today_converted}    Convert Date    ${today}    epoch
    ${today_converted}    Convert To Integer    ${today_converted}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendScheduledMessageRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    symptomTypes    ${symptom_types_list}
    Set To Dictionary    ${body_dict["params"][0]}    modified    ${today_converted}
    Set To Dictionary    ${body_dict["params"][0]}    created    ${today_converted}
    Set To Dictionary    ${body_dict["params"][0]}    subscriptions    ${subscriptions_list}
    Set To Dictionary    ${body_dict["params"][0]}    id    ${patient_treatment_module_id}
    Set To Dictionary
    ...    ${body_dict["params"][0]["treatmentModule"]}
    ...    treatmentModuleType
    ...    ${treatment_module_type}
    Set To Dictionary    ${body_dict["params"][0]["treatmentModule"]}    id    ${treatment_module_id}
    Set To Dictionary    ${body_dict["params"][0]["scheduledMessages"][0]}    sendingDate    ${sending_date}
    Set To Dictionary    ${body_dict["params"][0]["scheduledMessages"][0]}    title    ${topic}
    Set To Dictionary    ${body_dict["params"][0]["scheduledMessages"][0]}    body    ${message}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File
    ...    ${EXECDIR}${/}resources${/}test_data${/}sendScheduledMessageRequestBody.json
    ...    ${updated_body}
    ${body_updated}    Get File
    ...    ${EXECDIR}${/}resources${/}test_data${/}sendScheduledMessageRequestBody.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Send QOL 15D Questionnaire Via API To Patient For Today
    [Documentation]    Keyword should be run after 'Add An Activated Patient Under Default Clinic' or "Add Patient To New Clinic Via API'
    ...    Sent QOL 15D Questionnaire is immediately available for patient to answer
    ...    180s Sleep was added so that new msg notification arrives on the patient side
    ${todays_date}    Get Current Date    result_format=%Y-%m-%d
    Set Variable    ${todays_date}
    Get Patient Main Treatment Module Id    ${patient_id}
    Send QOL 15D Questionnaire Via API    ${patient_id}    ${todays_date}    ${todays_date}
    Sleep    180s    # necessary to wait this long for questionnaire to arrive at patient portal

Send QOL Questionnaire Via API To Patient For Today
    [Documentation]
    [Arguments]    ${qol_type}
    ${todays_date}    Get Current Date    result_format=%Y-%m-%d
    Set Variable    ${todays_date}
    Get Patient Main Treatment Module Id    ${patient_id}
    Send Quality Of Life Questionnaire Via API    ${patient_id}    ${todays_date}    ${todays_date}    ${qol_type}
    Sleep    180s    # necessary to wait this long for questionnaire to arrive at patient portal

Send Scheduled Message To Patient Via Api
    [Arguments]    ${title}    ${body}
    ${todays_date}    Get Current Date    result_format=%Y-%m-%d
    ${today}    Get Current Date    exclude_millis=true
    ${today}    Convert Date    ${today}    epoch
    ${today}    Convert To Integer    ${today}
    ${today}    Convert To String    ${today}
    Set Variable    ${todays_date}
    Get Patient Main Treatment Module Id    ${patient_id}
    Send Scheduled Message Via API    ${patient_id}    ${todays_date}    ${title}    ${body}
    Sleep    120s    # necessary to wait this long for questionnaire to arrive at patient portal

Get First Patient Case via API
    [Documentation]    Keywords should be used in combination with Add Patient Via API where nurse token is generated
    ...    This has 5mins validity (should be sufficient for a test case)
    [Arguments]    ${patient_id}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}getPatientRequests.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${patient_id}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    Log    ${response_json}
    Set Test Variable    ${first_case_id}    ${response_json["result"]["cases"][0]["id"]}

Send Message Connected To First Case Via API
    [Documentation]    Keywords should be used in combination with Get First Patient Case via API;
    ...    topicTypes: 18 - "Question", 11 - "Instructions", 9 - "Invitation for physical examination",
    ...    10 - "Invitation for clinical examination", 38 - "Patient education", 12 - "Follow-up instruction"
    [Arguments]    ${first_case_id}    ${code_type}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${random_text}    Generate Random String    25    [LETTERS][NUMBERS]
    Set Test Variable    ${random_text}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createCaseMessage.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    4    ${code_type}
    Set List Value    ${body_dict["params"]}    0    ${first_case_id}
    Set List Value    ${body_dict["params"]}    2    ${random_text}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/casemessages    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Open Follow Up To First Case Via API
    [Documentation]    Keywords should be used in combination with Get First Patient Case via API
    [Arguments]    ${first_case_id}    ${follow_up_days}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${now}    Get Current Date
    ${date_today}    Convert Date    ${now}    result_format=%Y-%m-%d
    ${follow_up_date}    Add Time To Date    ${now}    ${follow_up_days} days    result_format=%Y-%m-%d
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}openFollowUpRequest.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${first_case_id}
    Set List Value    ${body_dict["params"]}    1    ${date_today}
    Set List Value    ${body_dict["params"]}    2    ${follow_up_date}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/casemessages    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Send Patient Education With Document To First Case Via API
    [Documentation]    Keywords should be used in combination with Get First Patient Case via API.
    ...    This sends PE with Brain Tumors and Headaches document. Please update if needed.
    [Arguments]    ${first_case_id}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${random_text}    Generate Random String    25    [LETTERS][NUMBERS]
    Set Test Variable    ${random_text}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createCaseMessageWithDocs.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${first_case_id}
    Set List Value    ${body_dict["params"]}    2    ${random_text}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/casemessages    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Close first case Via API
    [Documentation]    Keyword should be used with valid clinic token generated previously
    [Arguments]    ${first_case_id}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}closePatientCase.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${first_case_id}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/case    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Patient Is Invited To Use Noona
    [Arguments]    ${clinic_user}    ${clinic_id}    ${sub_id}
    Generate Clinic Token    ${clinic_user}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Add Patient To New Clinic Via API    ${sub_id}

Create Patient With Different Statuses
    [Arguments]    ${status}    ${clinic}    ${clinic_manager}    ${clinic_id}    ${sub_id}    ${clinic_ehr_token}    ${second_clinic}=no
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    IF    '${status}'=='activated'
        Patient Is Invited To Use Noona    ${clinic_manager}    ${clinic_id}    ${sub_id}
        Generate Clinic Token    ${clinic_manager}    ${DEFAULT_PASSWORD}    ${clinic_id}
        Send Change User Password Request
        IF    '${second_clinic}'=='no'
            Check If Email Notification About Activation Is Sent    sent    ${clinic}
        END
    ELSE IF    '${status}'=='locked'
        Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
        Generate Clinic Token    ${clinic_manager}    ${DEFAULT_PASSWORD}    ${clinic_id}
        Add Patient To New Clinic Via API    ${sub_id}
        ${email_subject}    Get Email Subject    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
        Should Be Equal    ${email_subject}    ${clinic} invites you to use Noona
        Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
        Send Change User Password Request
        Log    ${patient_email}
        Get Patient UserID    ${patient_id}
        Lock Patient Via API    ${user_id}
    ELSE IF    '${status}'=='unactivated'
        Patient Is Invited To Use Noona    ${clinic_manager}    ${clinic_id}    ${sub_id}
    ELSE IF    '${status}'=='proxy'
        Generate Clinic Token    ${clinic_manager}    ${DEFAULT_PASSWORD}    ${clinic_id}
        Create Proxy Patient Via API    ${sub_id}
    ELSE IF    '${status}'=='declined'
        Generate Clinic Token    ${clinic_manager}    ${DEFAULT_PASSWORD}    ${clinic_id}
        Create Proxy Patient Via API    ${sub_id}
        Decline Proxy Patient Via API    ${patient_id}
    ELSE IF    '${status}'=='candidate'
        Create Candidate Request    ${clinic_ehr_token}    f07p08 unit
    END

Consult A Care Team Via API
    [Arguments]    ${case_id}    ${consult_message}    ${care_team_sub_id}    ${clinic_user_sub_id}=none
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}consultCaseRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${case_id}
    Set List Value    ${body_dict["params"]}    1    ${consult_message}
    Set List Value    ${body_dict["params"]}    2    ${care_team_sub_id}
    IF    '${clinic_user_sub_id}'!='none'
        Set List Value    ${body_dict["params"]}    3    ${clinic_user_sub_id}
    END
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/case    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Reply To Consulation Via API
    [Arguments]    ${case_id}    ${reply_message}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}replyToCaseConsultationRequestBody.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    Set List Value    ${body_dict["params"]}    0    ${case_id}
    Set List Value    ${body_dict["params"]}    1    ${reply_message}
    ${body_dict}    Evaluate    json.dumps(${body_dict})    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic/case    data=${body_dict}    headers=${header}
    Should Contain    '${response.status_code}'    '200'

Add Treatment Module Via API
    [Arguments]     ${care_team_sub_id}    ${module}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    ${today}    Convert Date Time To Epoch
    Set To Dictionary    ${body_dict["params"][0]}    startDate    ${today}
    Set To Dictionary    ${body_dict["params"][0]["subscriptions"][0]}    subscriberId    ${care_team_sub_id}
    Set List Value    ${body_dict["params"]}    1    ${patient_id}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File
    ...    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    ...    ${updated_body}
    ${body_updated}    Get File
    ...    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Remove File    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    Delete All Sessions

Close Treatment Module Via API
    [Arguments]    ${care_team_sub_id}    ${module}
    &{header}    Create Dictionary    Content-Type=application/json    x-login-token=${login_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}.json
    ${body_dict}    Evaluate    json.loads("""${body}""")    json
    ${today}    Get Current Date
    ${startdate}    Convert Date Time To Epoch    date=${today}
    ${created}    Add Time To Date    ${today}    5 seconds
    ${created}    Convert Date Time To Epoch    date=${created}
    ${modified}    Add Time To Date    ${today}    10 seconds
    ${modified}    Convert Date Time To Epoch    date=${modified}
    ${enddate}    Add Time To Date    ${today}    2 seconds    result_format=%Y-%m-%dT%H:%M:%SZ
    Set To Dictionary    ${body_dict["params"][0]}    id    ${patient_treatment_module_id}
    Set To Dictionary    ${body_dict["params"][0]}    startDate    ${startdate}
    Set To Dictionary    ${body_dict["params"][0]}    modified    ${modified}
    Set To Dictionary    ${body_dict["params"][0]}    created    ${created}
    Set To Dictionary    ${body_dict["params"][0]["subscriptions"][0]}    subscriberId    ${care_team_sub_id}
    Set To Dictionary    ${body_dict["params"][0]["subscriptions"][0]}    patientId    ${patient_id}
    Set To Dictionary    ${body_dict["params"][0]}    endDate    ${end_date}
    ${updated_body}    Evaluate    json.dumps(${body_dict},indent=2)    json
    Set Global Variable    ${updated_body}
    Create File
    ...    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    ...    ${updated_body}
    ${body_updated}    Get File
    ...    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    ${body_json}    Evaluate    json.loads("""${body_updated}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    data=${body_updated}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Remove File    ${EXECDIR}${/}resources${/}test_data${/}module_actions${/}${module}_updated.json
    Delete All Sessions

Send New Login Link To Patient Via API
    [Arguments]    ${clinic_user_email}    ${clinic_id}
    Generate Clinic Token
    ...    ${clinic_user_email}
    ...    ${DEFAULT_PASSWORD}
    ...    ${clinic_id}
    Get Patient UserID    ${patient_id}    #should be extracted from creating patient step
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}sendNewPasswordRequestBody.json
    ${body_mod}    Replace String    ${body}    user_id    ${user_id}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Ask About Other Issues As Patient Via API
    [Documentation]    Add the rest of topic types if needed
    [Arguments]    ${title}    ${content}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}addMessageRequestbody.json
    ${today}    Get Current Date
    Set Test Variable    ${today}
    ${message_date}    Convert Date    ${today}    result_format=%Y-%m-%dT%H:%M:%SZ
    ${topic_type}    Set Variable If
    ...    '${title}'=='Medication information'    2
    ...    '${title}'=='Physical therapy and aids'    3
    ...    '${title}'=='Information about side effects'    4
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    other_issues_title    ${title}
    ${body_mod}    Replace String    ${body_mod}    other_issues_content    ${content} today ${today}
    ${body_mod}    Replace String    ${body_mod}    topic_type    ${topic_type}
    ${body_mod}    Replace String    ${body_mod}    message_date    ${message_date}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient/    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    Delete All Sessions

Add Delegate User Via API And Activate
    [Documentation]    family_name comes from add patient via api kw. Mailosaur keys should be set before this kw.
    [Arguments]    ${patient_email}    ${clinic}    ${clinic_id}    ${delegate_fname}    ${delegate_lname}
    ${delegate_email}    Set Variable    ${family_name}.delegate@${mailosaur_keys}[0].mailosaur.net
    Set Test Variable    ${delegate_email}
    Login As Patient Via API    ${patient_email}    ${clinic_id}
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}addDelegateUserRequestBody.json
    ${body}    Replace String    ${body}    delegate_first_name    ${delegate_fname}
    ${body}    Replace String    ${body}    delegate_last_name    ${delegate_lname}
    ${body}    Replace String    ${body}    delegate_email    ${delegate_email}
    ${body_json}    Evaluate    json.loads("""${body}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient/patientdelegate    headers=${header}    data=${body}
    Should Contain    '${response.status_code}'    '200'
    ${body}    Evaluate    json.loads('''${response.content}''')    json
    ${delegate_user_id}    Get Value From Json     ${body}     $..id
    ${delegate_user_id}    Get From List    ${delegate_user_id}    1
    Set Test Variable    ${delegate_user_id}
    Delete All Sessions
    @{message_data}    Patient Received An Email About A New Message
    ...    ${delegate_email}
    ...    ${clinic} invites you to use Noona
    ...    ${first_name}${SPACE}${family_name} wants to share their medical records with you.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Activate Delegate User Account   ${message_data}[2]    ${delegate_email}
    Close Browser

Activate Delegate User Via API
    &{header}    Create Dictionary    Content-Type=application/json-rpc    x-request-method=changePassword
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}changeDelegateUserPassword.json
    ${body_json}    Evaluate    json.loads("""${body}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient    headers=${header}    data=${body}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions
