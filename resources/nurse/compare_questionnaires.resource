*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource    ${EXECDIR}${/}resources${/}questionnaires${/}tenonc_medication_adherence_assessment.resource
Library     Screenshot


*** Variables ***
@{patient_list}
...                                             ***********
...                                             111281-876X
...                                             ***********
...                                             120169-381M
...                                             290639-714M
...                                             040376-770E
...                                             010101-122M
${save_questionnaire_button}                    //*[contains(text(),'Save')]
${questionnaire_next_button}                    //*[contains(text(),'Next')]
${questionnaire_cancel_button}                  //*[contains(text(),'Cancel')]/..
${ok_button}                                    //*[@id="ok-confirm"]
${radio_buttons_clinic}                         //*[@id="wizard"]/div/div/nh-form-generator/nh-wizard-section//nh-radio-list//div[1]/label
${checkboxes}                                   //*[@id="wizard"]/div/div/nh-form-generator/nh-wizard-section//nh-checkbox-list//div[1]/label
${text_area}                                    //div//textarea
${number_field}                                 //input[contains(@type, "number")]
${summary_content}                              //div[contains(@class, "questionnaire-summary-content")]
@{text_list}                                    ${EMPTY}
@{number_list}                                  ${EMPTY}
${sexual_activity_question_enabled}             ${FALSE}
${symptom_select_yes}                           //div[contains(text(),"{}")]/../../descendant::label[2]
${symptom_questionnaire_next_button}            questionnaire-button-next
${changes_in_gen_state_of_health_option_1}      //h1[contains(text(),"Changes in general state of health")]/../../following-sibling::div/descendant::label[1]
${info_entered_by_caregiver}                    //input[@id='next-of-kin']/..
${symptom_radio_buttons}                        //h1[contains(text(),"{}")]/../../following-sibling::div/child::nh-radio-list/div[1]/descendant::label[1]
${symptom_today_radio_buttons}                  //h1[contains(text(),"{}")]/../../following-sibling::div/child::nh-date-range-item/descendant::label[1]
${qol_summary_container}                        //div[@class='symptom-summary-content']
${qol_summary_text}                             (//p[contains(@class, "section-description")])[2]
${info_entered_by_caregiver_checkbox}           //label[@for='next-of-kin']
${questionnaire_row_status}                     (//div[@class='module-group'][1]//td[text()="{}"]/following-sibling::td[3])[last()]
${complete_questionnaire_btn}                   (//*[@data-testid='open-extra-action'])[last()]
${qol_radio_button}                             //input[@type="radio"]/following-sibling::label


*** Keywords ***
Verify Questionnaires Are Equal
    [Arguments]    ${questionnaire}    ${tick_checkboxes}=${FALSE}    ${select_radio_button}=${FALSE}
    Log    ${PREV_TEST_STATUS}
    IF    '${PREV_TEST_NAME}'=='${EMPTY}'
        Generate Random Patient Data In Suite Level
        Create New Patient For Questionnaires Test    ${QUESTIONNAIRES_TEST_SUB_ID_COMPARE}
        Set Suite Variable    ${login_token}    ${login_token}
        Set Suite Variable    ${patient_id}    ${patient_id}
    END
    Login As Nurse    email=${questionnaires_test_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    ${questionnaire}
    Navigate To Questionnaires Tab
    Try To Click Element    ${complete_questionnaire_btn}
    Expand Questionnaire Sub-questions And Sub-options    ${questionnaire}
    Compare Questionnaires
    ...    nurse
    ...    ${patient_ssn}
    ...    ${questionnaire}
    ...    ${tick_checkboxes}
    ...    ${select_radio_button}
    Close Browser

Expand Questionnaire Sub-questions And Sub-options
    [Arguments]    ${questionnaire}
    IF    'EQ-5D' in '${questionnaire}'
       Expand Multiple-section Questionnaires' Questions
    ELSE IF    'Tennessee Oncology Medication Adherence Assessment' in '${questionnaire}'
       Expand All Questions For TenOnc Medication Adherence Assessment Questionnaire
    END

Create New Patient For Questionnaires Test
    [Arguments]    ${subscriber_id}    ${gender}=random
    IF    '${gender}'=='random'
        ${gender_list}    Create List    male    female
        ${gender}    Evaluate    random.choice(${gender_list})    random
    ELSE
        ${gender}    Set Variable    ${gender}
    END
    Generate Clinic Token Suite Level
    ...    ${questionnaires_test_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${QUESTIONNAIRES_TEST_CLINIC_ID}
    Add Patient To New Clinic Via API Suite Level
    ...    ${subscriber_id}
    ...    gender_option=${gender}
    Send Change User Password Request Suite Level
    Log    ${patient_email}

Remove Patient As Compare Questionnaire Teardown
    [Documentation]    To make sure tc still passes even if removal of patient failed as it is not included in the test.
    Run Keyword And Warn On Failure    Remove Patient For Compare Questionnaires Test

Remove Patient For Compare Questionnaires Test
    Login As Patient Via API    ${patient_email}    ${QUESTIONNAIRES_TEST_CLINIC_ID}    level=suite
    Send Delete Patient Request    level=suite
    Set Suite Variable    ${login_token}    ${QUESTIONNAIRES_TEST_CLINIC_TOKEN}
    Login As Nurse Via API
    ...    ${questionnaires_test_clinic}[manager_email]
    ...    ${QUESTIONNAIRES_TEST_CLINIC_ID}
    ...    level=suite
    Remove Patient Via API    ${patient_id}    ${patient_user_id}    level=suite

Expand Multiple-section Questionnaires' Questions
    [Documentation]    Used for questionnaires which have multiple sections. Each section is expanded by clicking "Next"-btn. For instance: groups of questionnairs with initials EQ-5D or EORTC QLQ - BLM30
    Wait Until Element Is Visible    ${questionnaire_next_button}
    Sleep    3
    Try To Click Element    ${questionnaire_next_button}
    Wait Until Element Is Visible    (${qol_radio_button} )[last()]
    ${count}    Get Element Count    ${questionnaire_next_button}
    ${before_count}    Set Variable    ${count}
    ${after_count}    Set Variable    0
    WHILE    ${count} > 1 and ${before_count}!=${after_count}    limit=8
        ${before_count}    Get Element Count    ${questionnaire_next_button}
        Try To Click Element    (${qol_radio_button} )[last()]
        Try To Click Element    (${questionnaire_next_button})[last()]
        ${after_count}    Get Element Count    ${questionnaire_next_button}
    END

Expand Question Or Option
    [Arguments]    ${question_title}    ${element}
    Wait Until Page Contains    ${question_title}
    Wait Until Page Contains Element    ${element}
    Try To Click Element    ${element}

Input Texts To Text Areas
    [Arguments]    ${list_of_textarea_ids}
    FOR    ${item}    IN    @{list_of_textarea_ids}
        Try To Input Text    //textarea[@id="${item}"]    randome texts
    END

Cancel Questionnaire
    Wait Until Page Contains Element    ${questionnaire_cancel_button}
    Try To Click Element    ${questionnaire_cancel_button}
    Wait Until Page Contains Element    ${ok_button}
    Try To Click Element    ${ok_button}

Compare Questionnaires
    [Arguments]
    ...    ${user}
    ...    ${patient_id}
    ...    ${questionnaire}
    ...    ${tick_checkboxes}
    ...    ${select_radio_button}
    ...    ${navigate}=${FALSE}
    @{csv_data}    CsvLibrary.get_column_values    ${EXECDIR}${/}data${/}questionnaires${/}${questionnaire}.csv
    # TODO translated_title is not needed / used at the moment.
    ${translated_title}    Get From List    ${csv_data}    0
    ${clinic_translated_title}    CsvLibrary.get_clinic_title
    ...    ${EXECDIR}${/}data${/}questionnaires${/}${questionnaire}.csv
    @{ui_data}    Nurse.QuestionnaireInquiry.Get Questionnaire
    ...    ${user}
    ...    ${patient_id}
    ...    ${clinic_translated_title}
    ...    ${tick_checkboxes}
    ...    ${select_radio_button}
    ...    ${navigate}
    Log    ${ui_data}
    Log    ${csv_data}
    Remove Values From List    ${csv_data}    ${EMPTY}
    List Should Contain Sub List    ${ui_data}    ${csv_data}

Create Schedule
    [Arguments]    ${patient_id}    ${questionnaire}
    Nurse.Questionnaires.Add Schedule    ${patient_id}    ${questionnaire}
    Nurse.Questionnaires.Wait For Schedule To Be Sent

Login To Nurse
    Login.Login To Nurse    ${automated_tests_clinic}[default_user]    ${DEFAULT_PASSWORD}    clinic_name=${automated_tests_clinic}[name]
    ${patient_id}    Get From Dictionary    ${CLINIC_PATIENT}    ssn
    Set Suite Variable    ${PATIENT_ID}    ${patient_id}

Complete Questionnaire
    [Arguments]    ${questionnaire_type}
    ${complete_button}    Set Variable
    ...    (//td[text()="${questionnaire_type}"]/following-sibling::td[4]//button[1])[last()]
    Scroll Element Into View    (//td[text()="${questionnaire_type}"])[last()]
    Wait Until Page Contains Element    ${complete_button}    60s
    Try To Click Element    ${complete_button}
    Wait Until Page Contains Element    ${questionnaire_next_button}
    IF    '${questionnaire_type}'=='FACT-Lym (Version 4)' and ${sexual_activity_question_enabled}
        Enable Sexual Activity Question
    END
    compare_questionnaires.Check And Select Radio Buttons
    compare_questionnaires.Check And Write To Text Area
    compare_questionnaires.Check And Write To Number Field
    compare_questionnaires.Check And Tick Checkboxes
    SeleniumLibrary.Click Element    ${questionnaire_next_button}
    compare_questionnaires.Check Summary

Complete Symptom Questionnaire
    [Arguments]    ${questionnaire_type}    ${symptom}    ${next_of_kin}=no
    ${complete_button}    Set Variable
    ...    (//td[text()="${questionnaire_type}"]/following-sibling::td[4]//button[1])[last()]
    Wait Until Page Contains Element    xpath=${complete_button}    60s
    Scroll Element Into View    (//td[text()="${questionnaire_type}"])[last()]
    Try To Click Element    ${complete_button}
    Wait Until Page Contains Element    ${questionnaire_next_button}
    compare_questionnaires.Select Symptom From List    ${symptom}
    Complete Changes In General State Of Health
    compare_questionnaires.Check And Write To Text Area
    compare_questionnaires.Check And Select Symptom Date Today    ${symptom}
    compare_questionnaires.Check And Select Symptom Radio Buttons    ${symptom}
    Click Element    ${symptom_questionnaire_next_button}
    IF    '${next_of_kin}'=='yes'
        Click Element    ${info_entered_by_caregiver}
    END
    compare_questionnaires.Check Summary

Select Symptom From List
    # 'No' is selected for each symptom by default. This keyword only allows one symptom selection.
    [Arguments]    ${symptom}
    ${element}    Format String    ${symptom_select_yes}    ${symptom}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}
    Try To Click Element    ${symptom_questionnaire_next_button}

Complete Changes In General State Of Health
    Click Element    ${changes_in_gen_state_of_health_option_1}
    Click Element    ${symptom_questionnaire_next_button}
    # TODO: Distress slider
    Click Element    ${symptom_questionnaire_next_button}
    # TODO: Weight
    Click Element    ${symptom_questionnaire_next_button}

Save Symptom Questionnaire
    [Arguments]    ${questionnaire_type}
    Wait Until Page Contains Element    ${save_questionnaire_button}
    Wait Until Element Is Enabled    ${save_questionnaire_button}
    SeleniumLibrary.Click Element    ${save_questionnaire_button}
    Wait Until Page Contains Element    ${ok_button}
    Page Should Contain    The questionnaire has been saved on behalf of the patient
    Sleep    1s
    Wait Until Element Is Visible    ${ok_button}
    SeleniumLibrary.Click Element    ${ok_button}

Save Questionnaire And Click Ok To Confirm
    Wait Until Page Contains Element    ${save_questionnaire_button}
    Wait Until Element Is Enabled    ${save_questionnaire_button}
    Click Element    ${save_questionnaire_button}
    Wait Until Page Contains Element    ${ok_button}
    Page Should Contain    The questionnaire has been saved on behalf of the patient
    Sleep    1s
    Wait Until Element Is Visible    ${ok_button}
    Click Element    ${ok_button}

Save Questionnaire
    [Arguments]    ${questionnaire_type}
    Save Questionnaire And Click Ok To Confirm
    ${questionnaire_status}    Format String    ${questionnaire_row_status}    ${questionnaire_type}
    Wait Until Page Contains Element    ${questionnaire_status}
    IF    '${questionnaire_type}'=='Tennessee Oncology Distress Questionnaire'
        Verify Tnonc Distress Questionnaire Status
    ELSE
        Wait Until Element Contains    ${questionnaire_status}    COMPLETED BY THE CLINIC
    END

Verify Tnonc Distress Questionnaire Status
    [Documentation]    Rate is set as test variable only for tnonc distress screening questionnaire
    IF    ${rate}<8
        Wait Until Element Contains    ${questionnaire_status}    COMPLETED AND REVIEWED BY THE CLINIC
    END

Enable Sexual Activity Question
    ${disable_extra_question}    Get Web Element
    ...    //label[contains(@for, "factLymVerfourModForm-fact-mari-conf-false")]
    ${enable_extra_question}    Get Web Element    //label[contains(@for, "factLymVerfourModForm-fact-mari-conf-true")]
    Wait Until Page Contains Element    ${enable_extra_question}    20s
    Wait Until Keyword Succeeds    20s    1s    SeleniumLibrary.Click Element    ${enable_extra_question}
    Set Test Variable    ${enable_extra_question}
    Set Test Variable    ${disable_extra_question}

Check And Write To Text Area
    ${text_area_exists}    Run Keyword And Return Status    Page Should Contain Element    ${text_area}
    IF    ${text_area_exists}==True    compare_questionnaires.Write To Text Area

Check And Write To Number Field
    ${number_field_exists}    Run Keyword And Return Status    Page Should Contain Element    ${number_field}
    IF    ${number_field_exists}==True    Write To Number Field

Check And Tick Checkboxes
    ${checkboxes_exists}    Run Keyword And Return Status    Page Should Contain Element    ${checkboxes}
    IF    ${checkboxes_exists}==True    Tick Checkboxes

Check And Select Radio Buttons
    ${radio_buttons_exists}    Run Keyword And Return Status    Page Should Contain Element    ${radio_buttons_clinic}
    IF    ${radio_buttons_exists}==True
        compare_questionnaires.Select Radio Buttons
    END

Check And Select Symptom Radio Buttons
    [Arguments]    ${symptom}
    ${symptom_radio_buttons}    Format String    ${symptom_radio_buttons}    ${symptom}
    ${symptom_radio_buttons_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${symptom_radio_buttons}
    IF    ${symptom_radio_buttons_exists}
        compare_questionnaires.Select Symptom Radio Buttons    ${symptom}
    END

Check And Select Symptom Date Today
    [Arguments]    ${symptom}
    ${symptom_today_radio_buttons}    Format String    ${symptom_today_radio_buttons}    ${symptom}
    ${symptom_radio_buttons_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${symptom_today_radio_buttons}
    IF    ${symptom_radio_buttons_exists}
        Wait Until Element Is Visible    ${symptom_today_radio_buttons}
        Click Element    ${symptom_today_radio_buttons}
    END

Select Radio Buttons
    Wait Until Page Contains Element    ${radio_buttons_clinic}    20s
    @{radio_buttons}    Get WebElements    ${radio_buttons_clinic}
    IF    ${sexual_activity_question_enabled}
        ${index}    Get Index From List    ${radio_buttons}    ${disable_extra_question}
    ELSE
        ${index}    Set Variable    -1
    END
    IF    ${index}>=0 and ${sexual_activity_question_enabled}
        Set List Value    ${radio_buttons}    ${index}    ${enable_extra_question}
    END
    FOR    ${radio_button}    IN    @{radio_buttons}
        Wait Until Keyword Succeeds    20s    1s    SeleniumLibrary.Click Element    ${radio_button}
    END

Select Symptom Radio Buttons
    [Arguments]    ${symptom}
    ${symptom_radio_buttons}    Format String    ${symptom_radio_buttons}    ${symptom}
    Wait Until Page Contains Element    ${symptom_radio_buttons}    20s
    @{radio_buttons}    Get WebElements    ${symptom_radio_buttons}
    IF    ${sexual_activity_question_enabled}
        ${index}    Get Index From List    ${symptom_radio_buttons}    ${disable_extra_question}
    ELSE
        ${index}    Set Variable    -1
    END
    IF    ${index}>=0 and ${sexual_activity_question_enabled}
        Set List Value    ${radio_buttons}    ${index}    ${enable_extra_question}
    END
    FOR    ${radio_button}    IN    @{radio_buttons}
        Wait Until Keyword Succeeds    20s    1s    SeleniumLibrary.Click Element    ${radio_button}
    END

Tick Checkboxes
    Wait Until Page Contains Element    ${checkboxes}    20s
    @{boxes}    Get WebElements    ${checkboxes}
    FOR    ${box}    IN    @{boxes}
        Wait Until Keyword Succeeds    20s    1    SeleniumLibrary.Click Element    ${box}
    END

Write To Text Area
    Wait Until Page Contains Element    ${text_area}    20s
    @{text_list}    Create List
    @{text_areas}    Get WebElements    ${text_area}
    FOR    ${area}    IN    @{text_areas}
        ${random_madness}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
        Wait Until Keyword Succeeds    20s    1s    Input Text    ${area}    ${random_madness}
        Append To List    ${text_list}    ${random_madness}
    END
    Set Test Variable    @{text_list}

Write To Number Field
    Wait Until Page Contains Element    ${number_field}    20s
    @{number_list}    Create List
    @{number_fields}    Get WebElements    ${number_field}
    FOR    ${field}    IN    @{number_fields}
        ${random_madness}    Generate Random String    2    [NUMBERS]
        ${random_madness}    Strip String    ${random_madness}    mode=left    characters=0
        ${attr}    Get Element Attribute    ${field}    id
        IF    '${attr}'=='dob'
            Input Text    dob    1980
            Append To List    ${number_list}    1980
        ELSE
            Wait Until Keyword Succeeds    20s    1s    Input Text    ${field}    ${random_madness}
            Append To List    ${number_list}    ${random_madness}
        END
    END
    Set Test Variable    @{number_list}

Check Summary
    Wait Until Page Contains Element    ${summary_content}    20s
    FOR    ${number}    IN    @{number_list}
        Element Should Contain    ${summary_content}    ${number}
    END
    FOR    ${text}    IN    @{text_list}
        Element Should Contain    ${summary_content}    ${text}
    END

Click Questionnaire Next Button
    SeleniumLibrary.Click Element    ${questionnaire_next_button}

Save Questionnaire Answered For Patient
    Wait Until Page Contains Element    ${save_questionnaire_button}
    Wait Until Element Is Enabled    ${save_questionnaire_button}
    Click Element    ${save_questionnaire_button}
    Wait Until Page Contains    Questionnaire saved

Click Clinic Staff Login
    Wait Until Element Is Visible    ${ok_button}
    Click Element    ${ok_button}

Rate With Vertical Slider
    [Documentation]    Status check questionnaire takes the non-default pixels
    [Arguments]    ${rating}=random    ${questionnaire}=default
    Wait Until Element Is Visible    ${vertical_slider_thumb}
    IF    'native' not in '${ENVIRONMENT}'
        IF    '${rating}'=='random'    Random Slider Rating
        IF    '${questionnaire}'=='default'
            @{pixels}    Create List    140    112    84    56    28    0    -28
            ...    -56    -84    -112    -140
        ELSE
            @{pixels}    Create List    148    140    112    84    56    0    -56
            ...    -84    -112    -140    -148
        END
        Sleep    1
        FOR    ${INDEX}    IN RANGE    0    5
            Sleep    1
            ${rate}    Get Text    ${vertical_slider_thumb}
            Set Test Variable    ${rate}
            IF    '${rating}'=='random' and '${rate}'=='?'
                Drag And Drop By Offset    ${vertical_slider_thumb}    0    ${pixels}[${rating}]
            ELSE IF    '${rating}'=='random' and '${rate}'!='?'
                BREAK
            ELSE IF    '${rating}'!='random' and ${rating}>=5 and '${rate}'!='${rating}'
                Drag And Drop By Offset    ${vertical_slider_thumb}    0    ${pixels}[${rating}]
            ELSE IF    ${rating}<5 and '${rate}'!='${rating}'
                Rate With Low Distress    ${rating}
            ELSE IF    '${rating}'!='random' and '${rate}'=='${rating}'
                BREAK
            END
        END
        Sleep    1
    ELSE
        IF    '${rating}'=='random'    Random Slider Rating
        ${rate}    Get Text    ${vertical_slider_thumb}
        Set Test Variable    ${rate}
        IF    '${rating}'=='random' and '${rate}'=='?'
            Swipe Slider For Native    ${rating}
        ELSE IF    '${rating}'!='random' and '${rate}'!='${rating}'
            Swipe Slider For Native    ${rating}
        END
    END

Random Slider Rating
    ${rating}    Evaluate    random.randint(0, 10)    modules=random
    Set Test Variable    ${rating}

Rate With Low Distress
    [Documentation]    Separating the rating with lower distress due to issues when using Drag and Drop By Offset keyword
    ...    when the element is not viewable by the time of the action
    [Arguments]    ${rating}
    Sleep    1
    ${keys}    Set Variable If    '${rating}'=='4'    TAB+ARROW_DOWN
    ...    '${rating}'=='3'    TAB+ARROW_DOWN+ARROW_DOWN
    ...    '${rating}'=='2'    TAB+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN
    ...    '${rating}'=='1'    TAB+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN
    ...    '${rating}'=='0'    TAB+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN+ARROW_DOWN
    Press Keys    ${vertical_slider_thumb}    ${keys}

Swipe Slider For Native
    [Documentation]    Keyword To Swipe Slider To Position On Native
    [Arguments]    ${rating}
    Wait Until Element Is Visible    ${slider_track}
    Scroll Element Into View    ${slider_track}
    ${slider_thumb_coord}    Get Element Rect    ${vertical_slider_thumb}
    ${slider_track_coord}    Get Element Rect    ${slider_track}
    ${X_coordinate}    Get From Dictionary    ${slider_thumb_coord}    x
    ${X_coordinate}    Convert To Integer    ${X_coordinate}
    ${Y_coordinate_start}    Get From Dictionary    ${slider_thumb_coord}    y
    ${Y_coordinate_start}    Convert To Integer    ${Y_coordinate_start}
    ${Y_coordinate_10}    Get From Dictionary    ${slider_track_coord}    y
    ${Y_coordinate_10}    Convert To Integer    ${Y_coordinate_10}
    ${height}    Get From Dictionary    ${slider_track_coord}    height
    ${height}    Convert To Integer    ${height}
    ${Y_coordinate_0}    Evaluate    ${Y_coordinate_10} + ${height}
    ${distance_between_two_ratings}    Evaluate    ${height} / 10
    ${Y_value}    Evaluate    ${Y_coordinate_0} - ${rating} * ${distance_between_two_ratings}
    Log To Console    ${Y_value}
    Sleep    1s
    Swipe    ${X_coordinate}    ${Y_coordinate_start}    ${X_coordinate}    ${Y_value}

Complete QOL Questionnaire As Nurse
    [Documentation]    User is already in the questionnaire
    Answer QOL Questions And Click Next
    Check QOL Summary

Answer QOL Questions And Click Next
    Wait Until Page Contains Element    ${questionnaire_next_button}
    ${status}    Run Keyword And Return Status    Element Should Be Visible    //button[@class='thumb']
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    IF    ${status}    Rate With Vertical Slider    8
    Click Element    ${questionnaire_next_button}

Check QOL Summary
    Wait Until Page Contains Element    ${qol_summary_container}
    Element Should Contain    ${qol_summary_text}    Please check that everything is correct.
    ...    If necessary, edit your answers by scrolling up.
    Element Should Be Visible    ${info_entered_by_caregiver_checkbox}
