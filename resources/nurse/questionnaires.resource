*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource


*** Variables ***
${add_treatment_module_button}                  //*[@data-testid="add-treatment-module"]
${add_without_template_link}                    //*[@data-testid="add-without-template-function-button"]
${treatment_module_selector}                    module-template-selector
${add_treatment_module_cancel_button}           //*[@data-testid="close-treatment-module"]
${responsible_care_team_dropdown}               //*[@data-testid='responsible-unit']/div
${responsible_care_team_dropdown_2}             //*[@data-testid="module-responsible-units_1"]/formly-group/formly-field/formly-wrapper-field
${responsible_care_team_link}                   module-responsible-units-add-button
${start_now_radio_button}                       //*[@data-testid="scheduled-start-false"]//label
${scheduled_radio_button}                       //*[@data-testid="scheduled-start-true"]//label
${save_treatment_module_button}                 //button[@data-testid="save-treatment-module"]
${schedule_template_dropdown}                   //*[@data-testid="questionnaire-template"]//input
${add_treatment_ok_button}                      //*[@data-testid='use-template-treatment-module']
${questionnaires_tab}                           tab-modules
${add_with_template_dropdown}                   //*[@data-testid="template-selector"]/div
${add_with_template_dropdown_input}             //*[@data-testid="template-selector"]/descendant::input
${add_without_template_dropdown}                //*[@data-testid="module-template-selector"]
${add_without_template_dropdown_input}          //*[@data-testid="module-template-selector"]/descendant::input
${module_edit_button}                           //*[@data-testid="open-treatment-module"]
${module_name_text}                             //div[@class='module-name link ng-binding']
${close_treatment_module_button}                //*[@data-testid="set-as-closed-treatment-module"]
${confirm_button}                               ok-confirm
${set_as_main_module_checkbox}                  //*[@data-testid='set-as-main-module']
${responsible_nurse_caredown}                   //*[@data-testid='responsible-nurse']/div
${schedule_content_dropdown}                    (//*[@data-testid="inquiry-aeq-type"])[last()]
${schedule_content_dropdown_input}              (//*[@data-testid="inquiry-aeq-type"]//input)[last()]
${date_format}                                  %d.%m.%Y
${date_format_scheduled}                        %d%m%Y
${remove_treatment_module_button}               //*[@data-testid='remove-treatment-module']
${close_treatment_module_button}                close-treatment-module
${add_schedule_button}                          //*[@id="symptom-inquiry-add-button"]
${treatment_date_input}                         (//input[@data-testid="treatment-date"])[last()]
${sending_date_input}                           (//input[@data-testid="sending-date"])[last()]
${scheduled_date_input}                         //*[@data-testid='treatmentStartDate']
${scheduled_questionnaire_row}                  //div[@class='inquiry scheduled']
${questionnaire_options_button}
...                                             (//div[@class='module-group'][1]//*[@data-testid="open-extra-options"])[last()]
${open_for_patient_button}                      //*[@data-testid="open-for-patient"]
${all-options}                                  (//*[@data-testid="open-extra-options"])
${patient_declined_button}                      //*[@data-testid="decline-questionnaire"]
${mark_qol_as_expired_button}                   //*[@data-testid="expire-questionnaire"]
${patient_cases_tab}                            tab-patient-messaging
${vertical_slider_thumb}                        //button[@class='thumb']
${last_complete_button}
...                                             (//div[@class='module-group'][1]//*[@data-testid="open-extra-action"])[last()]
${last_schedule_remove_button}                  (//*[@data-testid="symptom-inquiry-remove-button"])[last()]/..
${confirm_mark_as_expired_button}               ok-confirm
${cancel_mark_as_expired_button}                cancel-confirm
${mark_aeq_as_expired_button}                   //*[@data-testid="expire-module"]
${use_template_button}                          //*[@data-testid='symptom-inquiry-extra-button']
${scheduled_template_input}                     //*[@data-testid='templates']/descendant::input
${scheduled_template_date_input}                (//*[@data-testid='treatmentStartDate'])[last()]
${scheduled_template_apply_button}              //*[@data-testid='applyAdd-function-button']
${move_the_review_and_send_dates_checkbox}
...                                             //*[@data-testid='moveDate']/div/div
${complete_questionnaire_button}
...                                             (//td[text()="{}"]/following-sibling::td[contains(@class, 'button-cell')]//span[contains(text(),"Complete")])[last()]
${2_questionnaires_treatment_date_1}            (//*[@data-testid='treatment-date'])[last()-1]
${2_questionnaires_treatment_date_2}            (//*[@data-testid='treatment-date'])[last()]
${2_questionnaires_sending_date_1}              (//*[@data-testid='sending-date'])[last()-1]
${2_questionnaires_sending_date_2}              (//*[@data-testid='sending-date'])[last()]
${slider_track}                                 //div[@class='slider__track']
${reports_checkbox_1}                           //*[@id="module-responsible-units_0"]/formly-group/formly-field[2]/formly-group/formly-field[1]/formly-wrapper-field
${reports_checkbox_2}                           //*[@id="module-responsible-units_1"]/formly-group/formly-field[2]/formly-group/formly-field[1]/formly-wrapper-field
${messages_checkbox_1}                          //*[@id="module-responsible-units_0"]/formly-group/formly-field[2]/formly-group/formly-field[2]/formly-wrapper-field
${messages_checkbox_2}                          //*[@id="module-responsible-units_1"]/formly-group/formly-field[2]/formly-group/formly-field[2]/formly-wrapper-field
${remove_first_careteam}                        //*[@data-testid="module-responsible-units_0"]/../descendant::a
${remove_second_careteam}                       //*[@data-testid="module-responsible-units_1"]/../descendant::a
${questionnaire_dropdown_arrow}                 (//input[@id='one-time-code'])[last()]/../../..//span[@class='ng-arrow-wrapper']
${first_questionnaire_from_dropdown}            //div[@class='ng-dropdown-panel-items scroll-host']/div/div[1]
${mediconal_product_dosage_header}              //div[contains(text(),"Medicinal product & Dosage")]
${substances_dropdown}                          //*[@data-testid='substances']
${amount_text_field}                            //*[@data-testid='amount']
${unit_text_dropdown}                           //*[@data-testid='unit']


*** Keywords ***
Add Treatment Module
    [Documentation]    User is expected to be in the landing page after login
    [Arguments]
    ...    ${with_template}
    ...    ${module_or_template}
    ...    ${start_now}
    ...    ${care_team}=Care Team 1
    ...    ${schedule_content}=${EMPTY}
    Fetch Current Date
    Navigate To Questionnaires Tab
    Sleep    5s
    Wait Until Element Is Enabled    ${add_treatment_module_button}
    Try To Click Element    ${add_treatment_module_button}
    Wait Until Element Is Enabled    ${add_treatment_module_cancel_button}
    IF    ${with_template}
        Add Module With Template    ${module_or_template}    ${start_now}
    ELSE
        Add Module Without Template    ${module_or_template}    ${start_now}    ${care_team}    ${schedule_content}
    END

Add Module Without Template
    [Arguments]    ${module}    ${start_now}    ${care_team}    ${schedule_content}
    Try To Click Element    ${add_without_template_link}
    Wait Until Element Is Enabled    ${add_without_template_dropdown}
    Try To Click Element    ${add_without_template_dropdown}
    Try To Input Text    ${add_without_template_dropdown_input}    ${module}
    Try To Click Element    //*[@title='${module}']
    IF    ${start_now}    Try To Click Element    ${start_now_radio_button}
    # TODO: Else if start_now is False, click Scheduled start
    # TODO: Set as main treatment module
    Try To Click Element    ${responsible_care_team_link}
    Try To Input Text    ${responsible_care_team_dropdown}/descendant::input    ${care_team}
    Press Keys    ${responsible_care_team_dropdown}/descendant::input    ENTER
    IF    '${schedule_content}'!='${EMPTY}'
        Select Scheduled Content    ${schedule_content}    ${current_date_scheduled}
    END
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module added
    Try To Click Banner Message

Add A Second Care Team To Patient Main Module
    [Documentation]    User is should be on patient page
    [Arguments]    ${module}    ${second_care_team}
    Edit Module    ${module}
    Wait Until Element Is Visible    ${set_as_main_module_checkbox}
    Sleep    1s
    Element Should Be Disabled    ${save_treatment_module_button}
    ${module_has_one_careteam}    Run Keyword And Return Status
    ...    Page Should Not Contain Element
    ...    ${remove_second_careteam}
    ${module_has_two_careteams}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${remove_second_careteam}
    IF    ${module_has_one_careteam}==True
        Try To Click Element    ${responsible_care_team_link}
        Confirm That Reports And Messages Checkboxes Are Enabled And Clickable
        Try To Input Text    ${responsible_care_team_dropdown_2}/descendant::input    ${second_care_team}
        Press Keys    ${responsible_care_team_dropdown_2}/descendant::input    ENTER
        Element Should Be Enabled    ${save_treatment_module_button}
        Try To Click Element    ${save_treatment_module_button}
        Try To Click Banner Message
        Wait Until Page Does Not Contain    Treatment module saved
        Sleep    2s
    ELSE IF    ${module_has_two_careteams}==True
        Try To Click Element    ${add_treatment_module_cancel_button}
        Wait Until Page Contains Element    ${add_treatment_module_button}
    END

Confirm That Reports And Messages Checkboxes Are Enabled And Clickable
    [Documentation]    added because of a defect we once had in production NOONA-21651
    Wait Until Element Is Visible    ${reports_checkbox_1}
    @{checkboxes}    Create List
    ...    ${reports_checkbox_1}
    ...    ${reports_checkbox_2}
    ...    ${messages_checkbox_1}
    ...    ${messages_checkbox_2}
    FOR    ${each_checkbox}    IN    @{checkboxes}
        Try To Click Element    ${each_checkbox}
        Try To Click Element    ${each_checkbox}
    END

Remove First Care Team From Patient Main Module
    [Documentation]    Removes the first care team on the any patient treatment module
    [Arguments]    ${module}
    Edit Module    ${module}
    Wait Until Page Contains Element    ${add_treatment_module_cancel_button}
    ${module_has_one_careteam}    Run Keyword And Return Status
    ...    Page Should Not Contain Element
    ...    ${remove_second_careteam}
    ${module_has_two_careteams}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${remove_second_careteam}
    IF    ${module_has_two_careteams}==True
        Try To Click Element    ${remove_first_careteam}
        Element Should Be Enabled    ${save_treatment_module_button}
        Try To Click Element    ${save_treatment_module_button}
        Wait Until Page Does Not Contain    Treatment module saved
        Sleep    2s
    ELSE IF    ${module_has_one_careteam}==True
        Try To Click Element    ${add_treatment_module_cancel_button}
        Wait Until Page Contains Element    ${add_treatment_module_button}
    END

Remove Second Care Team From Patient Main Module
    [Documentation]    Removes the second care team on the any patient treatment module
    [Arguments]    ${module}
    Edit Module    ${module}
    Wait Until Page Contains Element    ${add_treatment_module_cancel_button}
    ${module_has_one_careteam}    Run Keyword And Return Status
    ...    Page Should Not Contain Element
    ...    ${remove_second_careteam}
    ${module_has_two_careteams}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${remove_second_careteam}
    IF    ${module_has_two_careteams}==True
        Try To Click Element    ${remove_second_careteam}
        Element Should Be Enabled    ${save_treatment_module_button}
        Try To Click Element    ${save_treatment_module_button}
        Wait Until Page Does Not Contain    Treatment module saved
        Sleep    2s
    ELSE IF    ${module_has_one_careteam}==True
        Try To Click Element    ${add_treatment_module_cancel_button}
        Wait Until Page Contains Element    ${add_treatment_module_button}
    END

Select Scheduled Content
    [Arguments]    ${schedule_content}    ${date}    ${send_date}=same
    Wait Until Element Is Visible    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    Wait Until Keyword Succeeds    20s    0.1s    Scroll Element Into View    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    Scroll Element Into View    ${add_treatment_module_cancel_button}
    Try To Click Element    ${add_schedule_button}
    Wait Until Keyword Succeeds    9s    0.3s    Try To Click Element    ${schedule_content_dropdown}
    Try To Input Text    ${schedule_content_dropdown_input}    ${schedule_content}
    Click Questionnaire From List    ${schedule_content}
    Input Text    ${treatment_date_input}    ${date}
    Try To Click Element    ${treatment_date_input}    #deletes autopopulated date
    Press Keys    ${treatment_date_input}    TAB+TAB+BACKSPACE    #deletes autopopulated date
    IF    '${send_date}'=='same'
        Input Text    ${sending_date_input}    ${date}
    ELSE
        Input Text    ${sending_date_input}    ${send_date}
    END

Click Questionnaire From List
    [Arguments]    ${schedule_content}
    Execute Javascript
    ...    document.querySelector('[role="listbox"]').querySelector('[title="${schedule_content}"]').click()

Add Module With Template
    [Arguments]    ${template}    ${start_now}
    ${status1}    Run Keyword And Return Status    Try To Click Element    ${add_with_template_dropdown}
    ${status2}    Run Keyword And Return Status    Wait Until Element Is Visible    ${noona-loader}
    IF    ${status1}==${FALSE} and ${status2}==${TRUE}
        Wait Until Noona Loader Is Not Visible
        Try To Click Element    ${add_with_template_dropdown}
    END
    Try To Input Text    ${add_with_template_dropdown_input}    ${template}
    Press Keys    ${add_with_template_dropdown_input}    ENTER
    IF    ${start_now}
        Wait Until Keyword Succeeds    9s    0.3s    Click Element    ${start_now_radio_button}
    ELSE
        Add Scheduled Start
    END
    Try To Click Element    ${add_treatment_ok_button}

Add Scheduled Start
    Tomorrow Date
    ${current_date_timestamp}    Get Current Date
    ${future_date}    Add Time To Date    ${current_date_timestamp}    1 days
    ${tomorrow_date_scheduled}    Convert Date    ${future_date}    result_format=%d%m%Y
    Try To Click Element    ${scheduled_radio_button}
    Input Text    ${scheduled_date_input}    ${tomorrow_date_scheduled}
    Press Keys    ${scheduled_date_input}    TAB

Fetch Current Date
    ${current_date}    Get Current Date    result_format=${date_format}
    ${current_date_scheduled}    Get Current Date    result_format=${date_format_scheduled}
    Set Test Variable    ${current_date}
    Set Test Variable    ${current_date_scheduled}

Tomorrow Date
    ${current_date_timestamp}    Get Current Date
    ${future_date}    Add Time To Date    ${current_date_timestamp}    1 days
    ${tomorrow_date_scheduled}    Convert Date    ${future_date}    result_format=${date_format_scheduled}
    Set Test Variable    ${tomorrow_date_scheduled}
    ${tomorrow_date}    Convert Date    ${future_date}    result_format=${date_format}
    Set Test Variable    ${tomorrow_date}

Remove Treatment Module
    [Arguments]    ${module}    ${date}
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    (//*[contains(@class, 'module-name link')])[2][contains(text(), '${module}')]
    IF    ${status}
        Try To Click Element
        ...    //div[contains(@class, "start-date") and contains(text(),'${date}')]/../../preceding-sibling::div[text()='${module}']
        Wait Until Page Contains Element    ${remove_treatment_module_button}
        Scroll Element Into View    ${remove_treatment_module_button}
        Try To Click Element    ${remove_treatment_module_button}
        Try To Click Element    ${confirm_button}
        Wait Until Page Contains    Treatment module removed    5s
    END

Close And Verify Module Moved To Closed
    [Arguments]    ${module}    ${date}
    Try To Click Element
    ...    //div[contains(@class, "start-date") and contains(text(),'${date}')]/../../preceding-sibling::div[text()='${module}']
    Wait Until Page Contains Element    ${close_treatment_module_button}
    Try To Click Element    ${close_treatment_module_button}
    Try To Click Element    ${confirm_button}
    Wait Until Page Contains    Treatment module saved
    Try To Click Banner Message
    Page Should Contain Element
    ...    //h4[contains(text(), "Closed modules")]/../descendant::div[text()='${module}'][1]/../div[3]/div[1]
    Wait Until Element Contains
    ...    //h4[contains(text(), "Closed modules")]/../descendant::div[text()='${module}'][1]/../div[3]/div[1]
    ...    ${date}

Verify Template Content And Save
    [Arguments]
    ...    ${template_treatment_module}
    ...    ${template_care_team}
    ...    ${template_clinic_user}
    ...    ${template_questionnaire}
    Wait Until Page Contains Element    ${treatment_module_selector}
    ${selected_module}    Get Text    ${treatment_module_selector}
    Scroll Element Into View    ${add_treatment_module_cancel_button}
    ${selected_care_team}    Get Text    ${responsible_care_team_dropdown}
    Wait Until Element Is Visible    ${responsible_nurse_caredown}
    ${selected_nurse}    Get Text    ${responsible_nurse_caredown}
    ${selected_questionnaire}    Get Text    ${schedule_content_dropdown}
    Should Be Equal    ${selected_module}    ${template_treatment_module}
    Should Be Equal    ${selected_care_team}    ${template_care_team}
    Should Be Equal    ${selected_nurse}    ${template_clinic_user}
    Should Be Equal    ${selected_questionnaire}    ${template_questionnaire}
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module added
    Try To Click Banner Message

Navigate To Questionnaires Tab
    Wait Until Element Is Enabled    ${questionnaires_tab}
    Wait Until Keyword Succeeds    20s    1s    Click Element    ${questionnaires_tab}

Set Module As Closed
    [Documentation]    Module should not be set as the main module before closing
    [Arguments]    ${module}
    Edit Module    ${module}
    Wait Until Element Is Visible    ${close_treatment_module_button}
    Scroll Element Into View    ${close_treatment_module_button}
    Try To Click Element    ${close_treatment_module_button}
    Wait Until Element Is Enabled    ${confirm_button}
    Wait Until Keyword Succeeds    15s    1s    Click Element    ${confirm_button}
    Wait Until Page Contains    Treatment module saved

Set Module As Main
    [Documentation]    Module has already been selected
    [Arguments]    ${module}
    Edit Module    ${module}
    Wait Until Element Is Visible    ${set_as_main_module_checkbox}
    Sleep    1s
    Try To Click Element    ${set_as_main_module_checkbox}
    Wait Until Element Is Enabled    ${confirm_button}
    Wait Until Keyword Succeeds    15s    1s    Click Element    ${confirm_button}
    Wait Until Element Is Enabled    ${save_treatment_module_button}
    Scroll Element Into View    ${save_treatment_module_button}
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Try To Click Banner Message

Edit Module
    [Documentation]    only Selects module under questionnaires tab
    [Arguments]    ${module}
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    Wait Until Page Contains Element    ${module_edit_button}
    Try To Click Element    //div[text()="${module}"]/../../descendant::button

Add Questionnaire To Schedule
    [Arguments]    ${questionnaire}    ${review_date}=default    ${send_date}=default
    Fetch Current Date
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    Try To Click Element    ${module_edit_button}
    IF    '${review_date}'!='default' and '${send_date}'!='default'
        Select Scheduled Content    ${questionnaire}    ${review_date}    ${send_date}
    ELSE
        Select Scheduled Content    ${questionnaire}    ${current_date_scheduled}
    END
    Scroll Element Into View    ${save_treatment_module_button}
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Reload Page Until Questionnaire Is Sent    ${questionnaire}

Add Scheduled Message To Module
    [Arguments]    ${scheduled_message}    ${send_date}=today
    Fetch Current Date
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Not Visible    ${noona-loader}
    IF    '${send_date}'!='today'
        Select Scheduled Content    ${scheduled_message}    ${send_date}
    ELSE
        Select Scheduled Content    ${scheduled_message}    ${current_date_scheduled}
    END
    Scroll Element Into View    ${save_treatment_module_button}
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Reload Page Until Questionnaire Is Sent    ${questionnaire}

Schedule Questionnaire For Tomorrow
    [Arguments]    ${questionnaire}
    Tomorrow Date
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Not Visible    ${noona-loader}
    Select Scheduled Content    ${questionnaire}    ${tomorrow_date_scheduled}
    Scroll Element Into View    ${save_treatment_module_button}
    Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved

Add Multiple Questionnaires
    [Arguments]    @{questionnaires}
    Fetch Current Date
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    ${count}    Get Count    @{questionnaires}
    FOR    ${questionnaire}    IN    @{questionnaires}
        Try To Click Element    ${module_edit_button}
        Wait Until Element Is Not Visible    ${noona-loader}
        Select Scheduled Content    ${questionnaire}    ${current_date_scheduled}
        Click Element    ${save_treatment_module_button}
        Wait Until Page Contains    Treatment module saved
    END
    Reload Page Until Questionnaire Is Sent    ${questionnaires}[-1]

Add Multiple Questionnaires And Save
    [Arguments]    @{questionnaires}
    Fetch Current Date
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${module_edit_button}
    IF    ${status}==${False}    Navigate To Questionnaires Tab
    ${count}    Get Count    @{questionnaires}
    Try To Click Element    ${module_edit_button}
    ${noona_loader_status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${noona-loader}
    IF    ${noona_loader_status} == True
        Wait Until Element Is Not Visible    ${noona-loader}    10s
    END
    FOR    ${questionnaire}    IN    @{questionnaires}
        Select Scheduled Content    ${questionnaire}    ${current_date_scheduled}
    END
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Reload Page Until Questionnaire Is Sent    ${questionnaires}[-1]

Reload Page Until Questionnaire Is Sent
    [Documentation]    Reloads page within 3mins
    [Arguments]    ${questionnaire}
    FOR    ${INDEX}    IN RANGE    1    30
        ${status}    Run Keyword And Return Status
        ...    Page Should Not Contain Element
        ...    (//td[text()="${questionnaire}"])/following-sibling::*[contains(@class, 'scheduled')][last()]
        IF    ${status}==${False}
            Reload Page
            Sleep    6
            Wait Until Element Is Visible    ${module_edit_button}
        END
        IF    '${status}'=='True'    BREAK
    END

Open Questionnaire For Patients
    [Documentation]    Clicks the last Options button instance
    Wait Until Element Is Visible    ${questionnaire_options_button}
    Scroll Element Into View    ${questionnaire_options_button}
    Try To Click Element    ${questionnaire_options_button}
    Wait Until Element Is Visible    ${open_for_patient_button}
    Try To Click Element    ${open_for_patient_button}

Open QOL Questionnaire For Patient
    [Documentation]    Clicks the last Options button instance. questionnaire type is qol.
    [Arguments]    ${questionnaire}
    Wait Until Element Is Visible    ${module_edit_button}
    Scroll Element Into View    ${module_edit_button}
    Try To Click Element    ${questionnaire_options_button}
    Try To Click Element    ${open_for_patient_button}
    Wait Until Element Is Not Visible    ${open_for_patient_button}

Questionnaire Filled By Clinic User
    [Arguments]    ${questionnaire}
    Try To Click Element    ${patient_cases_tab}
    Wait Until Page Contains Element    //div[contains(@class, "case-title") and contains(text(),'${questionnaire}')]
    Try To Click Element    (//div[contains(@class, "case-title") and contains(text(),'${questionnaire}')])[1]
    Wait Until Page Contains    Questionnaire was completed by Clinic Staff

Click Complete Questionnaire Button
    [Documentation]    Clicks "Complete" button outside Options button of the last questionnaire
    Wait Until Keyword Succeeds    9s    1s    Scroll Element Into View    ${last_complete_button}
    Try To Click Element    ${last_complete_button}

Answer Symptom Questionnaire As Nurse With Mild Other Symptom
    Click Complete Questionnaire Button
    Select Yes For Symptoms    Other symptom
    Changes In Gen State Of Health Is Displayed
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    Click Element    ${aeq_questionnaire_next_button}
    compare_questionnaires.Save Questionnaire And Click Ok To Confirm

Decline Questionnaire For Patient
    [Documentation]    Clicks the last Options button instance
    Wait Until Element Is Visible    ${questionnaire_options_button}
    Scroll Element Into View    ${questionnaire_options_button}
    Try To Click Element    ${questionnaire_options_button}
    Wait Until Element Is Visible    ${patient_declined_button}
    Try To Click Element    ${patient_declined_button}
    Wait Until Element Is Not Visible    ${patient_declined_button}
    Wait Until Page Contains    Questionnaire marked as declined
    Try To Click Banner Message

Questionnaire Status Is Correct
    [Arguments]    ${questionnaire}    ${questionnaire_status}    ${questionnaire_type}=qol
    ${status_index}    Set Variable If    '${questionnaire_type}'=='qol'    4
    ...    '${questionnaire_type}'=='aeq'    3
    Wait Until Element Is Visible    ${module_edit_button}
    ${questionnaire_lower}    Convert To Lowercase    ${questionnaire_status}
    ${status_class}    Set Variable If    '${questionnaire_lower}'=='sent'    sent
    ...    'expired' in '${questionnaire_lower}'    expired
    ...    '${questionnaire_lower}'=='completed by the clinic'    filledByClinic
    ...    '${questionnaire_lower}'=='responded'    answered
    ...    '${questionnaire_lower}'=='patient declined'    declined
    ...    '${questionnaire_lower}'=='scheduled'    scheduled
    Sleep    1
    ${text}    Get Text    (//div[@class='module-group'][1]/descendant::table//td[4])[last()]
    ${status}    Run Keyword And Return Status    Should Contain    ${questionnaire_status}    EXPIRED
    IF    ${status}
        ${text}    Replace String    ${text}    ${\n}    ${SPACE}
    END
    Should Be Equal    ${questionnaire_status}    ${text}

Remove Questionnaire
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Visible    ${last_schedule_remove_button}
    Scroll Element Into View    ${save_treatment_module_button}
    Wait Until Keyword Succeeds    9s    0.3s    Click Element    ${last_schedule_remove_button}
    Sleep    1
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved

Scheduled Questionnaire Is Removed
    [Arguments]    ${questionnaire}
    Wait Until Element Is Visible    ${module_edit_button}
    Page Should Not Contain    (//div[contains(text(),"${questionnaire}")])[last()]/following-sibling::div[4]

Mark Questionnaire As Expired
    [Documentation]    Clicks the last Options button instance. questionnaire type is either qol or aeq.
    [Arguments]    ${questionnaire_type}
    Wait Until Element Is Visible    ${module_edit_button}
    Scroll Element Into View    ${module_edit_button}
    IF    '${questionnaire_type}'=='qol'
        Click Element    ${questionnaire_options_button}
        Wait Until Element Is Visible    ${mark_qol_as_expired_button}
        Click Element    ${mark_qol_as_expired_button}
    ELSE IF    '${questionnaire_type}'=='aeq'
        Click Element    ${mark_aeq_as_expired_button}
    END
    Wait Until Page Contains    Mark as expired?
    Element Should Be Enabled    ${cancel_mark_as_expired_button}
    Try To Click Element    ${confirm_mark_as_expired_button}
    Wait Until Page Contains    Questionnaire marked as expired
    Try To Click Banner Message

Use Template For Scheduled Content
    [Arguments]    ${template_name}
    Wait Until Element Is Visible    ${use_template_button}
    Wait Until Element Is Enabled    ${use_template_button}
    Try To Click Element    ${use_template_button}
    Try To Input Text    ${scheduled_template_input}    ${template_name}
    Press Keys    ${scheduled_template_input}    ENTER

Select Current Date For Scheduled Template
    [Arguments]    ${date}
    Wait Until Element Is Visible    ${save_treatment_module_button}
    Scroll Element Into View    ${save_treatment_module_button}
    Try To Input Text    ${scheduled_template_date_input}    ${date}

Input Additional Questions For Next Visit
    [Documentation]    Used only by Treatment visit and Clinic appointment
    [Arguments]    ${message}
    Wait Until Page Contains
    ...    Please enter details here if you wish to discuss something specific with your care team during your next clinic visit.
    Input Text    xpath=//*[@id="aeq-additional-qestions"]    ${message}

Move Review Date
    [Arguments]    ${date}
    # locally    HOME,SHIFT+END,BACKSPACE works
    Press Keys
    ...    ${2_questionnaires_treatment_date_1}
    ...    CTRL+a
    ...    BACKSPACE
    Input Text    ${2_questionnaires_treatment_date_1}    ${date}

Questionnaire Treatment Review Date Is Correct
    [Arguments]    ${questionnaire}    ${expected_review_date}
    ${index}    Set Variable If    '${questionnaire}'=='Quality of life (15D)'    2    1
    Wait Until Element Is Visible
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    Scroll Element Into View
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    ${review_date}    Get Text
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    Should Contain    ${review_date}    ${expected_review_date}

Questionnaire Sending Date Is Correct
    [Arguments]    ${questionnaire}    ${expected_send_date}
    ${index}    Set Variable If    '${questionnaire}'=='Quality of life (15D)'    2    1
    Wait Until Element Is Visible
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    Scroll Element Into View
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    ${send_date}    Get Text
    ...    (//div[@class='module-group'][1]//td[text()="${questionnaire}"])[last()]/following-sibling::td[${index}]
    Should Contain    ${send_date}    ${expected_send_date}

Mark All QOL Questionnaires As Expired
    [Documentation]    user is already in the Questionnaires tab
    Wait Until Element Is Visible    ${module_edit_button}
    @{elements}    Get WebElements    ${all-options}
    FOR    ${element}    IN    @{elements}
        Try To Click Element    ${element}
        Wait Until Element Is Visible    ${mark_qol_as_expired_button}
        Try To Click Element    ${mark_qol_as_expired_button}
        Wait Until Page Contains    Mark as expired?
        Try To Click Element    ${confirm_mark_as_expired_button}
        Wait Until Page Contains    Questionnaire marked as expired
        Try To Click Banner Message
    END

Click Complete Button Per Questionnaire
    [Arguments]    ${questionnaire}
    ${element}    Format String    ${complete_questionnaire_button}    ${questionnaire}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}

Verify Complete Questionnaire Button Visibility
    [Arguments]    ${visible}    ${questionnaire}
    IF    '${visible}'=='Yes'
        Complete Questionnaire Button Is Visible    ${questionnaire}
    ELSE
        Sleep    1
        ${element}    Format String    ${complete_questionnaire_button}    ${questionnaire}
        Wait Until Element Is Not Visible    ${element}
    END

Complete Questionnaire Button Is Visible
    [Arguments]    ${questionnaire}
    ${element}    Format String    ${complete_questionnaire_button}    ${questionnaire}
    Wait Until Element Is Visible    ${element}

Update Module Care Team
    [Arguments]    ${care_team}
    Try To Click Element    ${responsible_care_team_dropdown}
    Try To Input Text    ${responsible_care_team_dropdown}/descendant::input    ${care_team}
    Press Keys    ${responsible_care_team_dropdown}/descendant::input    ENTER

Select Patient Symptomatic Days In Calendar
    [Documentation]    Only selects past 3 days which includes current date
    Select Answer To Question    When did you have this symptom?    Mark symptomatic days
    ${today}    Get Current Date
    ${yesterday_month_year}    Subtract Time From Date    ${today}    1 day    result_format=%B${SPACE}%Y
    ${yesterday_date}    Subtract Time From Date    ${today}    1 day    result_format=%-d
    ${2days_ago_month_year}    Subtract Time From Date    ${today}    2 days    result_format=%B${SPACE}%Y
    ${2days_ago_date}    Subtract Time From Date    ${today}    2 days    result_format=%-d
    ${todays_month_year}    Get Current Date    result_format=%B${SPACE}%Y
    ${todays_date}    Get Current Date    result_format=%-d
    ${todays_date}    Set Variable
    ...    //th[contains(text(),"${todays_month_year}")]/../../../descendant::td[not(contains(@class, 'disabled'))]/div[text()="${todays_date}"]
    ${yesterday}    Set Variable
    ...    //th[contains(text(),"${yesterday_month_year}")]/../../../descendant::td[not(contains(@class, 'disabled'))]/div[text()="${yesterday_date}"]
    ${2days_ago}    Set Variable
    ...    //th[contains(text(),"${2days_ago_month_year}")]/../../../descendant::td[not(contains(@class, 'disabled'))]/div[text()="${2days_ago_date}"]
    Set Test Variable    ${2days_ago}
    Set Test Variable    ${todays_date}
    Try To Click Element    ${todays_date}
    Try To Click Element    ${yesterday}
    Try To Click Element    ${2days_ago}
    Try To Click Element    (${todays_date})[2]

Check Questionnaires Date Format
    [Arguments]    ${questionnaire}    ${review_date}=current    ${send_date}=current
    ${actual_review_date}    Set Variable    (//td[text()="${questionnaire}"])[last()]/following-sibling::td[1]
    ${actual_send_date}    Set Variable    (//td[text()="${questionnaire}"])[last()]/following-sibling::td[2]
    Wait Until Element Is Visible    ${actual_review_date}
    ${actual_review_date}    Get Text    ${actual_review_date}
    ${actual_send_date}    Get Text    ${actual_send_date}
    Should Be Equal    ${review_date}    ${actual_review_date}
    Should Be Equal    ${send_date}    ${actual_send_date}

Verify Questionnaire Option If Disabled
    [Documentation]    User is in questionnaires tab
    [Arguments]    ${questionnaire}
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Visible    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    Wait Until Keyword Succeeds    20s    0.1s    Scroll Element Into View    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    Scroll Element Into View    ${add_treatment_module_cancel_button}
    Try To Click Element    ${add_schedule_button}
    Try To Click Element    ${questionnaire_dropdown_arrow}
    Scroll Element Into View    ${first_questionnaire_from_dropdown}
    Wait Until Element Is Visible    //div[contains(@class, 'option-disabled')]//div[@title='${questionnaire}']

Set Questionnaires Dates
    [Arguments]    ${date_format}=%d.%m.%Y    ${date_format_scheduled}=%d%m%Y
    ${current_date}    Get Current Date    result_format=${date_format}
    ${current_date_scheduled}    Get Current Date    result_format=${date_format_scheduled}
    ${current_date_timestamp}    Get Current Date
    ${tomorrow_date}    Add Time To Date    ${current_date_timestamp}    1 days    result_format=${date_format}
    ${tomorrow_date_scheduled}    Add Time To Date
    ...    ${current_date_timestamp}
    ...    1 days
    ...    result_format=${date_format_scheduled}
    ${after_2_days}    Add Time To Date    ${current_date_timestamp}    2 days    result_format=${date_format}
    ${after_2_days_scheduled}    Add Time To Date
    ...    ${current_date_timestamp}
    ...    2 days
    ...    result_format=${date_format_scheduled}
    ${after_7_days}    Add Time To Date    ${current_date_timestamp}    7 days    result_format=${date_format}
    ${after_7_days_scheduled}    Add Time To Date
    ...    ${current_date_timestamp}
    ...    7 days
    ...    result_format=${date_format_scheduled}
    Set Test Variable    ${current_date}
    Set Test Variable    ${current_date_scheduled}
    Set Test Variable    ${tomorrow_date}
    Set Test Variable    ${tomorrow_date_scheduled}
    Set Test Variable    ${after_2_days}
    Set Test Variable    ${after_2_days_scheduled}
    Set Test Variable    ${after_7_days}
    Set Test Variable    ${after_7_days_scheduled}
