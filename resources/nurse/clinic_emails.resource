*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Library     ${EXECDIR}${/}resources${/}libraries${/}email_attachments.py


*** Keywords ***
User Is Notified About A New Message
    [Arguments]    ${email}    ${password}    ${subject}    ${text}    ${expected_link}
    Open Mailbox    host=imap.mail.yahoo.com    user=${email}    password=${password}    port=993
    ${LATEST}    Wait For Email
    ...    sender=<EMAIL>
    ...    status=UNSEEN
    ...    subject=${subject}
    ...    text=${text}
    ...    timeout=100
    ${body}    Get Email Body    ${LATEST}
    ${link_list}    Get Links From Email    ${LATEST}
    Set Test Variable    ${link_list}
    ${link}    Convert To String    ${link_list}
    ${link}    Strip String    ${link}    characters=[]'
    Should Contain    ${link}    ${expected_link}
    Set Test Variable    ${link}
    Set Test Variable    ${LATEST}

Delete All User Emails
    [Arguments]    ${email}    ${password}
    Open Mailbox    host=imap.mail.yahoo.com    user=${email}    password=${password}    port=993
    ImapLibrary2.Delete All Emails
