*** Settings ***
Library     String
Library     DateTime
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}upload_or_remove_photo.resource


*** Variables ***
${patient_cases_tab}                            tab-patient-messaging
${list_view_button}                             //div[contains(@class, "list-view-icon")]
${card_view_button}                             //div[contains(@class, "card-view-icon")]
${log_out_link}                                 //*[@data-testid='logout-link']
${care_teams_dropdown}                          //div[contains(@class, "user-and-care-team-filter")]//button
${reports_tab}                                  side-effect-reports
${open_cases_tab}                               new-messages
${first_work_queue_item}                        workqueue-item-name-0
${patient-messaging-content}                    patient-messaging-content
${opened_case_container}                    //*[contains(@data-testid, "case-open")]//section[contains(@class, "patient-case-list-item")]
${opened_case_creation_date}                ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[1]//div[@class="subtitle"]
${opened_case_case_updated_date}            ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[2]//div[@class="subtitle"]
${opened_case_case_priority}                ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[3]//div[@class="subtitle"]
${opened_case_status}                        ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[4]//div[@class="subtitle"]
${opened_case_assigned_careteam}            ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[5]//div[@class="subtitle"]
${opened_case_assignee}                     ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[6]//div[@class="subtitle"]
${opened_case_origin}                       ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[7]//div[@class="subtitle"]
${opened_case_info_header}                  ${opened_case_container}//div[contains(@class, "case-header")]//h5
${open_patient_cases}
...                                             //div[contains(@id, "patient-messaging-content")]//div[contains(@class, "clinic") or contains(@class, "open") or contains(@class, "inProgress") or contains(@class, "new") or contains(@class, "callbackRequested")]
${contact_patient_button_top}                   //*[@data-testid="contact-patient-btn"]
${contact_patient_button}                       //*[@data-testid="contact-patient-button"]
${contact_patient_button_opened}
...                                             //*[contains(@class, "mat-expanded")]//*[@data-testid="contact-patient-button"]
${contact_type_list}                            //*[@id='type']/../..
${contact_type_box}                             //*[@data-testid="type"]
${contact_message_field}                        //*[@data-testid="messageForPatient-textarea"]
${send_contact_button}                          send-contact
${all_messages}                                 //div[contains(@class, "new-message")]
${search_care_team_dropdown_button}             //*[@data-testid="workQueue__careTeam--filter"]
${case_status}
...                                             //div[contains(@class, "case-details open-case open")]//div[text()='Case Status']/following::div[contains(@class, "subtitle")][1]
${patient_messaging_content_other_symptom}
...                                             //div[contains(@id, "patient-messaging-content")]//*[contains(@data-testid, 'case-open')]//div[contains(text(), 'Other symptom')]
${case_team_1}                                  Care Team 1
${close_case_button}                            //*[@data-testid='close-case-button']
${patient_cases_list}                           //div[@class='patient-case-list']
${first_close_case_button}
...                                             (//noona-patient-case-list-item//*[contains(@class, "mat-expanded")])[1]//*[@data-testid='close-case-button']
${closed_other_symptom_cases}
...                                             //div[contains(text(), "Other symptom")]//..//span[contains(text(), "Closed")]
${open_other_symptom_cases}
...                                             //*[@id="patient-messaging-content"]//div[contains(text(), "Other symptom")]/..//span[contains(text(), "Open")]
${close_case_modal}                             //*[@data-testid='close-case']
${patient_cases_case_row}                       //div[starts-with(@class, 'case-title')]    # //div[@class='case-title']
${patient_cases_case_status}                    //*[contains(@class, 'case-status')]    # //div[@class='case-status']
${patient_cases_date}                           //*[@class='date']
${patient_cases_case_assignee}                  //div[starts-with(@class, 'assignee')]
${case_outcome_checkbox}
...                                             //*[contains(@data-testid, 'case-outcome')]//label//*[contains(@class, 'formly-label')]
${reason_for_delay_dropdown}                    //*[@data-testid="delayReason"]
${reason_for_delay_list}
...                                             //div[contains(@class, "ng-dropdown-panel-items")]//div[contains(@class, 'ng-option')]
${case_outcome_label}                           //mat-label//*[contains(text(), "Case outcome")]
${case_type_dropdown}                           //*[@id='case-type__select']
${case_care_team_dropdown}                      care-team__select
${assigned_to_dropdown}                         assignee__select
${case_origin_dropdown}                         case-origin__select
${case_priority_dropdown}                       priority__select
${case_status_dropdown}                         status__select
${case_description_field}                       textarea-case-description
${case_save_exit_button}                        //*[@data-testid='newCase--save__button']
${case_continue_button}                         //*[@data-testid='newCase--continue__button']
${case_notes_field}                             textarea-case-notes
${medication_category_dropdown}
...                                             //div[contains(text(),"Medication Category")]/following-sibling::nh-dropdown//button
${medication_name_field}                        medicationName
${number_of_refills_field}                      //div[contains(text(),"Number of Refills")]/following-sibling::input
${dose_field}                                   //div[contains(text(),"Dose")]/following-sibling::input
${unit_dropdown}
...                                             //div[contains(text(),"Unit")]/following-sibling::nh-dropdown/descendant::button
${frequency_field}                              //div[contains(text(),"Frequency")]/following-sibling::input
${prescribing_physician_dropdown}
...                                             //div[contains(text(),"Prescribing physician")]/following-sibling::nh-dropdown//button//*[contains(@class,'caret')]
${case_next_button}                             //button[contains(text(),"Next")]
${narcotics_db_checked}                         //input[@id='enabled-checkbox-narcoticsChecked']/..
${case_summary_area}                            case-base-information-summary
${medication_summary}                           //div[@class='case-medication-summary']
${change_case_type_modal}                       //div[@class='modal-content']
${change_type_button}                           id=ok-confirm
${case_symptom_list}                            //div[contains(text(),"{}")]
${case_symptom_summary}                         //div[@class='symptom-card']
${acute_symptoms_checkbox}                      label-enabled-checkbox-
${case_note_button}
...                                             //mat-expansion-panel-header[@aria-expanded="true"]/..//*[@data-testid="add-case-note-button"]
${case_topic_field}                             data:testid:title
${case_note_field}                              data:testid:content
${case_note_save_button}                        data:testid:case-note__save--button
${saved_case_note_content}                      (//div[starts-with(@class, 'note')])[last()]
${last_case_note_edit_button}                   (//*[@data-testid="edit-caseNote-icon"])[last()]
${case_note_reminder_checkbox}                  data:testid:reminder
${case_note_reminder_tomorrow_date}             //td[@class="active day"]/following-sibling::td[1]
${case_reminder_unavailable_label}              (//div[@class="fieldContainer"]//div/span)[2]
${case_note_reminder_date_input}                data:testid:reminderDate
${consult_a_care_team_button}                   //*[contains(text()," Consult a care team / person ")]
${consult_care_team_dropdown}                   //label[text()="Care team *"]/../descendant::button
${consult_care_team_option}                     //div[contains(@data-testid, 'option-')]
${consult_care_team_field}                      //*[@data-testid="careTeam"]//input
${consult_care_person_field}                    //*[@data-testid="carePerson"]//input
${consult_message_field}                        data:testid:contact-message
${consult_button}                               send-contact-button
${reply_to_consultation_button}                 data:testid:reply-consultation-button
${reply_button}                                 data:testid:send-reply-button
${cancel_consultation_button}                   data:testid:cancel-consultation-button
${patient_cases_tab}                            //*[@data-testid="patient-messaging"]
${non_case_related_contact_patient_button}      //button[@data-testid='contact-patient-btn']
${contact_patient_topic_field}
...                                             //div[@data-testid='contact-patient-form']/descendant::div[@class='form-field'][1]/descendant::input
${assign_unassign_button}                       //*[contains(@data-testid, "assignment-button")]
${case_note_template_input}                     //*[@id="note-templates__select"]//input
${automation_note_template_title}               Automation note template for add case
${case_note_template_panel}                     //div[contains(@class, "dropdown-panel")]
${case_note_text_area}                          textarea-case-notes
${automation_note_template_content}             This is test automation template. Do not delete.
${expanded_case_note}
...                                             //*[contains(@class, 'case-information-content')]//div[contains(text(), "Case Notes")]/../div[2]
${expanded_case_internal_message}               //div[contains(@class, 'internal-message')]
${followup_duration_field}                      //*[@data-testid='duration']
${patient_education_panel_message}
...                                             //p[contains(text(),"Documents attached to Patient Education messages are included in the patient's Library in Noona.")]
${contact_panel_tooltip}                        //*[@data-mat-icon-name='icon-info']/..
${patient_education_info_text}
...                                             //span[contains(text(),'= Documents attached to this type will be added to')]
${add_documents_link}                           //a[starts-with(@class, 'form-group add-attachment link')]
${patient_cards_work_queue}                     //noona-patient-card/mat-card
${contact_type_question}                        //div[@title="Question"]
${contact_type_instruction}                     (//div[@title="Instructions"])[last()]
${contact_type_followup}                        (//div[@title="Follow-up instruction"])[last()]
${contact_type_inv_pe}                          (//div[@title="Invitation for a physical examination"])[last()]
${contact_type_inv_ct}                          (//div[@title="Invitation for clinical tests"])[last()]
${contact_type_pe}                              (//div[@title="Patient Education"])[last()]
${first_template_option}                        data:testid:option-0
${case_type_dropdown_list}                      //*[@role='listbox']//div[contains(@class, 'scroll-host')]
${medication_category_dropdown_list}
...                                             //div[contains(text(),"Medication Category")]/following-sibling::nh-dropdown//ul
${prescribing_physician_dropdown_list}
...                                             //div[contains(text(),"Prescribing physician")]/following-sibling::nh-dropdown//ul
${patient_message_title}                        (//h5[@class='message-title'])[last()]
${disabled_contact_patient_text_area}           //*[@data-testid="messageForPatient-textarea" and @disabled]
${disabled_add_documents_link}
...                                             //a[contains(@class, 'form-group add-attachment link') and @disabled='true']
${case_type_chemo_tx_questions}                 Chemo / Tx Questions
${case_type_paperwork}                          Paperwork
${case_outcome_left_message}                    Left message
${case_status_closed}                           Closed
${case_created_successfully_banner}             Case created successfully
${case_type_symptom_management}                 Symptom Management
${case_assigned_successfully_banner}            Case assigned successfully
${patient_is_already_assigned_banner}           Patient is already assigned
&{case_priority}                                low=Low    medium=Medium    high=High    critical=Critical
${assigned_user_per_case}                       //div[text()=" {} "]/../div[contains(@class, 'assignee')]
# Locators in Symptom Report under a patient case (from symptom questionnaire such as Baseline)
${symptom_report_symptom_row}                  //noona-patient-case-list//*[@class="noona-symptom-report"]//noona-symptoms-row
${symptom_report_symptom_title}                 ${symptom_report_symptom_row}//div[contains(@class, "symptom-title")]
${attachment_acknowledgement_button}           //button[contains(@class,"noona-disclaimer-dialog-actions__confirm")]
${acute_symptoms_list}                         //div[text()="Acute Symptoms"]/following-sibling::div/div[@class="subtitle"]


*** Keywords ***
Select Case Type
    [Arguments]    ${case_type}
    Try To Click Element    ${case_type_dropdown}
    Select Option From Dropdown    ${case_type_dropdown}    ${case_type}

Select Care Team
    [Arguments]    ${care_team}
    Select Option From Dropdown    ${case_care_team_dropdown}    ${care_team}

Select Clinic User
    [Arguments]    ${clinic_user}
    Select Option From Dropdown    ${assigned_to_dropdown}    ${clinic_user}
    Sleep    1

Select Case Priority
    [Arguments]    ${priority}
    Select Option From Dropdown    ${case_priority_dropdown}    ${priority}

Select Case Origin
    [Arguments]    ${origin}
    Select Option From Dropdown    ${case_origin_dropdown}    ${origin}

Select Case Status
    [Arguments]    ${status}
    Select Option From Dropdown    ${case_status_dropdown}    ${status}

Select Option From Dropdown
    [Documentation]    Generic keyword for Open case dropdowns
    [Arguments]    ${dropdown}    ${option}
    Try To Click Element    ${dropdown}
    ${actual_options}    Get WebElements    //div[contains(@class, "ng-option")]
    FOR    ${actual_option}    IN    @{actual_options}
        ${option_text}    Get Text    ${actual_option}
        ${contains_text}    Run Keyword And Return Status    Should Contain    ${option_text}    ${option}
        IF    '${contains_text}'=='True'
            Click Element    ${actual_option}
            BREAK
        END
    END

Input Case Description
    [Arguments]    ${text}
    Input Text    ${case_description_field}    ${text}

Click Continue Case Button
    Wait Until Element Is Visible    ${case_continue_button}
    Try To Click Element    ${case_continue_button}

Click Save And Exit Case Button
    Wait Until Element Is Visible    ${case_save_exit_button}
    Try To Click Element    ${case_save_exit_button}

Input Case Details
    [Documentation]    Keyword Set Case Details should be run before this
    [Arguments]    ${action}
    Select Case Type    ${case_details}[case_type]
    Select Care Team    ${case_details}[care_team]
    Sleep    1
    ${assigned_to_exists}    Run Keyword And Return Status    Should Not Be Empty    ${case_details}[assigned_to]
    IF    ${assigned_to_exists}
        Select Clinic User    ${case_details}[assigned_to]
    END
    Input Case Description    ${case_details}[case_description]
    IF    '${case_details}[case_type]'=='Symptom Management'
        Select Acute Symptoms    @{acute_symptoms}
    END
    Select Case Priority    ${case_details}[case_priority]
    Select Case Origin    ${case_details}[case_origin]
    IF    '${action}'=='continue'
        Click Continue Case Button
    ELSE IF    '${action}'=='save'
        Click Save And Exit Case Button
    END

Select Acute Symptoms
    [Arguments]    @{acute_symptoms}
    FOR    ${symptom}    IN    @{acute_symptoms}
        IF    '${symptom}'=='Chest Pain'
            Click Element    ${acute_symptoms_checkbox}chestPain
        ELSE IF    '${symptom}'=='Intractable Pain'
            Click Element    ${acute_symptoms_checkbox}intractablePain
        ELSE IF    '${symptom}'=='High-grade Fever'
            Click Element    ${acute_symptoms_checkbox}highGradeFever
        ELSE IF    '${symptom}'=='Trouble Breathing'
            Click Element    ${acute_symptoms_checkbox}troubleBreathing
        ELSE IF    '${symptom}'=='Currently Bleeding'
            Click Element    ${acute_symptoms_checkbox}currentlyBleeding
        END
    END

Input Case Notes
    [Arguments]    ${notes}
    Input Text    ${case_notes_field}    ${notes}

Input Medication Category
    [Arguments]    ${category}
    Try To Click Element    ${medication_category_dropdown}
    Try To Click ELement    //li[text()=" ${category} "]

Input Medication Name
    [Arguments]    ${name}
    Input Text    ${medication_name_field}    ${name}

Tick Narcotics Database Checked
    Wait Until Element Is Visible    ${narcotics_db_checked}
    Execute Javascript    document.getElementById('enabled-checkbox-narcoticsChecked').click()

Input Number Of Refills
    [Arguments]    ${number}
    Input Text    ${number_of_refills_field}    ${number}

Input Dose
    [Arguments]    ${number}
    Input Text    ${dose_field}    ${number}

Input Unit
    [Arguments]    ${unit}
    Select Option From Dropdown    ${unit_dropdown}    ${unit}

Input Frequency
    [Arguments]    ${frequency}
    Input Text    ${frequency_field}    ${frequency}

Input Prescribing Physician
    [Arguments]    ${physician}
    Try To Click Element    ${prescribing_physician_dropdown}
    Select Option From Dropdown    ${prescribing_physician_dropdown_list}    ${physician}

Click Case Management Next Button
    Click Element    ${case_next_button}

Set And Format Current Date Time
    [Documentation]    If case is edited, the created date is passed through and set to CREATED_DATE
    ...    variable, if the case new both created and updated dates are the same date time when the
    ...    case is created
    [Arguments]    ${created}=${EMPTY}
    ${datetime}    Nurse.PatientCase.Set And Format Current Date Time
    IF    '${created}' != '${EMPTY}'
        Set Suite Variable    ${CREATED_DATE}    ${created}
    ELSE
        Set Suite Variable    ${CREATED_DATE}    ${datetime}
    END
    Set Suite Variable    ${UPDATED_DATE}    ${datetime}

Fill In And Validate Medication Information Form
    [Documentation]    Fill in Medication Information form if case type == Medication Refill
    [Arguments]    @{additional_info_list}    &{medicatione_info_dict}
    Nurse.PatientCase.Fill In Medication Information Form    @{additional_info_list}    &{medicatione_info_dict}
    Validate Medication Refill Summary    ${additional_info_list}    &{medication_info_dict}

Validate Medication Refill Summary
    [Documentation]    Validate medication refill case summary before saving the case
    [Arguments]    ${additional_info_list}    &{medication_info_dict}
    ${titles}    ${values}    Nurse.PatientCase.Get Medication Refill Case Summary
    ${case_titles}    ${case_values}    Nurse.PatientCase.Get Ordered Dictionary    &{UPDATED_CASE_DETAILS_DICT}
    Remove From Dictionary    ${medication_info_dict}    Additional information
    ${medication_titles}    ${medication_values}    Nurse.PatientCase.Get Ordered Dictionary    &{medication_info_dict}
    ${expected_titles}    Combine Lists    ${case_titles}    ${medication_titles}
    ${expected_values}    Combine Lists    ${case_values}    ${medication_values}
    ${expected_titles}    ${expected_values}    Nurse.PatientCase.Insert Additional Info List
    ...    ${expected_titles}
    ...    ${expected_values}
    ...    ${additional_info_list}
    Lists Should Be Equal    ${expected_titles}    ${titles}
    Lists Should Be Equal    ${expected_values}    ${values}

Validate Case Row Details
    [Documentation]    Validate if the case row details under Patient Cases tab are correct
    [Arguments]    ${case}    ${action}    ${updated_case_summary}
    ${expected_case_row_details}    Nurse.PatientCase.Set Expected Case Row Details
    ...    ${CASE_MANAGEMENT}
    ...    ${CASE_TYPE}
    ...    ${action}
    ...    ${CREATED_DATE}
    ...    &{updated_case_summary}
    ${case_row_details}    Nurse.PatientCase.Get Case Row Details    ${case}
    Lists Should Be Equal    ${expected_case_row_details}    ${case_row_details}

Compare Main Case Details
    [Arguments]    ${updated_case_summary}
    ${main_case_details}    Nurse.PatientCase.Get Main Case Details
    Run Keyword And Continue On Failure
    ...    Dictionaries Should Be Equal
    ...    ${main_case_details}
    ...    ${updated_case_summary}

Compare Acute Symptoms
    [Arguments]    ${expected_symptoms}
    ${actual_symptoms}    Nurse.PatientCase.Get Acute Symptoms
    Lists Should Be Equal    ${actual_symptoms}    ${expected_symptoms}
    # TODO: To be updated after minor bug fix.
    # List should not be sorted and should show depending on how it's shown in the creation

Compare Case Notes
    [Arguments]    ${expected_notes}
    ${actuals_notes}    Nurse.PatientCase.Get Case Notes Details
    Dictionaries Should Be Equal    ${actuals_notes}    ${expected_notes}

Compare Medication Refill Info
    [Arguments]    ${case}    ${action}    ${expected_medication_info}    ${additional_info_list}
    ${medication_refill_info}    Nurse.PatientCase.Get Medication Refill Details
    IF    '${action}'=='Save'
        ${expected_med_info}    Create Dictionary    No information provided=${EMPTY}
    ELSE
        ${expected_med_info}    Nurse.PatientCase.Insert Additional Info Into Dict
        ...    ${additional_info_list}
        ...    &{expected_medication_info}
    END
    Dictionaries Should Be Equal    ${expected_med_info}    ${medication_refill_info}

Select Symptom And Contact Patient With
    [Arguments]    ${contact_type}    ${message_template}=none
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${contact_patient_button}
    IF    ${status}
        Contact Patient Per Case    ${contact_type}    ${message_template}
    ELSE
        Wait Until Page Contains Element    ${patient_cases_tab}
        Try To Click Element    ${patient_cases_tab}
        Wait Until Page Contains Element    ${open_patient_cases}    40s
        ${first_open_case}    Get WebElement    ${open_patient_cases}
        Try To Click Element    ${first_open_case}
        Try To Click Banner Message
        Contact Patient Per Case    ${contact_type}    ${message_template}
    END

Contact Patient Per Case
    [Documentation]    Set with_docs variable separately for PE document attachment
    [Arguments]    ${contact_type}    ${message_template}    ${message}=default
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible    ${contact_patient_button}    timeout=5s
    ${contact_patient_button}    Set Variable If    ${status}==True    ${contact_patient_button}
    ...    ${contact_patient_button_opened}
    Wait Until Page Contains Element    ${contact_patient_button}
    Try To Click Element    ${contact_patient_button}
    Wait Until Page Contains Element    ${contact_type_box}
    Sleep    1s
    Try To Click Element    ${contact_type_box}
    IF    '${contact_type}' == 'Question'
        Try To Click Element    ${contact_type_question}
    END
    IF    '${contact_type}' == 'Instructions'
        Try To Click Element    ${contact_type_instruction}
    END
    IF    '${contact_type}' == 'Invitation for clinical tests'
        Try To Click Element    ${contact_type_inv_ct}
    END
    IF    '${contact_type}' == 'Invitation for a physical examination'
        Try To Click Element    ${contact_type_inv_pe}
    END
    IF    '${contact_type}' == 'Patient Education'
        Try To Click Element    ${contact_type_pe}
        IF    '${with_docs}'=='yes'
            Click Add Documents
            Search Document Articles    Heart
            Select First Document In List
            Compare Selected Documents From Attached
            View Education Document In Clinic
            Save Education Document Attachment
            Verify Attached Document On Contact Patient Panel
        END
    END
    Wait Until Page Contains Element    ${contact_message_field}
    IF    '${contact_type}'=='Follow-up instruction'
        Try To Click Element    ${contact_type_followup}
    END
    IF    '${contact_type}'=='Follow-up instruction'
        Input Text    ${followup_duration_field}    2
    END
    ${random_string}    Generate Random String    70
    IF    '${message}'=='default'
        Set Test Variable    ${random_string}
    ELSE
        Set Test Variable    ${random_string}    ${message}
    END
    IF    '${message_template}'=='none'
        Input Text    ${contact_message_field}    ${random_string}
    ELSE
        Select Message Template To Contact Patient    ${message_template}
    END
    Try To Click Element    ${send_contact_button}
    Wait Until Page Does Not Contain Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}

Sent Response Is Displayed As Last In Discussion Chain And Is Unread
    [Arguments]    ${contact_type}
    Wait Until Page Contains Element    ${patient-messaging-content}
    Reload Page
    Last Message Is Unread    ${contact_type}

Last Message Is Unread
    [Arguments]    ${contact_type}
    Wait Until Page Contains Element    ${open_patient_cases}
    ${first_open_case}    Get WebElement    ${open_patient_cases}
    Scroll Element Into View    (${all_messages})[1]
    Wait Until Element Contains    (${all_messages})[1]    ${random_string}    5s
    Wait Until Element Contains    (${all_messages})[1]    ${contact_type}    5s
    Wait Until Element Contains    (${all_messages})[1]    Message unread by patient    5s

Open First Workqueue Item
    Wait Until Page Contains Element    ${first_work_queue_item}
    Try To Click Element    ${first_work_queue_item}

Open Other Symptom Case
    Try To Click Element    ${patient_cases_tab}
    Wait Until Page Contains Element    ${patient_messaging_content_other_symptom}
    ${first_other_symptom}    Get WebElement    ${patient_messaging_content_other_symptom}
    Try To Click Element    ${first_other_symptom}

Close Case
    Wait Until Element Is Visible    ${first_close_case_button}
    Wait Until Element Is Enabled    ${first_close_case_button}
    Try To Click Element    ${first_close_case_button}
    Run Keyword And Ignore Error    Wait Until Element Is Not Visible    ${first_close_case_button}    10s

Check Other Symptom Case Is Closed
    Reload Page
    Sleep    2s
    Wait Until Page Contains Element    ${closed_other_symptom_cases}
    ${first_closed_case}    Get WebElement    ${closed_other_symptom_cases}
    ${random_desc_6}    Get Substring    ${random_desc}    0    6
    ${case_open}    Run Keyword And Return Status    Page Should Contain    Case closed
    IF    '${case_open}'=='False'
        Try To Click Element    ${first_closed_case}
    END
    Wait Until Page Contains    ${random_desc_6}
    Page Should Contain    Case closed

Select Case From Patient Cases Tab
    [Arguments]    ${case_type}
    # selects the first instance that matches the case type
    Wait Until Page Contains Element    ${patient_cases_case_row}
    @{elements}    Get Webelements    ${patient_cases_case_row}
    FOR    ${element}    IN    @{elements}
        ${actual}    Get Text    ${element}
        IF    '${actual}'=='${case_type}'
            Click Element    ${element}
            BREAK
        END
    END

Case Status Is Correct
    [Arguments]    ${case_type}    ${case_status}
    # selects the first instance that matches the case type
    Sleep    1s
    Wait Until Page Contains Element    ${patient_cases_case_row}
    ${count}    Get Element Count    ${patient_cases_case_row}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${actual}    Get Text    (${patient_cases_case_row})[${INDEX}]
        IF    '${actual}'=='${case_type}'
            Selected Case Status Is Correct    ${INDEX}    ${case_status}
            BREAK
        END
    END

Case Assignee Is Correct
    [Arguments]    ${case_type}    ${assignee}
    Wait Until Page Contains Element    ${patient_cases_case_row}
    ${count}    Get Length    ${patient_cases_case_row}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${actual}    Get Text    (${patient_cases_case_row})[${INDEX}]
        IF    '${actual}'=='${case_type}'
            Selected Case Assignee Is Correct    ${INDEX}    ${assignee}
            BREAK
        END
    END

Case Is Unassigned
    [Documentation]    Checks that the case under patient cases tab has no assignee initials in the case title level
    [Arguments]    ${case_type}
    Wait Until Page Contains Element    ${patient_cases_case_row}
    Wait Until Page Does Not Contain Element    (${patient_cases_case_assignee})[1]

Case Is Assigned To Correct User
    [Documentation]    Please use this keyword to replace Case Assignee Is Correct
    [Arguments]    ${case_type}    ${assigned_to}
    ${assignee_element}    Format String    ${assigned_user_per_case}    ${case_type}
    Wait Until Element Is Visible    ${assignee_element}
    ${text}    Get Text    ${assignee_element}
    Should Be Equal    ${text}    ${assigned_to}

Selected Case Status Is Correct
    [Arguments]    ${index}    ${status}
    ${status_text}    Get Text    (${patient_cases_case_status})[${INDEX}]
    Should Be Equal    ${status_text}    ${status}

Selected Case Assignee Is Correct
    [Arguments]    ${index}    ${assignee}
    ${status_text}    Get Text    (${patient_cases_case_assignee})[${INDEX}]
    Should Be Equal    ${status_text}    ${assignee}

Select Case Outcome
    [Arguments]    ${case_outcome}
    Wait Until Element Is Visible    ${case_outcome_label}
    @{elements}    Get Webelements    ${case_outcome_checkbox}
    FOR    ${element}    IN    @{elements}
        ${actual}    Get Text    ${element}
        Wait Until Element Is Enabled    ${element}
        IF    '${actual}'=='${case_outcome}'
            Wait until keyword succeeds    22    2    Select Checkbox    ${element}
            BREAK
        END
    END

Select Reason For Delay
    [Arguments]    ${reason}
    Wait Until Element Is Enabled    ${reason_for_delay_dropdown}
    Try To Click Element    ${reason_for_delay_dropdown}
    @{elements}    Get Webelements    ${reason_for_delay_list}
    FOR    ${element}    IN    @{elements}
        ${actual}    Get Text    ${element}
        IF    '${actual}'=='${reason}'
            Click Element    ${element}
            BREAK
        END
    END

Close Case With Outcome
    [Arguments]    ${outcome}    ${case_type}=no    ${delay_reason}=no
    # Patient should be in the patient cases tab already
    Sleep    1
    IF    '${case_type}'!='no'
        Select Case From Patient Cases Tab    ${case_type}
    END
    Close Case
    Sleep    1
    Select Case Outcome    ${outcome}
    IF    '${delay_reason}'!='no'    Select Reason For Delay    ${delay_reason}
    Try To Click Element    ${close_case_modal}
    Sleep    1

Close Cases With Outcome Per Care Team
    [Arguments]    ${work_queue_tab}    ${care_team}
    IF    '${work_queue_tab}'=='open cases'
        Go To Open Cases Tab
        Select Case Type Filter In Work Queue    Select all
    ELSE
        Go To Patient Reports
        Select Questionnaire Type In Work Queue    Select all
    END
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${care_team}
    Click Card View Button
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    (${patient_cards_work_queue})[1]
    ...    timeout=5s
    IF    ${status}
        ${count}    Get Element Count    ${patient_cards_work_queue}
    ELSE
        ${count}    Set Variable    0
    END
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    (${patient_cards_work_queue})[1]
        IF    ${status}
            Try To Click Element    (${patient_cards_work_queue})[1]
            Close Case
            Select Case Outcome    Provider Consulted
            ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${reason_for_delay_dropdown}
            IF    ${status}
                Select Reason For Delay    After Hours
                Try To Click Element    ${close_case_modal}
            ELSE
                Try To Click Element    ${close_case_modal}
            END
            Return To Patients
        ELSE
            BREAK
        END
    END

Open A New Case For Analytics
    [Arguments]    ${patient_id}    ${type}    ${care_team}    ${assigned_to}    ${priority}    ${origin}
    ...    ${action}=Save
    Set To Dictionary
    ...    ${main_case_details_dict}
    ...    Case Type=${type}
    ...    Case Care Team=${care_team}
    ...    Assigned To=${assigned_to}
    ...    Case Priority=${priority}
    ...    Case Origin=${origin}
    Set Case Type    new    ${type}    &{main_case_details_dict}
    Nurse.PatientCase.Fill In Main Case Details
    ...    ${CASE_MANAGEMENT}
    ...    ${patient_id}
    ...    ${action}
    ...    ${acute_symptoms_list}
    ...    &{UPDATED_CASE_DETAILS_DICT}
    Set And Format Current Date Time
    IF    '${CASE_TYPE}'=='Medication Refill' and '${action}'=='Continue'
        Fill In And Validate Medication Information Form    @{additional_info_list}    &{medication_info_dict}
    END
    IF    '${action}'=='Continue'
        Nurse.PatientCase.Fill In Case Notes Details    ${CASE_MANAGEMENT}    &{case_notes_dict}
    END
    Wait Until Page Contains    Case created successfully

Case Summary Is Visible
    Wait Until Page Contains Element    ${case_summary_area}

Medication Summary Is Visible
    Wait Until Page Contains Element    ${medication_summary}

Case Symptom Summary Is Visible
    Wait Until Page Contains Element    ${case_symptom_summary}

First Case Type Is Correct
    [Documentation]    Verifies that the first case in patient cases tab is correct
    [Arguments]    ${case_type}
    Wait Until Page Contains Element    ${patient_cases_case_row}
    ${actual}    Get Text    (${patient_cases_case_row})[1]
    Should Be Equal    ${case_type}    ${actual}

First Case Status Is Correct
    [Documentation]    Verifies that the status of the first case in patient cases tab is correct
    [Arguments]    ${case_status}
    Wait Until Page Contains Element    ${patient_cases_case_status}
    Wait Until Element Is Visible    ${patient_cases_case_status}
    ${actual}    Get Text    (${patient_cases_case_status})[1]
    Should Be Equal    ${case_status}    ${actual}

Change Case Type
    Wait Until Element Is Visible    ${change_case_type_modal}
    Element Should Contain
    ...    ${change_case_type_modal}
    ...    Changing the case type will delete all information
    ...    that is specific to the current case type.
    Element Should Contain    ${change_case_type_modal}    Are you sure you want to change the case type?
    Click Element    ${change_type_button}

Select Case Symptom
    [Arguments]    ${symptom}
    ${element}    Format String    ${case_symptom_list}    ${symptom}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${element}
    Sleep    1
    Click Element    ${element}

Open A New Case
    [Documentation]    Set Case Details should be run before this
    [Arguments]    ${ssn}    ${action}    ${case_note_template}=no
    Search Patient By Identity Code    ${ssn}
    Wait Until Keyword Succeeds    15x    2s    Click New Case Button
    IF    '${case_note_template}'=='Yes'
        Input Case Details With Case Note Template    ${action}
    ELSE
        Input Case Details    ${action}
    END

Input Medication Refill Details
    [Documentation]    Set Medication Details should be run before this
    Input Medication Category    ${medication_details}[category]
    IF    '${medication_details}[category]'=='Controlled substance 1-2'
        Tick Narcotics Database Checked
    END
    Input Medication Name    ${medication_details}[medication_name]
    Input Number Of Refills    ${medication_details}[number_refills]
    Input Dose    ${medication_details}[dose]
    Input Unit    ${medication_details}[unit]
    Input Frequency    ${medication_details}[frequency]
    Input Prescribing Physician    ${medication_details}[physician]

Process The Case
    [Documentation]    Set Case Details should be run before this
    Click Process Case Button
    Click Continue Case Button
    Input Case Notes    ${case_details}[case_notes]
    Click Save And Exit Case Button
    Wait Until Page Contains    The patient case was successfully updated

Complete Case Symptom Details
    compare_questionnaires.Check And Write To Text Area
    Check And Select Symptom Date Today    Other symptom
    Check And Select Symptom Radio Buttons    Other symptom
    # TODO: Please modify soon to be applicable for other symptoms

Create Case Note
    [Documentation]    Clicks the case note button of the first open case. Reminder value is 'tomorrow'.
    [Arguments]    ${topic}    ${note}    ${reminder}=False
    Wait Until Page Contains Element    ${case_note_button}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${case_note_button}
    Try To Click Element    ${case_note_button}
    Sleep    1
    Input Text    ${case_topic_field}    ${topic}
    Input Text    ${case_note_field}    ${note}
    IF    '${reminder}'!='False'    Set Case Note Reminder    ${reminder}
    Click Element    ${case_note_save_button}
    Wait Until Element Contains    ${saved_case_note_content}    ${topic}
    Wait Until Element Contains    ${saved_case_note_content}    ${note}
    IF    '${reminder}'!='False'
        Wait Until Element Contains    ${saved_case_note_content}    Reminder: ${date}
    END

Edit Case Note
    [Documentation]    Assumes that case is already opened and edit case note button is visible
    [Arguments]    ${new_topic}    ${new_note}    ${reminder}=False
    Wait Until Page Contains Element    ${last_case_note_edit_button}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${last_case_note_edit_button}
    Try To Click Element    ${last_case_note_edit_button}
    Sleep    1
    Input Text    ${case_topic_field}    ${new_topic}
    Input Text    ${case_note_field}    ${new_note}
    IF    '${reminder}'!='False'    Set Case Note Reminder    ${reminder}
    Click Element    ${case_note_save_button}
    Wait Until Element Contains    ${saved_case_note_content}    ${new_topic}
    Wait Until Element Contains    ${saved_case_note_content}    ${new_note}
    IF    '${reminder}'!='False'
        Wait Until Element Contains    ${saved_case_note_content}    Reminder: ${date}
    END

Set Case Note Reminder
    [Arguments]    ${date}
    Try To Click Element    ${case_note_reminder_checkbox}
    Try To Click Element    ${case_note_reminder_date_input}
    Try To Input Text    ${case_note_reminder_date_input}    ${date}
    Set Test Variable    ${date}

Check If Case Reminder Is Unavailable
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${last_case_note_edit_button}
    Click Element    ${case_note_button}
    Wait Until Element Contains    ${case_reminder_unavailable_label}    Each case can have only one active reminder

Consult A Care Team
    [Arguments]    ${care_team}    ${care_person}    ${message}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${consult_a_care_team_button}
    Try To Click Element    ${consult_a_care_team_button}
    Try To Click Element    ${consult_care_team_field}
    Try To Input Text    ${consult_care_team_field}    ${care_team}
    Try To Click Element    ${consult_care_team_option}
    Try To Click Element    ${consult_care_person_field}
    Try To Input Text    ${consult_care_person_field}    ${care_person}
    Try To Click Element    ${consult_care_team_option}
    Try To Input Text    ${consult_message_field}    ${message}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${consult_button}
    Try To Click Element    ${consult_button}
    Wait Until Element Is Not Visible    ${consult_button}
    Wait Until Page Contains    Consultation sent

Reply To Consultation
    [Arguments]    ${reply_message}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${reply_to_consultation_button}
    Try To Click Element    ${reply_to_consultation_button}
    Input Text    ${consult_message_field}    ${reply_message}
    Try To Click Element    ${reply_button}

Cancel Consultation
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${cancel_consultation_button}
    Click Element    ${cancel_consultation_button}

Close Open Cases For Patient
    [Arguments]    ${patient_ssn}
    Wait Until Page Contains Element    ${patient_cases_case_status}
    ${status}    Page Should Not Contain Indicated Case Statuses
    IF    ${status}!=True    Close All Open Cases

Close All Open Cases
    ${count}    Get Element Count    ${patient_cases_case_status}
    FOR    ${status_index}    IN    @{index_list}
        ${status_index}    Evaluate    ${status_index}+1
        Try To Click Element    (${patient_cases_case_row})\[${status_index}]
        Close Case
    END

Navigate To Patient Cases Tab
    Wait Until Element Is Visible    ${patient_cases_tab}
    Try To Click Element    ${patient_cases_tab}

Page Should Not Contain Indicated Case Statuses
    @{not_exist_list}    Create List
    @{status_list}    Create List    Consultation answered    Consultation In Progress    In progress
    ...    Clinic replied    Open    Callback Requested
    FOR    ${status}    IN    @{status_list}
        @{statuses}    Get Actual Cases Statuses
        ${not_exist}    Run Keyword And Return Status
        ...    List Should Not Contain Value    ${statuses}    ${status}
        ${not_exist}    Convert To String    ${not_exist}
        Append To List    ${not_exist_list}    ${not_exist}
    END
    Log List    ${not_exist_list}
    ${status}    Run Keyword And Return Status    List Should Not Contain Value    ${not_exist_list}    False
    RETURN    ${status}

Get Actual Cases Statuses
    [Documentation]    Gets the cases statuses and put them in a list
    @{actual_status_elements}    Get WebElements    ${patient_cases_case_status}
    @{actual_status}    Create List
    @{index_list}    Create List
    FOR    ${status}    IN    @{actual_status_elements}
        ${text}    Get Text    ${status}
        Append To List    ${actual_status}    ${text}
    END
    ${size}    Get Length    ${actual_status}
    FOR    ${INDEX}    IN RANGE    0    ${size}
        IF    '${actual_status}[${INDEX}]'!='Closed'
            ${case_index}    Set Variable    ${INDEX}
        ELSE
            ${case_index}    Set Variable    ${None}
        END
        IF    '${actual_status}[${INDEX}]'!='Closed'
            Append To List    ${index_list}    ${case_index}
        END
    END
    Set Suite Variable    @{index_list}
    RETURN    @{actual_status}

Contact Patient
    [Documentation]    Contact patient non-case related (first contact patient button)
    [Arguments]    ${topic}    ${contact_type}    ${message_template}=none    ${pe_check}=no
    Sleep    1s
    Wait Until Element Is Enabled    ${non_case_related_contact_patient_button}
    Try To Click Element    ${non_case_related_contact_patient_button}
    Wait Until Element Is Visible    ${contact_type_list}
    IF    '${pe_check}'=='yes'
        Element Should Be Visible    ${add_documents_link}
    END
    Try To Input Text    ${contact_patient_topic_field}    ${topic}
    Try To Click Element    ${contact_type_list}
    Try To Click Element
    ...    //div[contains(@class, 'dropdown-panel-items')]/descendant::div[contains(text(),"${contact_type}")]
    Wait Until Page Contains Element    ${contact_message_field}
    IF    '${contact_type}'=='Patient Education'
        Wait Until Element Is Visible    ${patient_education_panel_message}
        Patient Education Tooltip Info Is Displayed
    END
    ${random_string}    Generate Random String    30
    Set Test Variable    ${random_string}
    IF    '${message_template}'=='none'
        Input Text    ${contact_message_field}    ${random_string}
    ELSE
        Select Message Template To Contact Patient    ${message_template}
    END
    Try To Click Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}
    Wait Until Element Is Not Visible    ${send_contact_button}

Contact Patient Button Is Disabled
    Wait Until Element Is Visible    ${non_case_related_contact_patient_button}
    Element Should Be Disabled    ${non_case_related_contact_patient_button}

Patient Education Tooltip Info Is Displayed
    Try To Click Element    ${contact_panel_tooltip}
    Wait Until Element Is Visible    ${patient_education_info_text}
    Try To Click Element    ${contact_panel_tooltip}

Case Is Not Expanded
    Wait Until Element Is Visible    ${patient_cases_case_row}
    Wait Until Keyword Succeeds    10s    1s    Element Attribute Should Not Contain "open"

Element Attribute Should Not Contain "open"
    ${attr}    Get Element Attribute    ${patient_cases_case_row}/../..    class
    Should Not Contain    ${attr}    open

Close Open Cases
    [Documentation]    usually used as test cleanup to make sure no open cases exist before test begins
    [Arguments]    ${ssn}    ${nurse_email}=${automated_tests_clinic}[default_user]
    IF    '${nurse_email}'!='none'    Login As Nurse    ${nurse_email}
    Search Patient By Identity Code    ${ssn}
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${patient_cases_list}
    ...    timeout=5s
    IF    ${status}==${FALSE}    Navigate To Patient Cases Tab
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${patient_cases_case_status}
    ...    timeout=7s
    IF    ${status}    Close Open Cases For Patient    ${ssn}

Close Open Cases Per Care Team
    [Arguments]    ${care_team}    ${nurse_email}=${automated_tests_clinic}[default_user]    ${tab}=Open Cases Tab    ${questionnaire_type}=default
    IF    '${nurse_email}'!='none'    Login As Nurse    ${nurse_email}
    IF    '${tab}'=='Patient Reports'    Go To Patient Reports
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${care_team}
    IF    '${questionnaire_type}'!='default'   Select Questionnaire Type In Work Queue    ${questionnaire_type}
    IF    '${tab}'=='Open Cases Tab'    Select Case Type Filter In Work Queue    Select all
    Sleep    1
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    (${patient_cards_work_queue})[1]
    ...    timeout=5s
    IF    ${status}
        ${count}    Get Element Count    ${patient_cards_work_queue}
    ELSE
        ${count}    Set Variable    0
    END
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    (${patient_cards_work_queue})[1]
        IF    ${status}
            Capture Screenshot    #added to see which cases are in the wq before selecting
            Try To Click Element    (${patient_cards_work_queue})[1]
            Acknowledge Attachment Disclaimer
            Wait Until Element Is Not Visible    ${noona-loader}
            Close Case
            Return To Patients
        ELSE
            BREAK
        END
    END

Unassign Nurse From Case
    [Documentation]    Nurse is already in patient cases tab and case is already opened
    Wait Until Element Is Enabled    ${assign_unassign_button}
    Try To Click Element    ${assign_unassign_button}
    Wait Until Page Contains    Case unassigned

Assign Nurse To Case
    [Documentation]    Nurse is already in patient cases tab and case is already opened
    Wait Until Element Is Enabled    ${assign_unassign_button}
    Try To Click Element    ${assign_unassign_button}
    Wait Until Page Contains    Case assigned successfully

Input Case Details With Case Note Template
    [Documentation]    Keyword Set Case Details should be run before this
    [Arguments]    ${action}
    Select Case Type    ${case_details}[case_type]
    Select Care Team    ${case_details}[care_team]
    Sleep    1
    ${assigned_to_exists}    Run Keyword And Return Status    Should Not Be Empty    ${case_details}[assigned_to]
    IF    ${assigned_to_exists}
        Select Clinic User    ${case_details}[assigned_to]
    END
    Input Case Description    ${case_details}[case_description]
    IF    '${case_details}[case_type]'=='Symptom Management'
        Select Acute Symptoms    @{acute_symptoms}
    END
    Select Case Priority    ${case_details}[case_priority]
    Select Case Origin    ${case_details}[case_origin]
    IF    '${action}'=='continue'
        Click Continue Case Button
    ELSE IF    '${action}'=='save'
        Click Save And Exit Case Button
    END
    Use Case Note Template

Use Case Note Template
    Try To Input Text    ${case_note_template_input}    ${automation_note_template_title}
    Wait Until Page Contains Element    ${case_note_template_panel}
    Element Should Contain    ${case_note_template_panel}    ${automation_note_template_title}
    Try To Click Element    ${case_note_template_panel}
    ${case_note_value}    Get Value    ${case_note_text_area}
    Should Be Equal    ${case_note_value}    ${automation_note_template_content}

Verify Case Note Text
    Wait Until Page Contains Element    ${expanded_case_note}
    Try For Element Should Contain    ${expanded_case_note}    ${automation_note_template_content}

Create Case Note With Template
    Wait Until Page Contains Element    ${case_note_button}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${case_note_button}
    Try To Click Element    ${case_note_button}
    Wait Until Page Contains Element    ${topic_input_field}
    Try To Click Element    ${note_template_dropdown}
    Try To Input Text    ${note_template_input}    ${automation_note_template_title}
    Try To Click Element    ${first_template_option}
    Try To Click Element    ${case_note_save_button}
    Wait Until Page Contains    Note added
    Try To Click Banner Message

Verify Case Note With Template In A Case
    Wait Until Page Contains Element    ${expanded_case_internal_message}
    Element Should Contain    ${expanded_case_internal_message}    ${automation_note_template_title}
    Element Should Contain    ${expanded_case_internal_message}    ${automation_note_template_content}

Contact Patient Text Area Is Uneditable
    Wait Until Element Is Visible    ${contact_message_field}
    Wait Until Page Contains Element    ${disabled_contact_patient_text_area}
    Wait Until Page Contains Element    ${disabled_add_documents_link}

Latest Case Has Correct Type And Date
    [Arguments]    ${type}    ${date}
    Wait Until Element Is Visible    ${patient_cases_case_row}
    ${actual_date}    Get Text    (//div[contains(text(),"${type}")])[1]/following-sibling::div[@class='case-date']
    Should Contain    ${actual_date}    ${date}

Get Symptoms From Symptom Report Case
    Wait Until Page Contains Element    ${symptom_report_symptom_row}
    ${symptom_rows_count}    Get Element Count    ${symptom_report_symptom_row}
    Set Test Variable    ${symptom_rows_count}
    ${reported_symptoms_list}    Create List
    FOR    ${index}    IN RANGE    1    ${symptom_rows_count} + 1
        ${symptom}    Get Text    (${symptom_report_symptom_title})[${index}]
        Append To List    ${reported_symptoms_list}    ${symptom}
    END
    RETURN    ${reported_symptoms_list}

Expand A Single Symptom From A Symptom Report
    [Documentation]    ${symptom_name} takes the symptom id name not the UI name. See above kw "Clinic User Can Click To Expand Reported Symptom One By One"
    [Arguments]    ${symptom_name}    ${with_photo}=no
    Try To Click Element    //*[@data-testid="${symptom_name}"]//mat-expansion-panel-header
    IF    '${with_photo}'=='yes'
        Wait Until Element Is Visible    ${image_id}
        Try To Click Element   //photo-uploader${image_id}
    END

Attached Photo From A Reported Symptom Can Be Viewed In Fullscreen And Closed
    Wait Until Element Is Visible    ${noona_photo_preview}
    Wait Until Element Is Visible    ${noona_photo_preview_uploaded_photo}
    Try To Click Element    ${noona_photo_preview_toggle_fullscreen_button}
    Try To Click Element    ${noona_photo_preview_x_close_button}

Acknowledge Attachment Disclaimer
    ${acknowledgement_status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${attachment_acknowledgement_button}
    IF    ${acknowledgement_status}
        Try To Click Element    ${attachment_acknowledgement_button}
    END

Acute Symptoms Are Visible In Case Summary
    [Arguments]    @{expected_acute_symptoms}
    Wait Until Element Is Visible    (${acute_symptoms_list})[1]
    ${count}    Get Element Count    ${acute_symptoms_list}
    @{actual_acute_symptoms}    Create List
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    (${acute_symptoms_list})[${INDEX}]
        Append To List    ${actual_acute_symptoms}    ${text}
    END
    Lists Should Be Equal    ${expected_acute_symptoms}    ${actual_acute_symptoms}