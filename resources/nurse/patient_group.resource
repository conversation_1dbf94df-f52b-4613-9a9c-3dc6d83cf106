*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${patient_groups_link}                      //*[@id="clinic-patient-category"]
${add_category_button}                      //button[contains(text(), "Add category")]
${last_add_group_button}                    (//button[contains(text(), "Add group")])[last()]
${category_input_field}                     //input[contains(@placeholder, "Category name")]
${group_input_field}                        //input[contains(@placeholder, "Group name")]
${last_category_input_field}                (//input[contains(@placeholder, "Category name")])[last()]
${last_group_input_field}                   (//input[contains(@placeholder, "Group name")])[last()]
${last_color_select_dropdown}               (//*[contains(@id, "color-select")])[last()]
${first_color_select_dropdown}              (//*[contains(@id, "color-select")])[last()]//div[contains(@id, "-0")]
${save_button}                              //button[contains(text(),'Save')]
${remove_button}                            //button[contains(text(), "Remove category")]
${last_remove_group_button}                 (//button[.=" Remove "])[last()]
${confirm_remove_button}                    //*[@id="confirm-pg-delete-yes"]
${open_groups_button}                       //button[contains(text(), "Open group")]
${patient_group_header}                     //noona-manage-patient-group
${work_queue_link}                          //*[@id="work-queue-link"]
${patient_group_modal_dropdown_arrow}       //*[@title='Clear all']/following-sibling::span
${open_groups_link}                         //button[text()='Open groups']


*** Keywords ***
Navigate To Work Queue
    Try To Click Element    ${back_to_button}
    Try To Click Element    ${work_queue_link}

Navigate To Patient Groups
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Page Does Not Contain Element    ${loader}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${patient_groups_link}

Add New Category
    ${status}    Run Keyword And Return Status    Page Should Contain    ${category_input_field}
    IF    '${status}' == 'False'
        Try To Click Element    ${add_category_button}
    END
    ${random_cat_name}    Generate Random String
    Set Test Variable    ${random_cat_name}
    Try To Input Text    ${last_category_input_field}    ${random_cat_name}

Set Category Color
    Try To Click Element    ${last_color_select_dropdown}
    Try To Click Element    ${first_color_select_dropdown}

Add Group To Category
    ${random_group_name}    Generate Random String
    Set Test Variable    ${random_group_name}
    Try To Input Text    ${last_group_input_field}    ${random_group_name}

Add New Group
    Try To Click Element    ${last_add_group_button}
    ${random_group_name_2}    Generate Random String
    Set Test Variable    ${random_group_name_2}
    Try To Input Text    ${last_group_input_field}    ${random_group_name_2}

Save Changes
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Patient groups saved
    Try To Click Banner Message
    Wait Until Page Does Not Contain    Patient groups saved

Changes Are Saved
    Reload Page
    Wait For Element To Be Present    ${last_category_input_field}
    Wait Until Page Contains Element    ${last_category_input_field}
    @{category_input_fields}    Get WebElements    ${category_input_field}
    ${category_names}    Create List    ${EMPTY}
    FOR    ${cat_input_field}    IN    @{category_input_fields}
        ${cat_text}    Get Value    ${cat_input_field}
        Append To List    ${category_names}    ${cat_text}
    END
    Should Contain    ${category_names}    ${random_cat_name}
    @{group_input_fields}    Get WebElements    ${group_input_field}
    ${group_names}    Create List    ${EMPTY}
    FOR    ${group_input_field}    IN    @{group_input_fields}
        ${group_text}    Get Value    ${group_input_field}
        Append To List    ${group_names}    ${group_text}
    END
    Should Contain    ${group_names}    ${random_group_name}
    Should Contain    ${group_names}    ${random_group_name_2}

Remove Category
    Try To Click Element    ${remove_button}
    Try To Click Element    ${confirm_remove_button}

Edit Group
    ${random_group_name_2}    Generate Random String
    Set Test Variable    ${random_group_name_2}
    Try To Input Text    ${last_group_input_field}    ${random_group_name_2}

Remove Group
    Try To Click Element    ${last_remove_group_button}
    Try To Click Element    ${confirm_remove_button}

Group Removed
    Reload Page
    Wait For Element To Be Present    ${last_category_input_field}
    Wait Until Page Contains Element    ${last_category_input_field}
    @{group_input_fields}    Get WebElements    ${group_input_field}
    ${group_names}    Create List    ${EMPTY}
    FOR    ${group_input_field}    IN    @{group_input_fields}
        ${group_text}    Get Value    ${group_input_field}
        Append To List    ${group_names}    ${group_text}
    END
    Should Not Contain    ${group_names}    ${random_group_name}

Add Patient To Groups
    Wait Until Page Contains Element    ${open_groups_button}    7s
    Try To Click Element    ${open_groups_button}
    Try To Click Element    //div//p[contains(text(), "${random_cat_name}")]/..//ng-select//div
    Try To Click Element    //div//span[contains(text(), "${random_group_name}")]
    Try To Click Element    ${patient_group_modal_dropdown_arrow}
    Try To Click Element    //div//span[contains(text(), "${random_group_name_2}")]
    Try To Click Element    ${save_button}

Patient Is Added To Groups
    Wait Until Page Contains Element    ${patient_group_header}
    Try For Element Should Contain    ${patient_group_header}    ${random_group_name}
    Try For Element Should Contain    ${patient_group_header}    ${random_group_name_2}

Remove Groups From Patient
    Try To Click Element    ${open_groups_button}
    Wait Until Page Contains Element    ${save_button}
    Try To Click Element
    ...    //div[contains(@class, "group-container")]//*[normalize-space(text()) = '${random_group_name}']//span[contains(@class, "remove")]
    Try To Click Element
    ...    //div[contains(@class, "group-container")]//*[normalize-space(text()) = '${random_group_name_2}']//span[contains(@class, "remove")]
    Try To Click Element    ${save_button}

Patient Is Removed From The Group
    Wait Until Page Contains Element    ${patient_group_header}
    Sleep    1s
    Element Should Not Contain    ${patient_group_header}    ${random_group_name}
    Element Should Not Contain    ${patient_group_header}    ${random_group_name_2}

# Keywords below are not used until https://adelmann.atlassian.net/browse/NOONA-10435 is fixed.

Remove Category v2
    @{remove_buttons}    Get WebElements    ${remove_button}
    FOR    ${button}    IN    @{remove_buttons}
        Try To Click Element    ${button}
        Try To Click Element    ${confirm_remove_button}
    END

Category Removed
    Reload Page
    Wait Until Page Contains Element    ${last_category_input_field}
    @{category_input_fields}    Get WebElements    ${category_input_field}
    ${category_names}    Create List    ${EMPTY}
    FOR    ${cat_input_field}    IN    @{category_input_fields}
        ${cat_text}    Get Value    ${cat_input_field}
        Append To List    ${category_names}    ${cat_text}
    END
    Should Contain    ${category_names}    ${random_cat_name}
    @{group_input_fields}    Get WebElements    ${group_input_field}
    ${group_names}    Create List    ${EMPTY}
    FOR    ${group_input_field}    IN    @{group_input_fields}
        ${group_text}    Get Value    ${group_input_field}
        Append To List    ${group_names}    ${group_text}
    END
    Should Not Contain    ${group_names}    ${random_group_name}
    Should Not Contain    ${group_names}    ${random_group_name_2}
