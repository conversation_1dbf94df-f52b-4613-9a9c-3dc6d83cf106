*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource

Library     String
Library     SeleniumLibrary
Library     DateTime


*** Variables ***
${patients_page_header}                                     //*[@id="manage-patients"]/div[1]/h2
${send_login_link_button}                                   //*[@data-testid="send-login-link"]
${status_entry}                                             //div[contains(@class, "status-entry")][2]
${user_status_entry}                                        //div[contains(@class, "status-entry")][1]
${additional_user_status_entry}                             //div[contains(@class, "status-entry")][3]
${status_has_changed_text}                                  Status has changed
${sms_invitations_tabs}                                     //*[@id="patient-invitation-list"]
${sms_invitations_status_selection_box}                     //*[contains(@data-testid,"invitations__status")]
${sms_invitations_statuses_list}                            ${sms_invitations_status_selection_box}//ng-dropdown-panel
${sms_invitations_select_all_option}                        ${sms_invitations_statuses_list}//*[@data-testid="btn-selectAll"]
${sms_invitations_remove_all_option}                        ${sms_invitations_statuses_list}//*[@data-testid="btn-removeAll"]
${sms_invitations_invitation_reminder_sent}                 //*[@title="Invitation reminder sent"]
${sms_invitations_invitation_expired}                       //*[@title="Invitation expired"]
${sms_invitations_invitation_declined}                      //*[@title="Declined"]
${sms_invitations_invitation_time_scheduled}                //*[@title="Invitation time scheduled"]
${sms_invitations_invitation_sending_invitation_failed}     //*[@title="Sending invitation failed"]
${sms_invitations_invitation_username_sent}                 //*[@title="Username sent"]
${sms_invitations_invitation_invitation_sent}               //*[@title="Invitation sent"]
${patient_list_tab}                                         //*[@id="patient-list"]
${candidates_tab}                                           //div[@data-testid="patient-integration-list"]
${candidates_table}                                         //*[@data-testid="candidates-table"]
${candidates_invitation_dialog}                             //*[@data-testid="invite-patient-dialog"]
${candidates_invitation_invite_button}                      //*[@data-testid="invite-patient"]
${candidates_invitation_diagnosis_field}                    //*[@data-testid="currentDiagnosis"]//input
${invite_patient_button}                                    //*[@id="open-invite-patient"]
${patient_ssn_field}                                        //*[@id="ssn"]
${first_name_field}                                         //*[@id="fname"]
${last_name_field}                                          //*[@id="lname"]
${phone_number_field}                                       //*[@data-testid="phoneNumber"]
${phone_number}                                             +358555666777888
${classification_dropdown}                                  //*[text()='Search for a diagnosis']/..
${classification_dropdown_first_option}                     //ul[starts-with(@class, 'dropdown-menu dropdown-options')]/li[1]
${invitation_link_id_first_option}                          //ul[starts-with(@class, 'dropdown-menu dropdown-options')]/li[1]
${schedule_template_dropdown}                               //*[@data-testid="questionnaire-template"]//input
${treatment_module_field}                                   //*[@data-testid="treatment-module"]
${treatment_module_dropdown}                                ${treatment_module_field}//input
${care_team_link}                                           module-responsible-units-add-button
${care_team_dropdown}                                       //*[@data-testid="responsible-unit"]//input
${care_team_dropdown_options}
...                                                         //*[@data-testid="responsible-unit"]//div[contains(@class, "ng-option")]
${care_team_dropdown_option}                                //*[@data-testid='option-1']
${send_invite_button}                                       invite-patient
${gender_radio_button_female}                               //label[contains(@for, "gender-female")]
${first_patient_invitation_row}                             //*[@id="patient-0"]
${invitation_scheduled_text}                                Invitation time scheduled
${invitation_sent_text}                                     Invitation sent
${edit_link}                                                //*[@id='action-edit-patient']
${edit_sms-invitation_link}                                 action-edit-patient
${mark_as_declined_button}                                  //*[@id="mark-as-declined"]
${invitation_declined_text}                                 Declined
&{patient_dict}
...                                                         social security number=1111111
...                                                         first name=John
...                                                         last name=Watson
...                                                         gender=Male
...                                                         date of birth=05.03.1983
...                                                         email=<EMAIL>
...                                                         mobile type=Mobile
...                                                         mobile number=+35800000
...                                                         icd-10=C00&
...                                                         treatment module=4
...                                                         responsible care team=Care Team 1
&{patient_dict_template}
...                                                         social security number=1111111
...                                                         first name=John
...                                                         last name=Watson
...                                                         gender=Male
...                                                         date of birth=05.03.1983
...                                                         email=<EMAIL>
...                                                         mobile type=Mobile
...                                                         mobile number=+35800000
...                                                         icd-10=C00&
${questionnaire}                                            Baseline questionnaire
${questionnaire_status}                                     SCHEDULED
${schedule_template_name}                                   Auto test template
${care_team}                                                Care Team 1
${date_format}                                              %d%m%Y
${date_format_2}                                            %d%m%Y
${add_time_to_date}                                         2 days
${scheduled_inquiry_locator}                                //tbody[@role='rowgroup']
${diagnosis_tab}                                            //*[@data-testid="diagnosis"]
${selected_diagnosis_option}                                //*[@data-testid="diagnosisCode"]
${C00_diagnosis_text}                                       C00& Malignant neoplasm of lip
${C01_diagnosis_text}                                       C01& Malignant neoplasm of base of tongue
${diagnosis_form_dropdown}                                  //*[@id="diagnosisCode"]//*[@class="searchable-filter"]
${confirm_button}                                           //*[@id="ok-confirm"]
${save_button}                                              //*[@id="diagnosisForm-submit"]
${diagnosis_date_field}                                     //*[@data-testid="beginDate"]
${send_user_account_button}                                 //*[@id="send-account"]
${send_invitation_modal_send}                               send-button
${toast_message}                                            //div[@class='toast-message']
${patients_menu}                                            //*[@id="managepatients-link"]
${search_a_patient_field}                                   //*[@id="input-search-ssn-search"]
${general_information_tab}                                  //*[@data-testid="general-information"]
${User_status}                                              //*[@id="status-entry-content"]
${change_to_proxy_button}                                   //*[@data-testid="change-to-proxy"]
${declined_account_invite_button}                           //*[@id="declined-account-invite"]
${work_queue_menu_button}                                   //*[@id="work-queue-link"]
${change_password_button}                                   //*[@data-testid="change-password"]
${create_patient_tab}                                       //*[@data-testid="create-patient"]
${identity_code_field}                                      //*[@data-testid="ssn"]
${birthdate_field}                                          //*[@data-testid="birthDate"]
${female_radio_button}                                      //*[@data-testid='gender-female']//label
${male_radio_button}                                        //*[@data-testid='gender-male']//label
${fname_field}                                              //*[@data-testid="fname"]
${lname_field}                                              //*[@data-testid="lname"]
${email_field}                                              //*[@data-testid="email"]
${phone_number_field_sms_invitation}                        //*[@data-testid="phoneNumber"]
${phone_number_dropdown}                                    //*[@data-testid="phoneNumberType1"]/div
${diagnosis_field}                                          //*[@data-testid="diagnosis"]//input
${treatment_module_dropdown_options}                        //*[@data-testid="treatment-module"]//*[contains(@class, "ng-option")]
${create_patient_button}                                    //button[@id="create-patient"]
${unlock_patient_button}                                    //*[@data-testid="unlock"]
${save_patient_button}                                      //button[@data-testid='save-patient']
${new_password_input}                                       //*[@data-testid="password"]
${new_password_again_input}                                 //*[@data-testid="passwordConfirm"]
${save_password_button}                                     //*[@id="save-user"]
${proxy_checkbox}                                           //input[@id='nurse-controlled']/..
${treatment_module_dropdown_first_option}                   //*[@data-testid='option-0']
${work_queue_item}                                          workqueue-0
${empty_wq}                                                 No patients in the queue.
${gen_info_account_language}                                //div[text()="Language"]/following-sibling::div
${treatment_start_date_field}                               //*[@data-testid="treatment-start-date"]
${diagnosis_tab_diagnosis_dropdown}                         //*[@data-testid='diagnosisCode']/descendant::input
${multiclinic_popup_message}
...                                                         Email already used by another clinic. Verify that this is the same patient. Accounts will be linked (1):
${female_gender_radio_button}                               //*[@data-testid='gender_0']
${male_gender_radio_button}                                 //*[@data-testid='gender_1']
${email_address_field}                                      //*[@data-testid='emailAddress']
${medicalRecordNumber}                                      //*[@data-testid="medicalRecordNumber"]
${confirm_modal}                                            //*[contains(@class, 'modal-dialog')]
${no_results_text}                                          No results
${en_language}                                              //*[@data-testid='locale-en_GB']//label
${proxy_patient_checkbox}                                   //*[@data-testid="nurse-controlled"]
${list_of_patients_table}                                   list-patients-table
${gen_info_tab_pri_provider_dropdown}                       (//*[@data-testid='primary-provider']//span[contains(@class, 'arrow')])[1]
${gen_info_tab_pri_provider_option}                         //*[@data-testid='primary-provider']//div[contains(text(),"{}")]
${label-patient-updated}                                    Patient updated
${patient_dob_header}                                       //div[text()="Date of birth"]/following-sibling::div
${ssn_header_create_patient}                                //div[text()[contains(., 'Identity code / Social security number')]]
${ssn_header_1_list_of_patients_page}                       (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'Identity code')])[1]
${ssn_header_2_list_of_patients_page}                       (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'Identity code')])[2]
${ssn_header_other_patient_tabs}                            (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'Identity code')])
${ssn_header_patient_profile}                               //div[@class='detail-label' and text()='Identity code']
${ssn_header_gen_info_tab}                                  //div[@class='form-label' and contains(text(), 'Identity code')]
${ins_header_create_patient}                                //div[text()[contains(., 'INS')]]
${ins_header_1_list_of_patients_page}                       (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'INS')])[1]
${ins_header_2_list_of_patients_page}                       (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'INS')])[2]
${ins_header_other_patient_tabs}                            (//th[contains(@class, 'cdk-column-medicalRecordNumber') and contains(text(), 'INS')])
${ins_header_patient_profile}                               //div[@class='detail-label' and text()='INS']
${ins_header_gen_info_tab}                                  //div[@class='form-label' and contains(text(), 'INS')]
${search_result_list_rows}                                  //*[@class='suggestion-list']/div
${search_result_suggestion_footer}                          //*[@class='suggestion-footer']
${patient_info_header}                                      //*[@class='patient-header']
${search_result_page_header}                                //h2[text()="Search results"]
${search_result_ssn}                                        //*[@id='table-noona-template']//span[text()="{}"]
${return_to_search}                                         //*[@id='back-to-button']//span[text()=" Return to search "]
${patient_name_header}                                      //*[@id='patient-name']
${search_results_last_names}                                //*[@id='table-noona-template']//td[1]
${search_results_last_name_column header}                   //*[@id='table-noona-template']//span[text()='Last name']
${multi_clinic_pop_container}                               //*[@class='multi-clinic-popup-contents__container']


*** Keywords ***
Navigate To Patient Page
    Try To Click Element    ${patients_menu}

Navigate To Candidates Tab
    Try To Click Element    ${patients_menu}
    Try To Click Element    ${candidates_tab}

Navigate To SMS-Invitations Tab
    Wait Until Element Is Visible    ${sms_invitations_tabs}
    Try To Click Element    ${sms_invitations_tabs}

Search Patient By Identity Code
    [Arguments]    ${ssn}
    ${case_displayed}    Run Keyword and Return status    Wait Until Page Contains Element    ${work_queue_item}    10s
    IF    ${case_displayed}
        Wait Until Element Is Enabled    ${work_queue_item}
    END
    ${in_workqueue}    Run Keyword And Return Status    Wait Until Element Is Visible    ${refresh_workqueue_button}
    IF    ${in_workqueue}==${FALSE}
        ${in_handle_patient}    Run Keyword and Return status
        ...    Wait Until Page Contains Element
        ...    ${back_to_button}
        ...    5s
        IF    ${in_handle_patient}
            Try To Click Element    ${back_to_button}
        ELSE
            Try To Click Element    ${work_queue_menu_button}
        END
    END
    Input Text    ${search_a_patient_field}    ${ssn}
    Sleep    2
    Press Keys    ${search_a_patient_field}    RETURN
    ${handle_patient}    Run Keyword And Return Status    Wait Until Location Contains    handle-patient
    IF    '${handle_patient}' == 'True'
        Log    Patient view is ready
    ELSE
        Click Element    ${search_result_suggestion_footer}
        Wait Until Location Contains    handle-patient
    END

Search Unactivated Patient By Identity Code
    [Arguments]    ${ssn}
    Wait Until Element Is Enabled    ${patients_menu}
    Sleep    1
    Wait Until Element Is Visible    ${search_a_patient_field}
    Input Text    ${search_a_patient_field}    ${ssn}
    Sleep    1
    Press Keys    ${search_a_patient_field}    RETURN
    ${status}    Run Keyword And Return Status    Wait Until Element Is Enabled    ${send_user_account_button}    15s
    IF    '${status}'=='False'
        Press Keys    ${search_a_patient_field}    RETURN
    END

Choose General Information Tab
    Wait Until Element Is Visible    ${general_information_tab}
    Try To Click Element    ${general_information_tab}

Navigate To Diagnosis-tab
    Wait Until Page Contains Element    ${diagnosis_tab}
    Try To Click Element    ${diagnosis_tab}

Change Password
    [Arguments]    ${password}=random
    ${random_string_password}    Generate Random String    15
    IF    '${password}'!='random'
        Set Test Variable    ${NEW_PASSWORD}    ${password}
    ELSE
        Set Test Variable    ${NEW_PASSWORD}    ${random_string_password}-1
    END
    Change Patient Password    ${NEW_PASSWORD}

Change Patient Password
    [Arguments]    ${password}
    Choose General Information Tab
    Wait Until Page Contains Element    ${change_password_button}
    Try To Click Element    ${change_password_button}
    Try To Input Text    ${new_password_input}    ${password}
    Try To Input Text    ${new_password_again_input}    ${password}
    Try To Click Element    ${save_password_button}
    Wait Until Page Contains    Password changed
    Try To Click Banner Message

Click Send New Login Link
    Wait Until Page Contains Element    ${send_login_link_button}
    Try To Click Element    ${send_login_link_button}

Status Has Changed Date Is Correct
    Wait Until Page Contains    ${status_has_changed_text}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Wait Until Page Contains    ${current_date}
    Element Should Contain    ${status_entry}    ${current_date}

Navigate To SMS-invitations
    Try To Click Element    ${sms_invitations_tabs}

Add Birthday
    ${current_date}    Get Current Date    result_format=%d%m%Y
    ${current_date_verify}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date_verify}
    Input Text    ${birthdate_field}    ${current_date}

Create New Patient With Template
    Login As Nurse
    Set New Patient Data With Random Data
    ${current_date}    Get Current Date
    ${future_date}    Add Time To Date    ${current_date}    ${add_time_to_date}
    ${future_date}    Convert Date    ${future_date}    result_format=${date_format}
    Set To Dictionary
    ...    ${patient_dict}
    ...    schedule template=${schedule_template_name}
    ...    treatment start date=${future_date}
    ...    icd-10 classification=${random_icd}
    Create New Patient With Robot

Create New Patient With Robot
    [Documentation]    User is already logged in. &{patient_dict} is already set beforehand.
    [Arguments]    ${proxy}=${FALSE}
    # checks if nurse is in create patient page
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${fname_field}    timeout=5s
    IF    ${status}==${FALSE}
        Try To Click Element    ${patients_menu}
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${fname_field}    timeout=5s
        IF    ${status}==${FALSE}
            Try To Click Element    ${create_patient_tab}
        END
    END
    # sets patient as proxy patient if indicated in the dict
    IF    ${proxy}    Try To Click Element    ${proxy_patient_checkbox}
    ${mrn_exist}    Run Keyword And Return Status    Element Should Be Visible    ${medicalRecordNumber}
    # checks if MRN exists and inputs MRN and id code, if not, value is inputted in identity code field only
    IF    ${mrn_exist}
        Try To Input Text    ${medicalRecordNumber}    ${patient_dict}[social security number]
        Try To Input Text    ${identity_code_field}    ${patient_dict}[social security number]
    ELSE IF    ${mrn_exist}==${FALSE}
        Try To Input Text    ${identity_code_field}    ${patient_dict}[social security number]
    END
    # sets birthday format
    ${dob}    Remove String    ${patient_dict}[date of birth]    .
    Try To Input Text    ${birthdate_field}    ${dob}
    # sets gender
    IF    '${patient_dict}[gender]'=='Female'
        Try To Click Element    ${female_radio_button}
    ELSE IF    '${patient_dict}[gender]'=='Male'
        Try To Click Element    ${male_radio_button}
    END
    # sets first and last name
    Try To Input Text    ${fname_field}    ${patient_dict}[first name]
    Try To Input Text    ${lname_field}    ${patient_dict}[last name]
    # sets phone number and email if not proxy
    IF    '${proxy}'=='${FALSE}'
        Try To Input Text    ${email_field}    ${patient_dict}[email]
        Try To Click Element    ${phone_number_dropdown}
        Try To Click Element    //div[@title='${patient_dict}[mobile type]']
        Try To Input Text    ${phone_number_field}    ${patient_dict}[mobile number]
    END
    # sets English language as default
    Try To Click Element    ${en_language}
    # sets diagnosis/icd
    Try To Input Text    ${diagnosis_field}    ${patient_dict}[icd-10 classification]
    Sleep    1
    Press Keys    ${diagnosis_field}    ENTER
    ${schedule_template_exists}    Run Keyword And Return Status
    ...    Dictionary Should Contain Key
    ...    ${patient_dict}
    ...    schedule template
    # sets schedule template if indicated in the dict
    IF    ${schedule_template_exists}
        Try To Click Element    ${schedule_template_dropdown}
        Try To Input Text    ${schedule_template_dropdown}    ${schedule_template_name}
        Press Keys    ${schedule_template_dropdown}    ENTER
        Try To Input Text    ${treatment_start_date_field}    ${patient_dict}[treatment start date]
    END
    # sets treatment module
    Try To Input Text    ${treatment_module_dropdown}    ${patient_dict}[module]
    Press Keys    ${treatment_module_dropdown}    ENTER
    # sets care team if patient has no schedule template
    IF    ${schedule_template_exists}==${FALSE}
        Try To Input Text    ${care_team_dropdown}    ${patient_dict}[care team]
        Press Keys    ${care_team_dropdown}    ENTER
    END
    Wait Until Element Is Enabled    ${create_patient_button}    5s
    Try To Click Element    ${create_patient_button}
    Wait Until Page Contains    New patient created
    Wait Until Page Does Not Contain    New patient created

Convert Date Of Birth JSON Format
    ##TODO: Converst UI date to JSON format YYYY-MM-DD
    ${date_of_birth_dash_format}    Replace String    ${patient_dict}[date of birth]    .    -
    ${date_of_birth_json_format}    Convert Date    ${date_of_birth_dash_format}    result_format=%Y-%m-%d
    Set Global Variable    ${date_of_birth_json_format}

Create New Multiclinic Patient With Robot
    [Documentation]    User is already logged in. &{patient_dict} is already set beforehand.
    [Arguments]    ${first_clinic}    ${proxy}=${FALSE}
    Try To Click Element    ${patients_menu}
    Try To Click Element    ${create_patient_tab}
    IF    ${proxy}    Try To Click Element    ${proxy_patient_checkbox}
    Try To Input Text    ${identity_code_field}    ${patient_dict}[social security number]
    ${dob}    Remove String    ${patient_dict}[date of birth]    .
    Try To Input Text    ${birthdate_field}    ${dob}
    IF    '${patient_dict}[gender]'=='Female'
        Try To Click Element    ${female_radio_button}
    ELSE IF    '${patient_dict}[gender]'=='Male'
        Try To Click Element    ${male_radio_button}
    END
    Try To Input Text    ${fname_field}    ${patient_dict}[first name]
    Try To Input Text    ${lname_field}    ${patient_dict}[last name]
    Try To Input Text    ${email_field}    ${patient_dict}[email]
    Wait Until Page Contains    ${multiclinic_popup_message}
    Wait Until Page Contains    ${first_clinic}
    Try To Click Element    ${en_language}
    Set Random Diagnosis
    Set Random Module
    Set Random Care Team
    IF    '${proxy}'=='${FALSE}'
        Try To Input Text    ${phone_number_field}    ${patient_dict}[mobile number]
        Try To Click Element    ${phone_number_dropdown}
        Press Keys    ${phone_number_dropdown}    ENTER
    END
    Try To Click Element    ${schedule_template_dropdown}
    ${schedule_template_dropdown_options}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${schedule_template_exists}    Run Keyword And Return Status
    ...    List Should Contain Value
    ...    ${schedule_template_dropdown_options}
    ...    ${schedule_template_name}
    IF    ${schedule_template_exists}
        Try To Click Element    ${schedule_template_dropdown}
        Try To Input Text    ${schedule_template_dropdown}    ${schedule_template_name}
        Press Keys    ${schedule_template_dropdown}    ENTER
        Try To Input Text    ${treatment_start_date_field}    ${patient_dict}[treatment start date]
    ELSE
        Press Keys    ${schedule_template_dropdown}    ESC
    END
    Wait Until Element Is Enabled    ${create_patient_button}
    Try To Click Element    ${create_patient_button}
    Wait Until Page Contains    New patient created

Update Patient Data With Valid Email
    [Arguments]    ${email}
    ${ssn}    Evaluate    "".join(random.choice(string.digits) for _ in range(8))    random, string
    ${ssn}    Set Variable    N${ssn}
    Set Test Variable    ${ssn}
    ${first_name}    FakerLibrary.First_Name_Male
    ${last_name}    FakerLibrary.Last_Name_Male
    Set Test Variable    ${email}
    Set To Dictionary
    ...    ${patient_dict}
    ...    social security number=${ssn}
    ...    first name=${first_name}
    ...    last name=${last_name}
    ...    email=${email}

Update Patient Data With Random Data
    ${ssn}    Evaluate    "".join(random.choice(string.digits) for _ in range(8))    random, string
    ${ssn}    Set Variable    N${ssn}
    Set Test Variable    ${ssn}
    ${first_name}    FakerLibrary.First_Name_Male
    ${last_name}    FakerLibrary.Last_Name_Male
    ${email}    Set Variable    ${first_name}.${last_name}@noona.com
    Set To Dictionary
    ...    ${patient_dict}
    ...    social security number=${ssn}
    ...    first name=${first_name}
    ...    last name=${last_name}
    ...    email=${email}

Set New Patient Data With Random Data
    ${ssn}    Evaluate    "".join(random.choice(string.digits) for _ in range(8))    random, string
    ${ssn}    Set Variable    N${ssn}
    Set Test Variable    ${ssn}
    ${first_name}    FakerLibrary.First_Name_Male
    Set Test Variable    ${first_name}
    # ${last_name}    FakerLibrary.Last_Name_Male
    ${last_name}    Generate Random String    7    [LETTERS]
    Set Test Variable    ${last_name}
    ${email}    Set Email For New User    ${first_name}    ${last_name}
    ${case_displayed}    Run Keyword and Return status    Wait Until Page Contains Element    ${work_queue_item}    10s
    IF    ${case_displayed}
        Wait Until Element Is Enabled    ${work_queue_item}
    END
    Try To Click Element    ${patients_menu}
    Get Random Diagnosis
    Get Random Module
    # Get Random Care Team
    ${current_date}    Get Current Date
    ${future_date}    Add Time To Date    ${current_date}    ${add_time_to_date}
    ${future_date}    Convert Date    ${future_date}    result_format=${date_format}
    Set To Dictionary
    ...    ${patient_dict}
    ...    social security number=${ssn}
    ...    first name=${first_name}
    ...    last name=${last_name}
    ...    email=${email}
    ...    mobile number=+35800000
    ...    icd-10 classification=${random_icd}
    ...    module=${random_module}
    ...    care team=Care Team 2

Set New Patient Random Data With Mailosaur Email
    [Documentation]    Mailosaur Server - Noona Automated SMS
    Set New Patient Data With Random Data
    ${email}    Set Variable    ${first_name}.${last_name}@rlfb5sb3.mailosaur.net
    ${email}    Convert To Lower Case    ${email}
    Set To Dictionary    ${patient_dict}    email=${email}    mobile number=+35800000

Set New Patient Mailosaur Email Server
    [Arguments]    ${mailosaur_server_id}
    Set New Patient Data With Random Data
    ${email}    Set Variable    ${first_name}.${last_name}@${mailosaur_server_id}.mailosaur.net
    ${email}    Convert To Lower Case    ${email}
    Set To Dictionary    ${patient_dict}    email=${email}    mobile number=+35800000

Set Random Diagnosis
    Try To Click Element    ${diagnosis_field}
    ${diagnosis_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${diagnosis_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_icd}    Get From List    ${diagnosis_list}    ${random_int}
    Try To Input Text    ${diagnosis_field}    ${random_icd}
    Press Keys    ${diagnosis_field}    ENTER

Set Random Diagnosis In Candidate Invitation
    Try To Click Element    ${candidates_invitation_diagnosis_field}
    ${diagnosis_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${diagnosis_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_icd}    Get From List    ${diagnosis_list}    ${random_int}
    Try To Input Text    ${candidates_invitation_diagnosis_field}    ${random_icd}
    Press Keys    ${candidates_invitation_diagnosis_field}    ENTER

Get Random Diagnosis
    Try To Click Element    ${diagnosis_field}
    ${diagnosis_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${diagnosis_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_icd}    Get From List    ${diagnosis_list}    ${random_int}
    Set Test Variable    ${random_icd}
    Try To Click Element    ${diagnosis_field}/../../following-sibling::span

Set Random Module
    Try To Click Element    ${treatment_module_dropdown}
    ${module_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${module_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_module}    Get From List    ${module_list}    ${random_int}
    Try To Input Text    ${treatment_module_dropdown}    ${random_module}
    Press Keys    ${treatment_module_dropdown}    ENTER

Get Random Module
    Try To Click Element    ${treatment_module_dropdown}
    ${module_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${module_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_module}    Get From List    ${module_list}    ${random_int}
    Set Test Variable    ${random_module}
    Try To Click Element    ${treatment_module_dropdown}/../../following-sibling::span

Set Random Care Team
    Try To Click Element    ${care_team_dropdown}
    ${care_team_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${care_team_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_care_team}    Get From List    ${care_team_list}    ${random_int}
    Try To Input Text    ${care_team_dropdown}    ${random_care_team}
    Press Keys    ${care_team_dropdown}    ENTER

Get Random Care Team
    Try To Click Element    ${care_team_dropdown}
    ${care_team_list}    Execute Javascript
    ...    return Array.from(document.querySelectorAll('.ng-option')).map(item => item.innerText)
    ${line_count}    Get Length    ${care_team_list}
    ${line_count}    Evaluate    ${line_count} - 1
    ${random_int}    Random Int    min=0    max=${line_count}
    ${random_care_team}    Get From List    ${care_team_list}    ${random_int}
    Set Test Variable    ${random_care_team}
    Try To Click Element    ${care_team_dropdown}/../../following-sibling::span

Verify Scheduled Questionnaire
    Navigate To Questionnaires Tab
    Wait Until Page Contains Element    //div[@class='module-name link']
    Element Should Contain    ${scheduled_inquiry_locator}    ${questionnaire}
    Element Should Contain    ${scheduled_inquiry_locator}    ${questionnaire_status}

Verify C00& Malignant neoplasm of lip Is Selected
    Wait Until Page Contains Element    ${selected_diagnosis_option}
    Wait Until Element Contains    ${selected_diagnosis_option}    ${C00_diagnosis_text}

Change Diagnosis To C01& Malignant neoplasm of base of tongue And Save
    Try To Click Element    ${diagnosis_form_dropdown}
    Sleep    1s
    Try To Input Text    ${diagnosis_tab_diagnosis_dropdown}    ${C01_diagnosis_text}
    Press Keys    ${diagnosis_tab_diagnosis_dropdown}    ENTER
    Wait Until Page Contains Element    ${confirm_button}
    Try To Click Element    ${confirm_button}
    Wait Until Page Contains Element    ${save_button}
    Wait Until Element Is Enabled    ${save_button}
    ${current_date_input}    Get Current Date    result_format=%-d
    Try To Input Text    ${diagnosis_date_field}    ${current_date_input}
    Press Keys    ${diagnosis_date_field}    TAB
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Changes saved
    Try To Click Banner Message

Verify The Diagnosis Change
    Choose General Information Tab
    Navigate To Diagnosis-tab
    Wait Until Page Contains Element    ${selected_diagnosis_option}
    Wait Until Page Does Not Contain    Changes saved
    Sleep    1
    ${text}    Execute Javascript    return document.getElementById('diagnosisCode').innerText;
    Should Be Equal    ${text}    ${C01_diagnosis_text}

Send User Account
    [Documentation]    For proxy patient, nurse is allowed to edit patient's email and contact number
    [Arguments]    ${edit_contact}=yes    ${input_phone}=no
    # User is in General Info Tab
    Wait Until Element Is Enabled    ${send_user_account_button}
    Maximize Browser Window
    Sleep    1
    Try To Click Element    ${send_user_account_button}
    IF    '${input_phone}'=='yes'
        Try To Input Text    (${phone_number_field})[2]    +*********
    END
    IF    '${edit_contact}'=='yes'
        Wait Until Element Is Enabled    ${send_invitation_modal_send}    5s
        Try To Click Element    ${send_invitation_modal_send}
    END
    Wait Until Page Contains    Patient updated

Return To Patients
    Wait Until Element Is Enabled    ${back_to_button}
    Try To Click Element    ${back_to_button}

Click Current Toast Message
    Try To Click Element    ${toast_message}

Verify Patient Status Is Proxy
    Try To Click Element    ${general_information_tab}
    Wait Until Page Contains Element    ${User_status}
    Element Should Contain    ${User_status}    Proxy

Change Patient Status To Proxy And Verify
    Try To Click Element    ${general_information_tab}
    Wait Until Page Contains Element    ${User_status}
    Element Should Contain    ${User_status}    Username sent
    Try To Click Element    ${change_to_proxy_button}
    Wait Until Keyword Succeeds    9s    0.3s    Element Should Contain    ${User_status}    Proxy

Patient Declined Account Invite And Send User Account Button Is Enabled
    Try To Click Element    ${general_information_tab}
    Wait Until Page Contains Element    ${declined_account_invite_button}
    Try To Click Element    ${declined_account_invite_button}
    Wait Until Keyword Succeeds
    ...    9s
    ...    0.3s
    ...    Element Should Contain
    ...    ${additional_user_status_entry}
    ...    Patient declined an account invite
    Element Should Be Enabled    ${send_user_account_button}

Go To Work Queue
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${back_to_button}
    IF    ${status}    Try To Click Element    ${back_to_button}
    Wait Until Element Is Enabled    ${work_queue_menu_button}
    Sleep    1s
    Try To Click Element    ${work_queue_menu_button}

Delete Patient Account
    [Arguments]    ${email}
    Wait Until Element Is Visible    ${patients_menu}
    Wait Until Element Is Enabled    ${patients_menu}
    Try To Click Element    ${patients_menu}
    Try To Click Element    ${patient_list_tab}
    VAR    ${delete_patient_link}    //td[contains(@class, "deleted-patient-list-email") and contains(text(), '${email}')]//../td[contains(text(), 'Delete patient')]
    Wait Until Keyword Succeeds
    ...    15s
    ...    1s
    ...    Click Element
    ...    ${delete_patient_link}
    Try To Click Element    ${confirm_button}
    Wait Until Noona Loader Is Not Visible    90s
    Wait Until Page Contains    Patient removed successfully    120s
    Try To Click Banner Message
    Wait Until Element Is Visible    ${search_a_patient_field}
    Patient Is Not Found Anymore

Patient Is Not Found Anymore
    Wait Until Element Is Visible    ${search_a_patient_field}
    Input Text    ${search_a_patient_field}    ${patient_ssn}
    Sleep    2
    Press Keys    ${search_a_patient_field}    RETURN
    Wait Until Page Contains    Patient not found    timeout=60s

Unlock Patient
    Try To Click Element    ${unlock_patient_button}
    Wait Until Page Contains    Patient unlocked
    Try To Click Banner Message

Verify Language Is Updated In General Info Tab
    Wait Until Element Is Visible    ${gen_info_account_language}
    ${general_info_language}    Get Text    ${gen_info_account_language}
    ##TODO: Get and compare language under "Edit info" modal
    # get document.querySelector('[id=mat-radio-23]').textContent
    Set Global Variable    ${general_info_language}
    Should Be Equal    ${general_info_language}    ${converted_language_value}

SMS Status Is Invitation Is Sent
    Wait Until Page Contains Element    ${first_patient_invitation_row}
    Wait Until Keyword Succeeds
    ...    10s
    ...    0.3s
    ...    Element Should Contain
    ...    ${first_patient_invitation_row}/td[1]
    ...    ${random_patient_ssn}
    Element Should Contain    ${first_patient_invitation_row}/td[2]    ${family_name}
    Element Should Contain    ${first_patient_invitation_row}/td[3]    Test
    Element Should Contain    ${first_patient_invitation_row}/td[5]    ${mailosaur_number}
    Element Should Contain    ${first_patient_invitation_row}/td[6]    ${current_date_verify}
    FOR    ${INDEX}    IN RANGE    1    40
        ${status}    Run Keyword And Return Status
        ...    Element Should Contain    ${first_patient_invitation_row}/td[7]    ${invitation_sent_text}
        IF    '${status}'=='${FALSE}'
            Reload Page
            Sleep    3
        END
        IF    '${status}'=='True'    BREAK
    END

Fill All Information With Valid Phone Number
    [Arguments]    ${phone_number}
    ${random_first_name}    Generate Random String    8    [LETTERS]
    ${random_last_name}    Generate Random String    8    [LETTERS]
    Set Test Variable    ${random_first_name}
    Set Test Variable    ${random_last_name}
    Set Test Variable    ${phone_number}
    Wait Until Page Contains Element    ${patient_ssn_field}
    ${random_patient_ssn}    Random Ssn
    Set Test Variable    ${random_patient_ssn}
    Wait Until Keyword Succeeds    20s    1s    Input Text    ${patient_ssn_field}    ${random_patient_ssn}
    Add Birthday
    Try To Click Element    ${first_name_field}
    Input Text    ${first_name_field}    ${random_first_name}
    Input Text    ${last_name_field}    ${random_last_name}
    Input Text    ${phone_number_field}    ${phone_number}
    Try To Click Element    ${gender_radio_button_female}
    Set Focus To Element    ${classification_dropdown}
    Try To Click Element    ${classification_dropdown}
    Try To Click Element    ${classification_dropdown_first_option}
    Try To Click Element    ${treatment_module_dropdown}
    Select From List By Index    ${treatment_module_dropdown_options}    1
    Try To Click Element    ${care_team_dropdown}
    Press Keys    ${care_team_dropdown}    ENTER
    Try To Click Element    ${send_invite_button}
    Wait Until Page Contains    Invitation sent
    Try To Click Element    ${toast_message}

Go To List Of Patients Tab
    Wait Until Element Is Visible    ${patient_list_tab}
    Try To Click Element    ${patient_list_tab}
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${list_of_patients_table}

Go To Patients > List Of Patients
    Navigate To Patient Page
    Go To List Of Patients Tab

Open A Candidate Invitation From Candidates Table
    [Documentation]    ${candidate_number} variable takes number string which indicates the n:th candidate on the candidates table, starting from 1
    [Arguments]    ${candidate_number}
    Wait Until Element Is Visible    ${candidates_table}
    Try To Click Element    ${candidates_table}//tbody[${candidate_number}]//td[7]
    Wait Until Page Contains Element    ${candidates_invitation_dialog}

Click Invite Candidate Patient
    [Documentation]    Use this keyword to send account invitation for integrated patient
    Wait Until Element Is Enabled    ${candidates_invitation_invite_button}
    Try To Click Element    ${candidates_invitation_invite_button}

Invitation Sent Toast Banner Is Visible And Then Disappears
    Wait Until Page Contains    ${invitation_sent_text}
    Wait Until Page Does Not Contain    ${invitation_sent_text}

Patient Status Should Be ${patient_status}
    [Documentation]    Use this keyword to check patient's status change for instance after sending invitation or from patient to a proxy one
    Wait Until Element Is Visible    //*[@id="status-entry-content"]
    ${status}    Get Text    //*[@id="status-entry-content"]
    Should Be Equal    ${status}    ${patient_status}

Add Primary Provider To Patient
    [Documentation]    Nurse should already be on patient profile page
    [Arguments]    ${provider}
    Choose General Information Tab
    Wait Until Element Is Visible    ${gen_info_tab_pri_provider_dropdown}
    Try To Click Element    ${gen_info_tab_pri_provider_dropdown}
    ${gen_info_tab_pri_provider_option}    Format String    ${gen_info_tab_pri_provider_option}    ${provider}
    Try To Click Element    ${gen_info_tab_pri_provider_option}
    Save Changes
    General information save confirmation is displayed

Save Changes
    Wait Until Element Is Enabled    ${save_patient_button}
    Try To Click Element    ${save_patient_button}

General information save confirmation is displayed
    Wait Until Element Is Visible    //div[@aria-label='${label-patient-updated}']
    Wait Until Element Is Not Visible    //div[@aria-label='${label-patient-updated}']

Verify If Module Is Displayed In the List
    [Arguments]    ${module}    ${is_displayed}
    Wait Until Element Is Visible    ${treatment_module_dropdown}
    Try To Click Element    ${treatment_module_dropdown}
    Wait Until Element Is Visible    //*[@title='${ABDOMINAL_RADIOTHERAPY}[name]']    #always existing for this clinic
    IF    '${is_displayed}'=='Yes'
        Wait Until Element Is Visible    //*[@title='${module}']    timeout=3s
    ELSE
        Element Should Not Be Visible    //*[@title='${module}']    timeout=3s
    END

Search Dropdown Result Rows Are Correct
    [Arguments]    ${expected_rows}
    Wait Until Element Is Visible    ${search_result_list_rows}\[last()]
    Sleep    2s    #needs to wait for dropdown to change
    ${count}    Get Element Count    ${search_result_list_rows}
    ${count}    Convert To String    ${count}
    Should Be Equal    ${expected_rows}    ${count}

Search Suggestion Footer Is Correct
    [Arguments]    ${number_of_patients}
    Wait Until Element Is Visible    ${search_result_suggestion_footer}
    ${text}    Get Text    ${search_result_suggestion_footer}
    Should Be Equal    ${text}    See all ${number_of_patients} results
