*** Settings ***
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${announcement_link}                    messaging-link
${announcement_title_field}             //*[@data-testid='title']
${announcement_expire_date}             //*[@data-testid='expiration']
${announcement_content}                 //*[@data-testid='content']
${care_team_checkboxes}                 //*[@data-testid='selectedCareTeam']
${checked_care_teams}                   //*[@data-testid='selectedCareTeam']//input[contains(@aria-checked, "true")]
${care_team_label}                      //*[@data-testid='careTeams']//input
${announcement_patient_count}           //label[@class='patient-count']
${announcement_patient_count_number}    //label[@class='patient-count']//span[@class='count']
${post_announcement_button}             update
${care_team_dropdown_list}              //*[@data-testid="careTeams"]//ng-dropdown-panel
${care_team_dropdown_arrow}             //*[@id="careTeams"]/div/ng-select/div/span


*** Keywords ***
Navigate To Announcements
    Wait Until Element Is Visible    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Click Element    ${announcement_link}

Input Announcement Title
    [Arguments]    ${title}
    Wait Until Element Is Visible    ${announcement_title_field}
    Try To Input Text    ${announcement_title_field}    ${title}

Input Expiration Date
    [Documentation]    -d.-m format
    [Arguments]    ${date}
    Input Text    ${announcement_expire_date}    ${date}
    Press Keys    ${announcement_expire_date}    TAB

Input Announcement Content
    [Arguments]    ${message}
    Input Text    ${announcement_content}    ${message}

Select Care Team
    [Arguments]    ${care_team}
    Try To Click Element    ${care_team_label}
    Try To Click Element    ${care_team_dropdown_list}//*[contains(text(),'${care_team}')]
    Try To Click Element    ${care_team_dropdown_arrow}

Patient Count For Announcement Is Correct
    Sleep    1s
    Scroll Element Into View    ${announcement_patient_count}
    ${text}    Get Text    ${announcement_patient_count}
    ${number}    Get Text    ${announcement_patient_count_number}
    Should Not Be Empty    ${number}
    Should Not Be Equal    ${number}    0
    Should Be Equal    ${number}${SPACE}Patients will see this announcement    ${text}

Post Announcement
    Scroll Element Into View    ${post_announcement_button}
    ${status}    Execute Javascript    return document.getElementById('${post_announcement_button}').disabled
    IF    ${status}==False
        Try To Click Element    ${post_announcement_button}
    END

Care Team Selections Should Be Disabled
    Sleep    1s
    Page Should Not Contain Element    ${checked_care_teams}

Input Announcement Details
    [Arguments]    ${title}    ${date}    ${care_team}
    Navigate To Announcements
    Input Announcement Title    ${title}
    Input Expiration Date    ${date}
    Input Announcement Content    This is a test announcement
    Select Care Team    ${care_team}
