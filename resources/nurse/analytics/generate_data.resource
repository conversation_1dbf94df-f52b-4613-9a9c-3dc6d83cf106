*** Settings ***
Documentation       Suite description

Library             ExcelLibrary
Library             OperatingSystem
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource


*** Variables ***
${pass}     ${EMPTY}    # computer's password
${email}    <EMAIL>
${path}     ${EXECDIR}${/}data${/}analytics${/}patients${/}patient_details.xlsx


*** Keywords ***
Write Patient Details In Excel
    [Arguments]    ${count}    ${path}=${path}    ${sheet}=patient_stories
    FOR    ${INDEX}    IN RANGE    0    ${count}
        ${row}    Evaluate    ${INDEX} + 2
        Generate Random Patient Data In Excel    ${row}    ${sheet}
    END
    Save Excel Document    ${path}

Generate Random Patient Data In Excel
    [Arguments]    ${row}    ${sheet}
    ${ssn}    Evaluate    "".join(random.choice(string.digits) for _ in range(8))    random, string
    ${ssn}    Set Variable    N${ssn}
    # TODO: Only creating patients for Care Team 1 and Care Team 2 as of the moment
    ${rand}    Random Int    1    2
    # TODO: Only selecting from Chemo 11 and Chemo 18 modules as of the moment
    # ${rand_module}    Random Int    9    10
    Set Patient Module
    Set Patient Diagnosis
    IF    '${rand}'=='1'
        ${first_name}    FakerLibrary.First_Name_Male
    ELSE
        ${first_name}    FakerLibrary.First_Name_Female
    END
    ${last_name}    FakerLibrary.Last_Name_Male
    ${email}    Set Variable    ${first_name}.${last_name}@zq0od94c.mailosaur.net
    ${email}    Convert To Lowercase    ${email}
    ${gender}    Set Variable If    '${rand}'=='1'    Male    Female
    ${dob}    Format DOB
    @{patient_details}    Create List    ${ssn}    ${first_name}    ${last_name}    ${gender}    ${dob}
    ...    ${email}    Mobile    00000    ${patient_diagnosis}    ${patient_module}
    FOR    ${INDEX}    IN RANGE    1    11
        ${list_index}    Evaluate    ${INDEX}-1
        ${value}    Get From List    ${patient_details}    ${list_index}
        Write Excel Cell    ${row}    ${INDEX}    ${value}    ${sheet}
    END

Format DOB
    ${dob}    FakerLibrary.Date_Of_Birth    maximum_age=100    minimum_age=10
    ${dob}    Convert To String    ${dob}
    ${year}    Get Substring    ${dob}    0    4
    ${month}    Get Substring    ${dob}    5    7
    ${day}    Get Substring    ${dob}    8    10
    RETURN    ${day}.${month}.${year}

Write Status In Excel
    [Arguments]    ${row}    ${result}    ${message}
    ${columns}    Read Excel Row    1    sheet_name=patient_stories
    ${column_result}    Get Index From List    ${columns}    Result
    ${column_message}    Get Index From List    ${columns}    Message
    ${column_result}    Evaluate    ${column_result}+1
    ${column_message}    Evaluate    ${column_message}+1
    ${row}    Evaluate    ${row}+1
    Write Excel Cell    ${row}    ${column_result}    ${result}    patient_stories
    Write Excel Cell    ${row}    ${column_message}    ${message}    patient_stories
    Save Excel Document    ${path}

Create A Patient
    # Get Patient Details should be run before this
    [Arguments]    ${creation_status}
    Get Action Date Time    patient created
    Update System Time    month=${MONTH}    year=${YEAR}
    Login As Nurse    ${email}
    ${proxy}    Set Variable If    '${creation_status}'=='proxy'    True    False
    Set Suite Variable    ${patient_dict}    ${patient_details}
    Create New Patient With Robot    proxy=${proxy}
    # Ends in Questionnaires tab

Create User Story
    ${patient_count}    Get Patient Count    patient_stories
    # Set SSN Index
    FOR    ${INDEX}    IN RANGE    1    ${patient_count}+1
        ${test_status}    ${test_message}    Run Keyword And Ignore Error    Run Patient Actions    ${INDEX}
        Write Status In Excel    ${INDEX}    ${test_status}    ${test_message}
        Close All Browsers
        # Revert Time To Current
    END

Run Patient Actions
    [Arguments]    ${index}
    Get Patient Details    ${index}    patient_stories
    ${creation}    Get From Dictionary    ${patient_details}    patient created
    ${activation}    Get From Dictionary    ${patient_details}    activated account
    ${action}    Get From Dictionary    ${patient_details}    active portal patient
    ${patient_reported}    Get From Dictionary    ${patient_details}    ePRO action
    ${case}    Get From Dictionary    ${patient_details}    active triage patient
    ${outcome}    Get From Dictionary    ${patient_details}    close case
    ${delay_reason}    Get From Dictionary    ${patient_details}    delay reason
    ${email}    Get From Dictionary    ${patient_details}    email
    ${message}    Get From Dictionary    ${patient_details}    message
    ${message_status}    Get From Dictionary    ${patient_details}    message status

    IF    '${creation}'!='${NONE}'    Create A Patient    ${creation}
    IF    '${activation}'!='${NONE}'
        Activate Patient's Account    ${activation}
    END
    IF    '${action}'!='${NONE}'
        Manage Portal Patient Account    ${action}    ${patient_reported}
    END
    IF    '${case}'!='${NONE}'    Open A Triage Case    ${case}
    IF    '${outcome}'!='${NONE}'
        Close A Triage Case    ${case}    ${outcome}    ${delay_reason}
    END

    IF    '${message}'!='${NONE}'
        Send a message actions NDS    ${message}    ${email}
    END
    IF    '${message_status}'!='${NONE}'
        Read a message actions NDS    ${message_status}    ${email}
    END

    # Send Message To A Clinic As Patient

Send a message actions NDS
    [Arguments]    ${message}    ${email}
    Get Action Date Time    message
    Update System Time    time=${TIME}    month=${MONTH}    year=${YEAR}

    ${contact_type}    Set Variable    ${message}

    IF    '${message}'=='Nurse sends a question'
        Contact Patient NDS    ${contact_type}
    ELSE IF    '${message}'=='Nurse sends instructions'
        Contact Patient NDS    ${contact_type}
    ELSE IF    '${message}'=='Nurse sends invitation for physical examination'
        Contact Patient NDS    ${contact_type}
    ELSE IF    '${message}'=='Nurse sends Patient Education message'
        Contact Patient NDS    ${contact_type}
    END

    Set Suite Variable    ${contact_type}
    Sleep    5

Read a message actions NDS
    [Arguments]    ${message_status}    ${email}
    Get Action Date Time    message status
    Update System Time    time=${TIME}    month=${MONTH}    year=${YEAR}

    ${contact_type}    Set Variable    ${message_status}

    IF    '${message_status}'=='Patient reads the message'
        Read a message NDS    ${email}
    ELSE IF    '${message_status}'=='Patient answers the question'
        Answer a question NDS    ${email}
    END

    Set Suite Variable    ${contact_type}
    Sleep    5

Answer a question NDS
    [Arguments]    ${email}
    Login As Patient    ${email}

    Wait For Element To Be Present    //nh-smart-symptom-inquiry
    Click Element    //nh-smart-symptom-inquiry

    Wait For Element To Be Present    //textarea
    Input Text    //textarea    Patient answer text sample

    Sleep    2
    Click Element    //*[@id="button-inbox-message-send-reply"]
    Sleep    5

Read a message NDS
    [Arguments]    ${email}
    Login As Patient    ${email}
    Wait For Element To Be Present    //nh-smart-symptom-inquiry
    Click Element    //nh-smart-symptom-inquiry

    Wait For Element To Be Present    //*[@ng-reflect-id="button-inbox-message-close"]
    Try To Click Element    //*[@ng-reflect-id="button-inbox-message-close"]

    Sleep    5

Contact Patient NDS
    [Arguments]    ${contact_type}

    Login As Nurse    ${email}
    Search Patient By Identity Code    ${CURRENT_SSN}
    Wait Until Element Is Visible    ${contact_patient_button_top}

    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible    ${contact_patient_button_top}    timeout=5s

    Wait Until Page Contains Element    ${contact_patient_button_top}
    Try To Click Element    ${contact_patient_button_top}
    Wait Until Page Contains Element    ${contact_type_box}
    Sleep    1s

    Try To Click Element    ${contact_type_box}

    IF    '${contact_type}' == 'Nurse sends a question'
        Try To Click Element    ${contact_type_question}
        Input Text    //*[@data-testid="contact-topic-input"]    Question from clinic
    END

    IF    '${contact_type}' == 'Nurse sends instructions'
        Try To Click Element    ${contact_type_instruction}
        Input Text    //*[@data-testid="contact-topic-input"]    Instructions from clinic
    END

    IF    '${contact_type}' == 'Nurse sends invitation for physical examination'
        Try To Click Element    ${contact_type_inv_pe}
        Input Text    //*[@data-testid="contact-topic-input"]    Invitation for a physical examination
    END

    # IF    '${contact_type}' == 'Invitation for clinical tests'
    #    Try To Click Element    ${contact_type_inv_ct}
    # END

    IF    '${contact_type}' == 'Nurse sends Patient Education message'
        Try To Click Element    ${contact_type_pe}
        Input Text    //*[@data-testid="contact-topic-input"]    Patient Education material
    END

    # IF    '${contact_type}'=='Follow-up instruction'
    #    Try To Click Element    ${contact_type_followup}
    # END
    # IF    '${contact_type}'=='Follow-up instruction'
    #    Input Text    ${followup_duration_field}    2
    # END

    # IF    '${message}'=='default'
    #    Set Test Variable    ${random_string}
    # ELSE
    #    Set Test Variable    ${random_string}    ${message}
    # END
    # IF    '${message_template}'=='none'

    # ELSE
    #    Select Message Template To Contact Patient    ${message_template}
    # END

    Wait Until Page Contains Element    ${contact_message_field}
    ${random_string}    Generate Random String    70
    Click Element    ${contact_message_field}
    Input Text    ${contact_message_field}    ${random_string}

    Try To Click Element    ${send_contact_button}

    Wait Until Page Does Not Contain Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}

Get Patient Count
    # Returns patients count in excel. Excel file should already be opened.
    [Arguments]    ${sheet_name}
    ${ssn_column}    Read Excel Column    1    sheet_name=${sheet_name}
    Remove Values From List    ${ssn_column}    social security number
    ${count}    Get Length    ${ssn_column}
    RETURN    ${count}

Set SSN Index
    ${patient_count}    Get Patient Count    order
    ${ssn_column}    Read Excel Column    1    sheet_name=patient_stories
    FOR    ${INDEX}    IN RANGE    1    ${patient_count}
        ${ssn}    Get From List    ${ssn_column}    ${INDEX}
        Set Suite Variable    ${SSN${INDEX}}    ${ssn}
    END

Activate Patient's Account
    [Arguments]    ${action}
    Get Action Date Time    activated account
    Update System Time    month=${MONTH}    year=${YEAR}
    IF    '${action}'=='Change Password'
        Change Patient's Password As Nurse
    ELSE IF    '${action}'=='Activation Link'
        Set Test Variable    @{mailosaur_keys}    zq0od94c    CGb4PNpTgSyvEISa
        ${link}    Patient Received Invitation To Use Noona    ${patient_dict}[email]    JC Clinic
        Activate Noona Account As Patient    ${link}    ${patient_dict}[email]
        Wait Until Location Contains    patient/#/diary-timeline
    END
    ##TODO: Include other ways to activate patient's account

Change Patient's Password As Nurse
    # User should be in General Info Tab
    Login As Nurse    ${email}
    Search Patient By Identity Code    ${CURRENT_SSN}
    Wait Until Element Is Visible    tab-general-information
    Try To Click Element    tab-general-information
    Sleep    1
    ${status}    Run Keyword And Return Status    Page Should Contain Element    send-account
    IF    ${status}
        Send User Account
        Wait Until Page Contains    Patient updated
    END
    Sleep    1
    Change Password    password=${DEFAULT_PASSWORD}
    Sleep    1

Manage Questionnaire As ePRO Patient
    [Arguments]    ${patient_reported}    ${new_date}=yes
    IF    '${new_date}'=='yes'
        Get Action Date Time    active portal patient
        Update System Time    month=${MONTH}    year=${YEAR}
    END
    IF    'Responds to questionnaire' in '${patient_reported}'
        Respond To A Questionnaire    ${patient_reported}
    ELSE IF    '${patient_reported}'=='Sends a symptom message'
        Ask About Symptoms As Patient    ${patient_details}[email]
    END
    ##TODO: adds a symptom to diary

Respond To A Questionnaire
    [Arguments]    ${patient_reported}
    Login As Nurse    ${email}
    ${respond_by}    Get From Dictionary    ${patient_details}    ePRO action
    ${questionnaire}    Get From Dictionary    ${patient_details}    ePRO questionnaire
    Search Patient By Identity Code    ${CURRENT_SSN}
    Add Questionnaire To Schedule    ${questionnaire}
    IF    'as patient' in '${patient_reported}'
        Respond To Questionnaire As Patient    ${patient_details}[email]    ${questionnaire}
    ELSE IF    'nurse' in '${patient_reported}'
        Respond To Questionnaire As Nurse    ${CURRENT_SSN}    ${questionnaire}
    END
    ##TODO: respond to questionnaire by a caregiver

Respond To Questionnaire As Nurse
    # TODO: Include other questionnaires, not only AEQ
    [Arguments]    ${patient_id}    ${questionnaire}
    ${list_of_symptoms}    Read Excel Column    1    sheet_name=symptom_questionnaires
    ${list_of_qol}    Read Excel Column    1    sheet_name=qol_questionnaires
    ${symptom_is_in_list}    Run Keyword And Return Status
    ...    List Should Contain Value
    ...    ${list_of_symptoms}
    ...    ${questionnaire}
    ${qol_is_in_list}    Run Keyword And Return Status
    ...    List Should Contain Value
    ...    ${list_of_qol}
    ...    ${questionnaire}
    IF    ${symptom_is_in_list}
        compare_questionnaires.Complete Symptom Questionnaire    ${questionnaire}    Other symptom
        compare_questionnaires.Save Symptom Questionnaire    ${questionnaire}
    ELSE IF    ${qol_is_in_list}
        Set Library Search Order    compare_questionnaires    questionnaires
        compare_questionnaires.Complete Questionnaire    ${questionnaire}
        compare_questionnaires.Save Questionnaire    ${questionnaire}
    END

Respond To Questionnaire As Patient
    [Arguments]    ${email}    ${questionnaire}
    Login As Patient    ${email}
    ${list_of_symptoms}    Read Excel Column    1    sheet_name=symptom_questionnaires
    ${list_of_qol}    Read Excel Column    1    sheet_name=qol_questionnaires
    ${symptom_is_in_list}    Run Keyword And Return Status
    ...    List Should Contain Value
    ...    ${list_of_symptoms}
    ...    ${questionnaire}
    ${qol_is_in_list}    Run Keyword And Return Status
    ...    List Should Contain Value
    ...    ${list_of_qol}
    ...    ${questionnaire}
    IF    ${symptom_is_in_list}
        Navigate To Clinic
        Select Latest Clinic Message
        Select Answer Questionnaire
        Select Yes For Symptoms    Other symptom
        Changes In Gen State Of Health Is Displayed
        Select Answer To Question    When did you have this symptom?    Today
        questionnaires.Check And Select Radio Buttons
        questionnaires.Check And Write To Text Area
        questionnaires.Check And Write To Number Field
        questionnaires.Check And Tick Checkboxes
        Try To Click Element    ${aeq_questionnaire_next_button}
        Send Symptom Questionnaire To Clinic
    ELSE IF    ${qol_is_in_list}
        questionnaires.Complete Questionnaire    ${questionnaire}
        questionnaires.Save Questionnaire    ${questionnaire}
    END

Ask About Symptoms As Patient
    [Arguments]    ${email}
    Login As Patient    ${email}
    Navigate to Clinic
    Ask About Other Symptom

Manage Portal Patient Account
    [Arguments]    ${action}    ${patient_reported}
    Get Action Date Time    active portal patient
    Update System Time    month=${MONTH}    year=${YEAR}
    IF    '${action}'=='Sends message to Clinic'
        Send Message To A Clinic As Patient
    ELSE IF    '${action}'=='Is ePRO patient'
        Manage Questionnaire As ePRO Patient    ${patient_reported}    new_date=no
    END
    ##TODO: VDT medical records, Views labs results, Manages delegate users account

Send Message To A Clinic As Patient
    Login As Patient    ${patient_details}[email]
    Navigate to Clinic
    Select Ask about other issues
    Select topic
    Enter question
    Send question to clinic
    Sleep    1

Open A Triage Case
    [Arguments]    ${case}
    Get Action Date Time    active triage patient
    Update System Time    time=${TIME}    month=${MONTH}    year=${YEAR}
    Login As Nurse    ${email}
    ${care_team}    Get From Dictionary    ${patient_details}    case care team
    ${assigned_to}    Get From Dictionary    ${patient_details}    case assigned to
    ${priority}    Get From Dictionary    ${patient_details}    case priority
    ${origin}    Get From Dictionary    ${patient_details}    case origin
    &{case_details}    Create Dictionary    case_type=${case}    care_team=${care_team}
    ...    assigned_to=${assigned_to}    case_description=Test case description    case_priority=${priority}
    ...    case_origin=${origin}    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}
    Set Test Variable    @{acute_symptoms}    Chest Pain
    Open A New Case    ${CURRENT_SSN}    save

Close A Triage Case
    [Arguments]    ${case}    ${outcome}    ${delay}
    Get Action Date Time    close case
    Update System Time    time=${TIME}    month=${MONTH}    year=${YEAR}
    Refresh The View    ${delay}
    Login As Nurse    ${email}
    Wait Until Element Is Not Visible    remember-me-next-button
    Search Patient By Identity Code    ${patient_details}[social security number]
    Open Patient Cases Tab
    ${delay_reason}    Set Variable If    '${delay}'!='${NONE}'    ${delay}    no
    Close Case With Outcome    ${outcome}    case_type=${case}    delay_reason=${delay_reason}

Refresh The View
    # Wait for 3mins before closing
    [Arguments]    ${delay_reason}
    IF    '${delay_reason}'!='${NONE}'    Sleep    180s
    Reload Page

Get Action Date Time
    # Returns the month and year the action should be made
    [Arguments]    ${action}
    IF    '${action}'=='patient created'
        ${month_value}    ${year_value}    Set Variable    patient created month    patient created year
    ELSE IF    '${action}'=='activated account'
        ${month_value}    ${year_value}    Set Variable    activated account month    activated account year
    ELSE IF    '${action}'=='active portal patient'
        ${month_value}    ${year_value}    Set Variable    active portal patient month    active portal patient year
    ELSE IF    '${action}'=='active triage patient'
        ${month_value}    ${year_value}    Set Triage Case Action Time    active triage patient
    ELSE IF    '${action}'=='close case'
        ${month_value}    ${year_value}    Set Triage Case Action Time    close case
    ELSE IF    '${action}'=='message'
        ${month_value}    ${year_value}    Set Message Time    message
    ELSE IF    '${action}'=='message status'
        ${month_value}    ${year_value}    Set Message Time    message status
    ELSE
        ${month_value}    ${year_value}    Set Variable    ${None}    ${None}
    END

    # ...    ELSE IF    '${action}'=='message'
    # ...    Set Variable    message month    message year
    # ...    ELSE IF    '${action}'=='message status'
    # ...    Set Variable    message month    message year

    ${month}    Get From Dictionary    ${patient_details}    ${month_value}
    ${year}    Get From Dictionary    ${patient_details}    ${year_value}
    Set Test Variable    ${month}
    Set Test Variable    ${year}

Set Message Time
    [Arguments]    ${column}
    IF    '${column}'=='message'
        ${time}    ${month}    ${year}    Set Variable    message time    message month    message year
    ELSE IF    '${column}'=='message status'
        ${time}    ${month}    ${year}    Set Variable
        ...    message status time
        ...    message status month
        ...    message status year
    ELSE
        ${time}    ${month}    ${year}    Set Variable    ${None}    ${None}    ${None}
    END
    IF    '${column}'=='message'
        ${time}    Get From Dictionary    ${patient_details}    message time
    ELSE IF    '${column}'=='message status'
        ${time}    Get From Dictionary    ${patient_details}    message status time
    ELSE
        ${time}    Set Variable    ${None}
    END
    Set Test Variable    ${time}
    RETURN    ${month}    ${year}

Set Triage Case Action Time
    [Arguments]    ${column}
    IF    '${column}'=='active triage patient'
        ${time}    ${month}    ${year}    Set Variable
        ...    active triage patient time
        ...    active triage patient month
        ...    active triage patient year
    ELSE IF    '${column}'=='close case'
        ${time}    ${month}    ${year}    Set Variable    close case time    close case month    close case year
    ELSE
        ${time}    ${month}    ${year}    Set Variable    ${None}    ${None}    ${None}
    END
    IF    '${column}'=='active triage patient'
        ${time}    Get From Dictionary    ${patient_details}    active triage patient time
    ELSE IF    '${column}'=='close case'
        ${time}    Get From Dictionary    ${patient_details}    close case time
    ELSE
        ${time}    Set Variable    ${None}
    END
    Set Test Variable    ${time}
    RETURN    ${month}    ${year}

Update System Time
    # date_time format should be 2-digit number based on UTC: {month}{day}{hour}{minute}{year}
    # if day==default, it sets the day to the 2nd day of any month
    [Arguments]    ${time}=now    ${day}=default    ${month}=now    ${year}=now
    ${current}    Get Current Date    result_format=%m%d%H%M%Y
    ${current_month}    Get Substring    ${current}    0    2
    ${current_day}    Get Substring    ${current}    2    4
    ${current_hour}    Get Substring    ${current}    4    6
    ${current_minute}    Get Substring    ${current}    6    8
    ${current_year}    Get Substring    ${current}    -4
    ${hour}    Get Substring    ${time}    0    2
    ${minute}    Get Substring    ${time}    -2
    ${minute}    Set Variable If    '${time}'=='now'    ${current_minute}    ${minute}
    ${hour}    Set Variable If    '${time}'=='now'    ${current_hour}    ${hour}
    ${day}    Set Variable If    '${day}'=='default'    02    ${day}
    ${month}    Set Variable If    '${month}'=='now'    ${current_month}    ${month}
    ${year}    Set Variable If    '${year}'=='now'    ${current_year}    ${year}
    Log    ${month}${day}${hour}${current_minute}${year}
    Set Test Variable    ${month}
    Set Test Variable    ${year}
    Run    echo ${pass} | sudo -S date ${month}${day}${hour}${minute}${year}
    Sleep    1

Revert Time To Current
    Run    echo ${pass} | sudo -S sntp -sS time.apple.com

Get Patient Details
    [Arguments]    ${patient_index}    ${sheet}
    # patient_index starts with 1
    &{patient_details}    Create Dictionary
    ${headers}    Read Excel Row    1    sheet_name=${sheet}
    ${header_count}    Get Length    ${headers}
    ${patient_index}    Evaluate    ${patient_index}+1
    FOR    ${INDEX}    IN RANGE    1    ${header_count}+1
        ${header}    Read Excel Cell    row_num=1    col_num=${INDEX}    sheet_name=${sheet}
        ${data}    Read Excel Cell    row_num=${patient_index}    col_num=${INDEX}    sheet_name=${sheet}
        Set To Dictionary    ${patient_details}    ${header}=${data}
    END
    Set Suite Variable    ${patient_details}
    Set Suite Variable    ${CURRENT_SSN}    ${patient_details}[social security number]
    Log Dictionary    ${patient_details}

Close Excel And Reset Time
    Close All Excel Documents
    Revert Time To Current

Set Patient Module
    ${rand}    Random Int    1    55
    ${patient_module}    Read Excel Cell    row_num=${rand}    col_num=1    sheet_name=modules
    Set Test Variable    ${patient_module}

Set Patient Diagnosis
    ${rand}    Random Int    1    164
    ${patient_diagnosis}    Read Excel Cell    row_num=${rand}    col_num=1    sheet_name=icd
    Set Test Variable    ${patient_diagnosis}
