*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${pe_patient_mrn}                                   PE-001
${pe_patient_name}                                  id:patient-name
${pe_patient_first_name}                            Paiva
${pe_patient_last_name}                             Karhunen
${add_documents}                                    //span[contains(text(),'Add documents')]
${search_documents_modal}                           //div[@class='search-attachments-modal']
${contact_patient_panel}                            //*[@data-testid="contact-patient-form"]
${search_field}                                     //*[@data-testid="search"]
${search_button}                                    //a[contains(text(),'Search')]
${save_documents_button}                            //button[@id='save-template-edit']
${patients_search}                                  id:input-search-ssn-search
${patients_info_header}                             handle-patient
${case1_title}                                      (//div[contains(@class, 'case-title')])[1]
${contact_patient_button_top}                       //*[@data-testid="contact-patient-btn"]
${attached_documents_in_panel}                      //span[contains(text(),'Documents (1)')]
${message_attachment_element}                       //div[starts-with(@class, 'message-attachments')]
${number_of_documents_dropdown_contact_patient}     (//span[contains(@class, 'attachment-list-header-title')])[last()]
${number_of_documents_dropdown_message_thread}      (//span[contains(@class, 'attachment-list-header-title')])[1]
${maximum_attachment_limit_error_message}           You cannot attach more than 15 documents.
${document_search_cancel_button}                    cancel-template-edit
${searched_text_in_document_topic}                  //span[contains(text(),'{}')]
${contact_type_option}                              //div[contains(@class, 'dropdown-panel-items')]/descendant::div[contains(text(),"{}")]
${attachment_list_in_search_document_modal}         //div[@class='attachment-list']
${selected_document_table_data}                     //td[text()="{}"]/..
${remove_document_button_search_modal}              //message-attachment//span[contains(text(),"{}")]/../../a
${remove_document_button_contact_patient_window}    //span[contains(text(),"{}")]/../../a
${attachment_id_brain_tumors}                       00608b02-c128-4e3d-aeba-6bdcf30e0710
${attachment_name_brain_tumors}                     Brain Tumors: The Basics
${attachment_id_brain_metastases}                   750ddcc4-ff71-4b65-a383-c885a72963c7
${attachment_id_survivorship_heart}                 47f2520-4bdc-4881-a36a-47b44825ad98
${attachment_name_survivorship_heart}               Survivorship: Heart Health After Targeted Therapy
${attachment_name_brain_metastases}                 Brain Metastases: The Basics
# for the next 2 elements: index 1 because it indicates that the document is the next immediate element
${pe_document_bookmarked_element}
...                                                 //p[text()="Bookmarks"]/following-sibling::message-attachment[1]//*[contains(text(),"{}")]
${pe_other_docs_element}
...                                                 //p[text()="Other documents"]/following-sibling::message-attachment[1]//*[contains(text(),"{}")]
${bookmarked_icon}                                  //div[contains(text(),"{}")]/../preceding-sibling::div/ds-icon
${search_document_dropdown_arrow}                   //*[@id='searchAttachment_source--filter']//span[@class='ng-arrow-wrapper']
${patient_education_search_option_oncolink}         (//*[@title='Oncolink'])[last()]
${education_document_title}                         //td[contains(text(),"{}")]


*** Keywords ***
Verify Contact Patient Panel
    Select Contact The Patient
    Wait Until Element Is Enabled    ${contact_patient_panel}

Select Contact The Patient
    Wait Until Element Is Visible    ${contact_patient_button_top}
    Wait Until Keyword Succeeds    9    1    Click Button    ${contact_patient_button_top}

Contact Patient With Education Articles
    [Documentation]    Contact patient non-case related (first contact patient button)
    [Arguments]    ${topic}    ${contact_type}    ${message_template}=none
    Sleep    1s
    Wait Until Element Is Enabled    ${non_case_related_contact_patient_button}
    Try To Click Element    ${non_case_related_contact_patient_button}
    Wait Until Element Is Visible    ${contact_type_list}
    Try To Input Text    ${contact_patient_topic_field}    ${topic}
    Try To Click Element    ${contact_type_box}
    Try To Click Element    ${contact_type_pe}
    Wait Until Page Contains Element    ${contact_message_field}
    IF    '${contact_type}'=='Patient Education'
        Wait Until Element Is Visible    ${patient_education_panel_message}
        Patient Education Tooltip Info Is Displayed
        Element Should Be Visible    ${add_documents_link}
    END
    ${random_string_education}    Generate Random String    70
    Set Suite Variable    ${random_string_education}
    IF    '${message_template}'=='none'
        Input Text    ${contact_message_field}    ${random_string_education}
    ELSE
        Select Message Template To Contact Patient    ${message_template}
    END
    Click Add Documents

Verify Patient Education Sources
    [Arguments]    ${default}    @{sources}
    #contact type dropdown is already opened
    Try To Click Element    ${contact_type_pe}
    Click Add Documents
    Wait Until Element Is Visible    //*[@id='searchAttachment_source--filter']//div[@title='${default}']
    Try To Click Element    ${search_document_dropdown_arrow}
    FOR    ${source}    IN    @{sources}
        Wait Until Element Is Visible    //*[@role='option']//div[text()=' ${source} ' ]    timeout=3s
    END

Click Add Documents
    Try To Click Element    ${add_documents}
    Wait Until Element Is Visible    ${search_documents_modal}

Search Document Articles
    [Arguments]    ${document_topic}
    Wait Until Element Is Visible    ${search_field}
    Input Text    ${search_field}    ${document_topic}
    Set Suite Variable    ${document_topic}
    Try To Click Element    ${search_button}
    ${element}    Format String    ${searched_text_in_document_topic}    ${document_topic}
    Wait Until Element Is Visible    ${element}

Select First Document In List
    Execute Javascript    document.getElementsByClassName('grow documentTitle')[0].click();

Select Multiple Patient Education Documents In List
    [Arguments]    ${number_of_documents}
    FOR    ${index}    IN    ${number_of_documents}
        Execute Javascript    document.getElementsByClassName('grow documentTitle')[${index}].click();
    END

Select Education Document By Title
    [Arguments]    ${title}
    ${document_element}    Format String    ${education_document_title}    ${title}
    Wait Until Element Is Visible    ${document_element}
    Try To Click Element    ${document_element}

Compare Selected Documents From Attached
    ${1st_document_on_search_list}    Execute Javascript
    ...    return document.getElementsByClassName('grow documentTitle')[0].innerText;
    ${attached_doc_on_modal}    Execute Javascript
    ...    return document.getElementsByClassName('attachment-list')[0].innerText
    ${attached_doc_on_modal}    Remove String    ${attached_doc_on_modal}    \nOncolink\nRemove
    Should Be Equal    ${1st_document_on_search_list}    ${attached_doc_on_modal}
    Set Suite Variable    ${attached_education_document}    ${1st_document_on_search_list}
    View Education Document In Clinic

View Education Document In Clinic
    Wait until keyword succeeds
    ...    3x
    ...    1s
    ...    Execute Javascript
    ...    document.getElementsByClassName('link type-HTML view-document')[0].click();
    Wait until keyword succeeds    5x    1s    Switch Window    NEW
    Wait until keyword succeeds    5x    1s    Wait Until Page Contains    ${document_topic}
    Wait until keyword succeeds    3x    1s    Switch Window    MAIN

Save Education Document Attachment
    Try To Click Element    ${save_documents_button}    wait_in_seconds=5s

Verify Attached Document On Contact Patient Panel
    Wait Until Element Is Enabled    ${contact_patient_panel}
    Try To Click Element    ${attached_documents_in_panel}
    Wait Until Element Is Visible    //span[contains(text(),'${attached_education_document}')]

Send Message To Patient
    Try To Click Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}

Contact Patient With Patient Education Documents In Message Template
    [Documentation]    Contact patient non-case related (first contact patient button)
    [Arguments]    ${topic}    ${contact_type}    ${message_template}
    Sleep    1s
    Wait Until Element Is Enabled    ${non_case_related_contact_patient_button}
    Try To Click Element    ${non_case_related_contact_patient_button}
    Wait Until Element Is Visible    ${contact_type_list}
    Element Should Be Visible    ${add_documents_link}
    Try To Input Text    ${contact_patient_topic_field}    ${topic}
    Try To Click Element    ${contact_type_list}
    ${element}    Format String    ${contact_type_option}    ${contact_type}
    Try To Click Element    ${element}
    Wait Until Page Contains Element    ${contact_message_field}
    IF    '${contact_type}'=='Patient Education'
        Wait Until Element Is Visible    ${patient_education_panel_message}
        Patient Education Tooltip Info Is Displayed
    END
    ${random_string}    Generate Random String    70
    Set Test Variable    ${random_string}
    Select Message Template To Contact Patient    ${message_template}
    Wait Until Element Is Visible    ${message_attachment_element}

Remove Attached Document From Search Modal
    [Arguments]    ${document_name}
    ${element}    Format String    ${remove_document_button_search_modal}    ${document_name}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}
    Wait Until Element Does Not Contain    ${attachment_list_in_search_document_modal}    ${document_name}

Remove Attached Document From Contact Patient Window
    [Arguments]    ${document_name}
    Try To Click Element    ${number_of_documents_dropdown_contact_patient}
    ${element}    Format String    ${remove_document_button_contact_patient_window}    ${document_name}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}
    Wait Until Page Does Not Contain    ${document_name}

Document Is Bookmarked
    [Arguments]    ${document_title}
    Wait Until Page Contains    ${document_title}
    ${element}    Format String    ${pe_document_bookmarked_element}    ${document_title}
    Wait Until Element Is Visible    ${element}    # checks if element is in bookmark section
    Bookmark Icon Is Visible    ${document_title}
    ${element}    Format String    ${pe_other_docs_element}    ${document_title}
    Page Should Not Contain Element    ${element}    # checks if element is not in other documents

Document Is Listed In Other Documents
    [Documentation]    use when one or more documents are bookmarked and one or more are not
    [Arguments]    ${document_title}
    ${element}    Format String    ${pe_other_docs_element}    ${document_title}
    Wait Until Element Is Visible    ${element}    # checks if element is in other documents
    Document Is Not Bookmarked    ${document_title}    # checks if element is not in bookmark section

Document Is Not Bookmarked
    [Arguments]    ${document_title}
    Wait Until Page Contains    ${document_title}
    ${element}    Format String    ${pe_document_bookmarked_element}    ${document_title}
    Page Should Not Contain Element    ${element}    # checks if element is not in bookmark section
    Bookmark Icon Is Not Visible    ${document_title}

Bookmark Icon Is Visible
    [Arguments]    ${document_title}
    ${bookmarked_icon_element}    Format String    ${bookmarked_icon}    ${document_title}
    ${attr}    Get Element Attribute    ${bookmarked_icon_element}    class
    Should Contain    ${attr}    ds-icon--file_html-bookmarked

Bookmark Icon Is Not Visible
    [Arguments]    ${document_title}
    ${bookmarked_icon_element}    Format String    ${bookmarked_icon}    ${document_title}
    ${attr}    Get Element Attribute    ${bookmarked_icon_element}    class
    Should Not Contain    ${attr}    ds-icon--file_html-bookmarked


