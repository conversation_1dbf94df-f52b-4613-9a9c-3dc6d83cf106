*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource


*** Variables ***
${clinic_staff_password_field}      //div[@class='nurse-user-password']/input
${clinic_staff_login_button}        //div[@class='nurse-form-actions']/button
${about_link}                       //*[@class='noona-button button-color-primary button-variant-link']
${login_page_loader}                noona-loader
${welcome_to_noona_text}            Welcome to Noona
${logout_open_questionnaire_for_patient}    //*[@id='logout-link']


*** Keywords ***
Login To Noona As Nurse
    Login.Login To Nurse    ${automated_tests_clinic}[default_user]    ${DEFAULT_PASSWORD}    clinic_name=${automated_tests_clinic}[name]

Logout As Nurse
    Wait Until Page Contains Element    ${profile_drop_down}
    Click Element    ${profile_drop_down}
    Wait Until Element Is Visible    ${nurse_user_profile}
    Wait Until Element Is Visible    ${logout_from_clinic}
    Click Element    ${logout_from_clinic}

Verify Nurse Login Is Successful
    [Arguments]     ${user_name}
    Wait Until Page Contains Element        //div[contains(text(), '${user_name}')]

Login As Nurse
    [Arguments]    ${email}=${automated_tests_clinic}[default_user]    ${clinic}=${automated_tests_clinic}[name]    ${open_browser}=True    ${remember_login}=Yes    ${user_type}=${USER_TYPE}[clinic_user]
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists} and ${open_browser}
        Open Browser    ${NURSE_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${NURSE_LOGIN_URL}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Sleep    1s
    Page Should Not Contain    503 Service Temporarily Unavailable
    Accept All Cookies If Visible For Clinic
    IF    '${user_type}'=='noona_admin'
        #sso login with new noona admin user
        Login Using SSO And Switch Clinic   clinic=${clinic}
    ELSE
        #old login with old clinic users
        Login As Nurse From Landing Page    email=${email}
        ${clinic-check}    ${value} =    Run Keyword And Ignore Error
        ...    Wait Until Element Is Visible    ${clinic_list}    timeout=3s
        IF    '${clinic-check}'=='PASS'
            Wait until keyword succeeds    3x    1s    Choose Clinic    ${clinic}
        END
        IF    '${remember_login}'!='none'
            Wait until keyword succeeds    3x    1s    Keep Me Logged In    ${remember_login}
        END
    END

Log In To Clinic With SSO
    [Documentation]    Only used to login on clinic side by sso/migrated users
    [Arguments]    ${email}    ${switch_to_clinic}=no    ${open_browser}=True
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists} and ${open_browser}
        Open Browser    ${NURSE_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${NURSE_LOGIN_URL}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Sleep    1s
    Page Should Not Contain    503 Service Temporarily Unavailable
    Accept All Cookies If Visible For Clinic
    Login To Clinic Using SSO    ${email}
    IF    '${switch_to_clinic}'!='no'    Switch Clinic From User Profile Menu Options    ${switch_to_clinic}

Login As Nurse With Password Only
    [Documentation]    Used for clinic staff login after opening a questionnaire for patient
    Wait Until Page Contains Element    ${logout_open_questionnaire_for_patient}
    Try To Input Text    ${clinic_staff_password_field}    ${DEFAULT_PASSWORD}
    Click Element    ${clinic_staff_login_button}

Login As Nurse With 2FA
    [Arguments]    ${email}=${automated_tests_clinic}[default_user]    ${clinic}=${automated_tests_clinic}[name]    ${remember_login}=Yes
    Delete All Messages In Server    ${sms_server_id}    ${sms_api_key}
    Login To Noona    nurse    ${email}
    ${clinic-check}    ${value} =    Run Keyword And Ignore Error
    ...    Wait Until Element Is Visible    clinic    timeout=3s
    IF    '${clinic-check}'=='PASS'
        Wait until keyword succeeds    3x    1s    Choose Clinic    ${clinic}
    END
    Input Clinic User Verification Code
    Try To Click Element    ${2fa_next_button}
    IF    '${remember_login}'!='none'
        Wait until keyword succeeds    3x    1s    Keep Me Logged In    ${remember_login}
    END

Input Clinic User Verification Code
    Get Clinic User SMS From Mailosaur
    Try To Click Element    ${verification_code_first_input}
    Press Keys    NONE    ${get_sms_code}

Get Clinic User SMS From Mailosaur
    [Documentation]    mobile sent to number should be without country code
    ${sms_message} =    User Received SMS
    ${get_sms_code} =    Get Substring    ${sms_message}    0    6
    Set Global Variable    ${get_sms_code}

Login As Nurse From Landing Page
    [Documentation]    Used when user is already in the login page
    [Arguments]    ${email}
    Wait Until Element is Visible    ${email_textbox}
    Try To Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    Wait Until Element Is Enabled    ${login_button}
    Try To Click Element    ${login_button}

Login Using SSO And Switch Clinic
    [Documentation]    Used when user is logging in using sso login page
    [Arguments]    ${clinic}    ${email}=${SSO_NOONA_ADMIN}
    Login To Clinic Using SSO    ${email}
    Switch Clinic From User Profile Menu Options    ${clinic}

Login To Clinic Using SSO
    [Arguments]    ${email}
    Try To Click Element    ${sso_login_link}
    Wait Until Element is Visible    ${sso_email_textbox}
    Try To Input Text    ${sso_email_textbox}    ${email}
    Try To Click Element    ${sso_email_login_button}
    Wait Until Element Is Visible    ${sso_password_textbox}
    Input Password    ${sso_password_textbox}    ${SSO_DEFAULT_PASSWORD}
    Try To Click Element    ${sso_pwd_login_button}
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${stay_sign_in}
    IF    ${status}
        Try To Click Element    //*[contains(@type, 'submit') and contains(@id,'idSIButton9')]
    END

Open Page And Login
    [Arguments]    ${url}    ${email}
    Open Page    ${url}
    Accept All Cookies If Visible For Clinic
    Try To Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    ${current_url} =    Get Location
    Set Test Variable    ${current_url}
    Try To Click Element    ${login_button}