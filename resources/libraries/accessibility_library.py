"""
Accessibility testing library for Robot Framework using axe-core
"""
import json
from robot.api.deco import keyword
from robot.libraries.BuiltIn import BuiltIn

try:
    from axe_selenium_python import Axe
    AXE_AVAILABLE = True
except ImportError:
    AXE_AVAILABLE = False


class AccessibilityLibrary:
    """Library for automated accessibility testing using axe-core"""
    
    ROBOT_LIBRARY_SCOPE = "GLOBAL"
    
    def __init__(self):
        self.axe = None
        self.driver = None
    
    def _get_driver(self):
        """Get the current Selenium WebDriver instance"""
        if not self.driver:
            selenium_lib = BuiltIn().get_library_instance("SeleniumLibrary")
            self.driver = selenium_lib.driver
        return self.driver
    
    @keyword
    def run_accessibility_scan(self, context=None, options=None):
        """
        Run axe-core accessibility scan on the current page
        
        Args:
            context: CSS selector or element to scan (None for full page)
            options: Dictionary of axe options (rules, tags, etc.)
        
        Returns:
            Dictionary containing scan results
        """
        driver = self._get_driver()
        self.axe = Axe(driver)
        
        # Inject axe-core into the page
        self.axe.inject()
        
        # Run the scan
        if context:
            results = self.axe.run(context=context, options=options)
        else:
            results = self.axe.run(options=options)
        
        return results
    
    @keyword
    def check_accessibility_violations(self, fail_on_violations=True, severity_threshold="minor"):
        """
        Check for accessibility violations and optionally fail the test
        
        Args:
            fail_on_violations: Whether to fail the test if violations are found
            severity_threshold: Minimum severity to consider (minor, moderate, serious, critical)
        
        Returns:
            List of violations
        """
        results = self.run_accessibility_scan()
        violations = results.get("violations", [])
        
        # Filter by severity if specified
        severity_levels = {"minor": 1, "moderate": 2, "serious": 3, "critical": 4}
        threshold_level = severity_levels.get(severity_threshold.lower(), 1)
        
        filtered_violations = []
        for violation in violations:
            violation_level = severity_levels.get(violation.get("impact", "minor").lower(), 1)
            if violation_level >= threshold_level:
                filtered_violations.append(violation)
        
        if filtered_violations and fail_on_violations:
            violation_summary = self._format_violations(filtered_violations)
            raise AssertionError(f"Accessibility violations found:\n{violation_summary}")
        
        return filtered_violations
    
    @keyword
    def check_wcag_compliance(self, level="AA", fail_on_violations=True):
        """
        Check WCAG compliance at specified level
        
        Args:
            level: WCAG level (A, AA, AAA)
            fail_on_violations: Whether to fail the test if violations are found
        """
        wcag_tags = {
            "A": ["wcag2a", "wcag21a"],
            "AA": ["wcag2a", "wcag2aa", "wcag21a", "wcag21aa"],
            "AAA": ["wcag2a", "wcag2aa", "wcag2aaa", "wcag21a", "wcag21aa", "wcag21aaa"]
        }
        
        options = {
            "tags": wcag_tags.get(level.upper(), wcag_tags["AA"])
        }
        
        results = self.run_accessibility_scan(options=options)
        violations = results.get("violations", [])
        
        if violations and fail_on_violations:
            violation_summary = self._format_violations(violations)
            raise AssertionError(f"WCAG {level} violations found:\n{violation_summary}")
        
        return violations
    
    @keyword
    def save_accessibility_report(self, filename="accessibility_report.json"):
        """Save accessibility scan results to a JSON file"""
        results = self.run_accessibility_scan()
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        BuiltIn().log(f"Accessibility report saved to {filename}")
        return filename
    
    @keyword
    def check_specific_rules(self, rules, fail_on_violations=True):
        """
        Check specific accessibility rules
        
        Args:
            rules: List of rule IDs to check
            fail_on_violations: Whether to fail the test if violations are found
        """
        options = {
            "rules": {rule: {"enabled": True} for rule in rules}
        }
        
        results = self.run_accessibility_scan(options=options)
        violations = results.get("violations", [])
        
        if violations and fail_on_violations:
            violation_summary = self._format_violations(violations)
            raise AssertionError(f"Rule violations found:\n{violation_summary}")
        
        return violations
    
    def _format_violations(self, violations):
        """Format violations for readable output"""
        formatted = []
        for violation in violations:
            rule_id = violation.get("id", "unknown")
            description = violation.get("description", "No description")
            impact = violation.get("impact", "unknown")
            node_count = len(violation.get("nodes", []))
            
            formatted.append(f"- {rule_id} ({impact}): {description} ({node_count} elements)")
        
        return "\n".join(formatted)
