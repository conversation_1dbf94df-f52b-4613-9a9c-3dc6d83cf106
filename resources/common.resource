*** Settings ***
Library     Collections
Library     String
Library     SeleniumLibrary
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_common.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource

*** Variables ***
${noona-loader}                             //*[@id="noona-loader"]
${incorrect_username_password_web}          Incorrect username or password. If you have forgotten your password, click "Problems logging in?"
${incorrect_username_password_false}        Incorrect username or password. If you have forgotten your password, please click Problems logging in -link.
${incorrect_username_password_native}       Incorrect username or password. If you have forgotten your password, click &quot;Problems logging in?&quot;
${diary_heading}                            //*[@id='header-page-title'][contains(text(),'Diary')]

*** Keywords ***
Get Lowercase Text List From Elements
    [Arguments]    ${locator}
    @{lst-elem}    Get WebElements    ${locator}
    @{lst-txt}    Create List
    FOR    ${elem}    IN    @{lst-elem}
        ${txt}    Get Text    ${elem}
        ${txt}    Convert To Lowercase    ${txt}
        Append To List    ${lst-txt}    ${txt}
    END
    RETURN    @{lst-txt}

Sort Elements In Column And Confirm
    [Arguments]    ${col-idx}
    Sleep    1
    Wait Until Keyword Succeeds    9    1    Click Element    //thead/tr/th[${col-idx}]
    Wait Until Page Does Not Contain Element    ${loader}    2s
    @{lst-element-texts}    Get Lowercase Text List From Elements    //tbody/tr/td[${col-idx}]/div/span
    @{lst-cpy}    Copy List    ${lst-element-texts}
    Sort List    ${lst-cpy}
    Lists Should Be Equal    ${lst-element-texts}    ${lst-cpy}

Verify Language Tabs
    @{expected_languages}    Create List    EN    NL    TR    ES    FR    IT    NO    DE    PT    SV    FI    PL
    @{language_list}    Create List
    @{elements}    Get WebElements    (//div[@class='tabs-new'])[1]/div/span[1]
    Wait Until Element Is Visible    ((//div[@class='tabs-new'])[1]/div/span[1])[1]
    FOR    ${element}    IN    @{elements}
        ${lang}    Get text    ${element}
        Append To List    ${language_list}    ${lang}
    END
    Lists Should Be Equal    ${expected_languages}    ${language_list}    ignore_order=True

Page Should Not Contain App Error
    Sleep    1
    Text Should Not Be In The Page    Application error. Please try again.

Set Email For New User
    [Arguments]    ${first_name}    ${last_name}
    ${email}    Set Variable    ${first_name}.${last_name}@ujbmenad.mailosaur.net
    ${email}    Convert To Lower Case    ${email}
    RETURN    ${email}

Remove Input Text
    [Documentation]    This is a workaround for "Clear Input Text"
    [Arguments]    ${input_field}    ${backspace_count}
    Wait Until Element Is Visible    ${input_field}
    Sleep    2s
    Repeat Keyword    ${backspace_count}    Press Keys    ${input_field}    BACKSPACE

Verify Css Property Color
    [Documentation]    Compares rgba/rgb color
    [Arguments]    ${element}    ${expected_color}    ${attribute}
    Wait Until Element Is Visible    ${element}
    ${webelement}    Get WebElement    ${element}
    ${element_color}    Call Method    ${webelement}    value_of_css_property    ${attribute}
    Should Contain    ${element_color}    ${expected_color}

Set Application On Environment
    IF    'native' in '${ENVIRONMENT}'    Setup Native App In Browserstack

Text Should Be In The Page
    [Arguments]    ${text}
    IF    'native' in '${ENVIRONMENT}'
        Page Should Contain Text    ${text}
    ELSE
        Page Should Contain    ${text}
    END

Text Should Not Be In The Page
    [Arguments]    ${text}
    IF    'native' in '${ENVIRONMENT}'
        Page Should Not Contain Text    ${text}
    ELSE
        Page Should Not Contain    ${text}
    END

Generic: Element Should Contain
    [Arguments]    ${element}    ${text}
    IF    'native' in '${ENVIRONMENT}'
        Element Should Contain Text    ${element}    ${text}
    ELSE
        Element Should Contain    ${element}    ${text}
    END

Generic: Element Should Not Contain
    [Arguments]    ${element}    ${text}
    IF    'native' in '${ENVIRONMENT}'
        Element Should Not Contain Text    ${element}    ${text}
    ELSE
        Element Should Not Contain    ${element}    ${text}
    END

Generic: Execute Javascript
    [Arguments]    ${script}
    IF    'native' in '${ENVIRONMENT}'
        ${return}    Execute Script    ${script}
    ELSE
        ${return}    Execute Javascript    ${script}
    END
    RETURN    ${return}

Generic: Get Element Count
    [Arguments]    ${element}
    IF    'native' in '${ENVIRONMENT}'
        ${return}    Get Matching Xpath Count    ${element}
    ELSE
        ${return}    Get Element Count    ${element}
    END
    RETURN    ${return}

Open URL In Chrome
    [Documentation]    Common keyword for opening browser when running in gitlab and manually
    [Arguments]    ${url}    ${alias}=None
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}    alias=${alias}
    ELSE
        Open Browser    ${url}    ${BROWSER}    alias=${alias}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}

Generic: Wait Until Element Is Not Visible
    [Documentation]    Wait Until Element Is Not Visible is not available in appiumlibrary
    [Arguments]    ${element}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Does Not Contain Element    ${element}
    ELSE
        Wait Until Element Is Not Visible    ${element}
    END

Generic: Clear Text
    [Arguments]    ${element}
    IF    'native' in '${ENVIRONMENT}'
        Clear Text    ${element}
    ELSE
        Clear Element Text    ${element}
    END

Remove Patient From Clinic Via API
    [Documentation]    ${patient_id} and ${patient_user_id} should be set separately from creating patient keyword
    [Arguments]    ${patient_email}    ${nurse_email}    ${clinic_id}    ${ehr_token}    ${patient_password}=default
    Login As Patient Via API    ${patient_email}    ${clinic_id}    patient_password=${patient_password}
    Send Delete Patient Request    patient_password=${patient_password}
    Set Test Variable    ${login_token}    ${ehr_token}
    Login As Nurse Via API    ${nurse_email}    ${clinic_id}
    Remove Patient Via API    ${patient_id}    ${patient_user_id}

Remove Patient As Test Teardown
    [Documentation]    This should not be part of the test case but only acts as a teardown or cleanp
    ...    to remove the newly created patient. Hence, putting inside a Run Keyword And Continue On Failure
    ...    Note: The test will still pass even if teardown fails but will leave a warning in the log file so we can check why it failed
    [Arguments]    ${patient_email}    ${nurse_email}    ${clinic_id}    ${ehr_token}    ${patient_password}=default
    Run Keyword And Warn On Failure
    ...    Remove Patient From Clinic Via API
    ...    ${patient_email}
    ...    ${nurse_email}
    ...    ${clinic_id}
    ...    ${ehr_token}
    ...    patient_password=${patient_password}

Remove Patient As Suite Teardown
    [Arguments]    ${ehr_token}    ${patient_email}    ${clinic_id}    ${manager_email}
    Set Suite Variable    ${login_token}    ${ehr_token}
    Login As Patient Via API    ${patient_email}    ${clinic_id}    level=suite
    Set Variable    ${patient_user_id}
    Send Delete Patient Request    level=suite
    Set Suite Variable    ${login_token}    ${ehr_token}
    Login As Nurse Via API
    ...    ${manager_email}
    ...    ${clinic_id}
    ...    level=suite
    Run Keyword And Warn On Failure    Remove Patient Via API    ${patient_id}    ${patient_user_id}    level=suite

Capture Screenshot
    IF    'native' in '${ENVIRONMENT}'
        ${id}    Evaluate    int(time.time())    modules=time
        ${image_file_name}    Set Variable    image_${id}.png
        Capture Page Screenshot    ${image_file_name}
    ELSE
        Capture Page Screenshot    EMBED
    END

Convert Text File To String
    [Arguments]    ${location}
    ${text_file}    Get File    ${location}
    @{lines}    Split To Lines    ${text_file}
    ${convert_list_to_string}    Evaluate    "".join(${lines})
    ${terms}    Catenate    ${convert_list_to_string}
    RETURN    ${terms}

Generate A Distinctive Random String
    [Documentation]   Utilise the keyword to generate and sanitize a random string before inputting in an input field
    [Arguments]    ${length}    ${pattern}
    ${random_string}    Generate Random String    ${length}    ${pattern}
    ${sanitized_string}    Remove Duplicated Char In String   ${random_string}
    RETURN    ${sanitized_string}

Wait Until Noona Loader Is Not Visible
    [Arguments]    ${timeout}=${SYS_VAR_PAGE_TIMEOUT}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Does Not Contain Element    ${noona-loader}    timeout=20s
    ELSE
        Wait Until Element Is Not Visible    ${noona-loader}    timeout=${timeout}
    END

Generate A Random Date From Current Date
    [Arguments]    ${days}    ${format}=%d-%m-%Y
    ${current_date}    Get Current Date
    ${random_date}    Add Time To Date    ${current_date}    ${days}
    ${formatted_date}    Convert Date    ${random_date}    result_format=${format}
    RETURN    ${formatted_date}

Prepare Chrome for Downloads
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${path}    Join Path    ${TEMPDIR}    autotest-downloads    robot-${now}
    Set Suite Variable    ${downloads-dir}    ${path}
    Create Directory    ${downloads-dir}
    ${chrome-options}    Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys, selenium.webdriver
    ${disabled}    Create List    Chrome PDF Viewer
    ${prefs}    Create Dictionary
    ...    download.default_directory=${downloads-dir}
    ...    plugins.plugins_disabled=${disabled}
    Call Method    ${chrome-options}    add_experimental_option    prefs    ${prefs}
    IF    '${BROWSER}'=='headlesschrome'
        Call Method    ${chrome-options}    add_argument    headless
    END
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    Set Suite Variable    ${remote_url_exists}
    IF    ${remote_url_exists}
        Open Browser
        ...    ${PATIENT_LOGIN_URL}
        ...    browser=chrome
        ...    remote_url=${REMOTE_URL}
        ...    options=${chrome-options}
    ELSE
        Open Browser    ${PATIENT_LOGIN_URL}    browser=chrome    options=${chrome-options}
    END

Re-login And Go To Profile
    [Arguments]    ${email}    ${password}=${DEFAULT_PASSWORD}
    Input Login Credentials And Login    ${email}    ${password}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'
        Allow Notifications Upon Re-login
    END
    Wait Until Element Is Visible    ${diary_heading}
    Go To Clinic Preferences

Return To Landing Page And Back To Login For Android
    Try To Click Native App Element    ${close_landing_page_android}
    @{contexts}    Get Contexts
    ${current}    Get Current Context
    Switch To Context    NATIVE_APP
    Try To Click Native App Element    ${native_app_login_android}

Check Incorrect Password Error And Prepare For Next Login
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        IF    'ios' in '${PLATFORM_NAME}'
            Text Should Be In The Page    ${incorrect_username_password_native}
            Clear Patient Login Details
        ELSE
            Text Should Be In The Page    ${incorrect_error_message_oidc}
            Return To Landing Page And Back To Login For Android    # added to enable continuation to next patient login; otherwise login button is not visible and cannot be scrolled to, due to missing Name or Id in locator
        END
    ELSE
        Text Should Be In The Page    ${incorrect_error_message_oidc}
    END

Convert Date Time To Epoch
    [Arguments]    ${date}=today
    IF    '${date}'=='today'
        ${date}    Get Current Date    exclude_millis=yes
    END
    ${date}    Convert Date    ${date}    epoch
    ${date}    Convert To Integer    ${date}
    ${date}    Convert To String    ${date}
    RETURN    ${date}