"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from robot.api.deco import keyword
from base import Base, run_on_failure, save_driver
from noona.nurse.patients.patient_details.questionnaires.questionnaire_inquiry.questionnaire_inquiry_navigation import (
    QuestionnaireInquiryNavigation,
)
from find_element import FindElement


class QuestionnaireInquiry(QuestionnaireInquiryNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(QuestionnaireInquiry, self).__init__()
        self.find_element = FindElement().find_element

    @keyword
    @save_driver
    @run_on_failure
    def get_questionnaire(
        self,
        user,
        patient_id,
        questionnaire_title,
        tick_all_checkboxes=False,
        select_radio_button=False,
        navigate=True,
    ):
        """Returns a list containing questionnaire headers

        :param user: type of user either nurse or patient
        :param navigate: True if user needs to navigate from other page
        :param patient_id: patient's ssn
        :param questionnaire_title: title of the questionnaire
        :param tick_all_checkboxes: check all the checkboxes
        :param select_radio_button: select all radio button one at a time
        :return: list of questionnaire headers

        Example:
        | Get Questionnaire | HADS |
        | Get Questionnaire | Sociodemographic information | ${TRUE} |
        """
        title_index = ""
        if user == "patient":
            title_index = "1"
        elif user == "nurse":
            title_index = "1"
        if navigate:
            self.page_object_navigation(patient_id, questionnaire_title)
        if tick_all_checkboxes:
            checkboxes = self.find_element(
                self.driver, '//input[@type="checkbox"]/following-sibling::label'
            )
            for checkbox in checkboxes:
                self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", checkbox)
        self.sl.wait_until_page_contains_element(
            '//div[contains(@class, "wizard-button")]/descendant::button[2]'
        )
        titles = self.sl.get_webelements("(//h1)[{0}]".format(title_index))
        headers = self.sl.get_webelements("//h4")
        answers = self.sl.get_webelements("//label")
        descriptions = self.sl.get_webelements("//p")
        headers_list = [self.sl.get_text(header) for header in headers]
        answers_list = [self.sl.get_text(answer) for answer in answers]
        title_text = [self.sl.get_text(title) for title in titles]
        description = [desc.get_attribute("innerHTML").strip().replace('<u>', '').replace('</u>', '').replace('\n', ' ').replace('<br>', ' ') for desc in descriptions]
        questionnaire = []
        questionnaire.extend(title_text)
        questionnaire.extend(description)
        questionnaire.extend(headers_list)
        questionnaire.extend(answers_list)
        if 'EQ-5D' in questionnaire_title:
            indicator_label = self.sl.get_text("//div[@class='indicator-label']")
            questionnaire.append(indicator_label)
        if 'EQ-5D-5L (UK)' in questionnaire_title:
            list_labels = self.sl.get_webelements("//p//li")
            list_list = [self.sl.get_text(list_label).replace('\n', ' ') for list_label in list_labels]
            questionnaire.extend(list_list)
        if select_radio_button:
            radios = self.find_element(
                self.driver, '//input[@type="radio"]/following-sibling::label'
            )
            for radio in radios:
                if 'EQ-5D' not in questionnaire_title:
                    self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", radio)
                headers = self.sl.get_webelements("//h4")
                headers_list2 = [self.sl.get_text(header) for header in headers]
                diff = list(set(headers_list2) - set(headers_list))
                questionnaire += diff
        return questionnaire

    @keyword
    @save_driver
    @run_on_failure
    def cancel_questionnaire(self):
        """Cancel an opened questionnaire.

        This keyword assumes that the questionnaire is
        already opened.

        Example:
        | Cancel Questionnaire |
        """
        # cancel button seems visible and enabled but can't be clicked for a given time
        time.sleep(1)
        self.sl.wait_until_page_contains_element('//div[contains(@class, "wizard-button")]/descendant::button[1]')
        self.sl.wait_until_element_is_enabled('//div[contains(@class, "wizard-button")]/descendant::button[1]')
        self.sl.click_element('//div[contains(@class, "wizard-button")]/descendant::button[1]')
        self.sl.wait_until_element_is_visible('//*[@id="ok-confirm"]')
        self.sl.click_element('//*[@id="ok-confirm"]')