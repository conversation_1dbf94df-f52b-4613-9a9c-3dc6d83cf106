*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}upload_or_remove_photo.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}timeline.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${symptom_elements}                     //h1[@class='ng-star-inserted']
${calendar_dropdown}                    //div[@class='datetime-selector is-open']
${calendar}                             //div[@class='diary-entry-date']
${date_picker}                          //div[contains(@class, "datepicker-days")]
${date_selected}                        //div[@class='title-row']/div[1]
${well_being_button}                    wellbeing-button-label
${energy_level_modal}                   diary-entry-modal
${energy_level_slider}                  //div[contains(@class, "slider")]
${slider_button}                        //button[@class='thumb']
${wellbeing_next_button}                //div[@class='page-indicator']//following-sibling::ds-button
@{pixels}                               140    112    84    56    28    0    -28
...                                     -56    -84    -112    -140
${wellbeing_date}                       //*[@data-testid='datetime-selector-date']
${wellbeing_ave}                        //button[@class='wellbeing-button wellbeing-button--overall-wellbeing']
${wellbeing_ave_modal}                  //button[@class='wellbeing-button wellbeing-button--overall-wellbeing']
${wellbeing_indicator_value}            //div[@class='wellbeing-item']//descendant::div[2]
${wellbeing_textarea}                   //div[@class='note']//textarea
${wellbeing_save_button}                //div[@class='modal-buttons-container ng-star-inserted']//descendant::button[2]
${wellbeing_reports_container}          //h1[@class='type--heading-2 mb-xs ng-star-inserted']
${well_being_reports_h2}
...                                     //h1[@class='container timeline-diary-title ng-star-inserted']//parent::section//descendant::h2
${diary_entry}
...                                     //section[@class='container timeline-month ng-star-inserted'][1]/descendant::wellbeing-indicator[1]
${diary_entry_modal}                    //div[@class='modal-content']
${patient_search}                       input-search-ssn-search
${clinic_energy}                        What is your energy level?
${clinic_appetite}                      How is your appetite?
${clinic_optimism}                      How optimistic do you feel?
${edit_button}                          diary-entry-show-modal-button-edit
${modal_average}
...                                     //button[@class='wellbeing-button wellbeing-button--overall-wellbeing wellbeing-button--view-only']
${modal_date}                           //p[@class='day-name']
${modal_energy}                         //div[@id='modals']//div[3]//wellbeing-indicator[1]
${modal_appetite}                       //div[@class='content']//div[2]//wellbeing-indicator[1]//div[1]
${modal_optimism}                       //div[@id='modals']//div[3]//wellbeing-indicator[1]
${modal_note}                           //div[@class='diary-entry-text']
${modal_header_label}                   //h1[@class='ng-star-inserted']
${modal_close_button_upper}             //button[@class='close']
${modal_close_button}                   diary-entry-show-modal-button-close
${vertical_slider_thumb}                //button[@class='thumb']
${timeline_monday}                      //span[contains(text(),'Mon')]
${text_wellbeing_summary_last_week}     //h1[contains(text(),'Your wellbeing last week')]
${timeline_weekly_summary_mon}          //div[contains(text(),'MON')]
${timeline_weekly_summary_tue}          //div[contains(text(),'TUE')]
${timeline_weekly_summary_wed}          //div[contains(text(),'WED')]
${timeline_weekly_summary_thu}          //div[contains(text(),'THU')]
${timeline_weekly_summary_fri}          //div[contains(text(),'FRI')]
${timeline_weekly_summary_sat}          //div[contains(text(),'SAT')]
${timeline_weekly_summary_sun}          //div[contains(text(),'SUN')]
${diary_entry_day_row}                  //h6[@class='timeline-day-title']/span[2]
${latest_diary_item}                    (//*[@class='timeline-day-items'])[1]
${your_well_being_text_element}         //*[text()="Your wellbeing last week"]/nh-wellbeing-summary


*** Keywords ***
User Selects Note From The Add Menu
    Click Add Menu Button
    Add Diary Note

User Selects Rate Your Well-being
    Wait Until Element Is Enabled    ${well_being_button}
    Wait Until Keyword Succeeds    20s    1s    SeleniumLibrary.Click Element    ${well_being_button}
    Wait Until Element Is Enabled    ${energy_level_modal}
    Wait Until Page Contains Element    ${energy_level_slider}

Rate Well Being With Slider Randomly
    ${random_rate}    Evaluate    random.randint(0, 10)    modules=random
    Wait Until Element Is Visible    ${vertical_slider_thumb}
    @{pixels}    Create List    140    112    84    56    28    0    -28
    ...    -56    -84    -112    -140
    ${pixel}    Set Variable    ${pixels}[${random_rate}]
    Sleep    1
    ${current_rate}    Get Text    ${vertical_slider_thumb}
    IF    ${random_rate}<2
        Rate With Other Well Being    ${random_rate}
    ELSE IF    ${random_rate}>=2 and ${random_rate}<=8
        Drag And Drop By Offset    ${vertical_slider_thumb}    0    ${pixels}[${random_rate}]
    ELSE IF    ${random_rate}>8 and '${current_rate}'!='${random_rate}'
        Rate With Other Well Being    ${random_rate}
    END
    Sleep    1
    RETURN    ${random_rate}

Rate With Other Well Being
    [Documentation]    For some reason, drag and drop can only be set with 2 to 8 ratings. This keyword handles the rest
    [Arguments]    ${random_rate}
    IF    ${random_rate}==0 or ${random_rate}==1
        Drag And Drop By Offset    ${vertical_slider_thumb}    0    ${pixels}[1]
    END
    IF    ${random_rate}==9 or ${random_rate}==10
        Drag And Drop By Offset    ${vertical_slider_thumb}    0    ${pixels}[8]
    END
    IF    ${random_rate}==1
        Press Keys    ${vertical_slider_thumb}    TAB+TAB+ARROW_DOWN
    ELSE IF    ${random_rate}==0
        Press Keys    ${vertical_slider_thumb}    TAB+TAB+ARROW_DOWN+ARROW_DOWN
    ELSE IF    ${random_rate}==9
        Press Keys    ${vertical_slider_thumb}    TAB+TAB+ARROW_UP
    ELSE IF    ${random_rate}==10
        Press Keys    ${vertical_slider_thumb}    TAB+TAB+ARROW_UP+ARROW_UP
    END

Click Wellbeing Next Button
    Wait Until Element Is Enabled    ${wellbeing_next_button}
    Try To Click Element    ${wellbeing_next_button}

User Writes Notes About The Day
    Wait Until Element Is Enabled    ${wellbeing_textarea}
    ${notes}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
    Input Text    ${wellbeing_textarea}    ${notes}
    Set Test Variable    ${NOTES}    ${notes}

User Selects Date For The Note
    ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Wait Until Element Is Visible    ${wellbeing_date}
    ${actual_date}    Get Value    ${wellbeing_date}
    Should Be Equal    ${actual_date}    ${expected_date}

Average Of The Set Well-being Rates Is Displayed In The Note
    ${raw_ave}    Evaluate    (${ENERGY}+${APPETITE}+${OPTIMISM})/3
    ${raw_ave}    Convert To Number    ${raw_ave}    1
    ${raw_ave}    Convert To String    ${raw_ave}
    ${decimal}    Get Substring    ${raw_ave}    -2
    IF    '${decimal}'=='.0'
        ${expected_ave}    Fetch From Left    ${raw_ave}    .
    ELSE
        ${expected_ave}    Set Variable    ${raw_ave}
    END
    ${actual_ave}    Get Text    ${wellbeing_ave_modal}
    Should Be Equal    ${actual_ave}    ${expected_ave}
    Set Test Variable    ${actual_ave}

Verify Answered Wellbeing Rates
    @{expected_rates}    Create List    ${ENERGY}    ${APPETITE}    ${OPTIMISM}
    @{actual_rates}    Create List
    @{rate_elements}    Get WebElements    ${wellbeing_indicator_value}
    FOR    ${rate_element}    IN    @{rate_elements}
        ${wellbeing_rate}    Get Text    ${rate_element}
        ${wellbeing_rate}    Convert To Integer    ${wellbeing_rate}
        Append To List    ${actual_rates}    ${wellbeing_rate}
    END
    Lists Should Be Equal    ${expected_rates}    ${actual_rates}

Verify Wellbeing Upload Button
    Wait Until Page Contains Element    ${wellbeing_upload_button}
    Scroll Element Into View    ${wellbeing_upload_button}
    Element Should Be Visible    ${wellbeing_upload_button}
    Element Should Be Enabled    ${wellbeing_upload_button}
    Element Should Be Visible    ${wellbeing_add_photo}

User Selects Save
    Wait Until Element Is Visible    ${wellbeing_save_button}
    Wait Until Element Is Enabled    ${wellbeing_save_button}
    Try To Click Element    ${wellbeing_save_button}

Note Is Saved In Diary And User Is Directed To Diary
    Wait Until Element Is Visible    ${wellbeing_reports_container}
    ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%a %e.+%m+%B %Y
    ${day_date}    ${month}    ${month_year}    Split String    ${expected_date}    +
    ${month}    Convert To Integer    ${month}
    ${day_date}    Remove String    ${day_date}    ${SPACE}
    ${expected_daydate}    Strip String    ${day_date}${month}.
    Sleep    1
    ${day}    Execute Javascript    return document.getElementsByClassName('date-name')[0].innerText;
    ${date}    Execute Javascript    return document.getElementsByClassName('date')[0].innerText;
    ${actual_daydate}    Strip String    ${day}${date}
    ${actual_month_year}    Execute Javascript
    ...    return document.getElementsByClassName('timeline-month-title type--heading-6')[0].innerText;
    Should Be Equal    ${expected_daydate}    ${actual_daydate}
    Should Be Equal    ${actual_month_year}    ${month_year}
    Diary Values Should Be Correct

Diary Values Should Be Correct
    Wait Until Element Is Visible    ${wellbeing_reports_container}
    Wait Until Element Is Visible    (${diary_timeline_entry})[2]
    Scroll Element Into View    (${diary_timeline_entry})[2]
    Sleep    1
    @{expected_diary_values}    Create List    ${actual_ave}    ${ENERGY}    ${APPETITE}    ${OPTIMISM}
    ${diary_average_rate}    Execute Javascript
    ...    return document.getElementsByClassName('wellbeing-indicator')[0].innerText;
    ${diary_energy}    Execute Javascript
    ...    return document.getElementsByClassName('wellbeing-indicator invert')[0].innerText;
    ${diary_energy_int}    Convert To Integer    ${diary_energy}
    ${diary_appetite}    Execute Javascript
    ...    return document.getElementsByClassName('wellbeing-indicator invert')[1].innerText;
    ${diary_appetite_int}    Convert To Integer    ${diary_appetite}
    ${diary_optimism}    Execute Javascript
    ...    return document.getElementsByClassName('wellbeing-indicator invert')[2].innerText;
    ${diary_optimism_int}    Convert To Integer    ${diary_optimism}
    ${actual_diary_values}    Create List
    ...    ${diary_average_rate}
    ...    ${diary_energy_int}
    ...    ${diary_appetite_int}
    ...    ${diary_optimism_int}
    Should Be Equal    ${actual_diary_values}    ${expected_diary_values}

User Sets Values For Well-being And Selects Next
    [Arguments]    ${action}=new
    IF    '${action}'=='update'    Reset Wellbeing Rating
    ${energy}    Rate Well Being With Slider Randomly
    Set Test Variable    ${ENERGY}    ${energy}
    Click Wellbeing Next Button
    IF    '${action}'=='update'    Reset Wellbeing Rating
    ${appetite}    Rate Well Being With Slider Randomly
    Set Test Variable    ${APPETITE}    ${appetite}
    Click Wellbeing Next Button
    IF    '${action}'=='update'    Reset Wellbeing Rating
    ${optimism}    Rate Well Being With Slider Randomly
    Set Test Variable    ${OPTIMISM}    ${optimism}
    Click Wellbeing Next Button

Note Is Not Visible To Clinic Users
    [Arguments]    ${ssn}
    Login As Nurse
    Search Patient By Identity Code    ${ssn}
    Navigate To Timeline Tab
    Page Should Not Contain    ${clinic_energy}
    Page Should Not Contain    ${clinic_appetite}
    Page Should Not Contain    ${clinic_optimism}
    Page Should Not Contain    ${NOTES}

Select Patient
    ${ssn}    Get From Dictionary    ${CLINIC_PATIENT_3}    ssn
    Search Patient By Identity Code    ${ssn}

User Selects Note From The Timeline
    Wait Until Element Is Enabled    ${diary_entry}
    Try To Click Element    ${diary_entry}
    Wait Until Element Is Visible    ${diary_entry_modal}

Note Details Are Displayed
    Wait Until Element Is Visible    ${modal_date}
    Wait Until Element Is Visible    ${modal_average}
    Wait Until Element Is Visible    ${modal_energy}
    Wait Until Element Is Visible    ${modal_appetite}
    Wait Until Element Is Visible    ${modal_optimism}
    Wait Until Element Is Visible    ${modal_note}
    Wait Until Element Is Visible    ${edit_button}
    Wait Until Element Is Visible    ${modal_close_button}

User Selects Edit
    Wait Until Element Is Enabled    ${edit_button}
    Try To Click Element    ${edit_button}
    Wait Until Element Is Enabled    ${diary_entry_modal}
    Wait Until Element Is Enabled    ${modal_header_label}

User Edits Note Details
    User Updates Date For The Note
    User Clicks Wellbeing Average
    User Sets Values For Well-being And Selects Next    action=update
    Average Of The Set Well-being Rates Is Displayed In The Note
    Verify Answered Wellbeing Rates
    User Writes Notes About The Day

User Is Directed To Diary
    Wait Until Element Is Visible    ${wellbeing_reports_container}

User Updates Date For The Note
    # TODO: Get current date before updating
    Wait Until Element Is Visible    ${wellbeing_date}
    Execute Javascript    document.querySelector("${wellbeing_date_jelement}").removeAttribute('readonly')
    Try To Click Element    ${wellbeing_date}
    # TODO: Get new date

User Clicks Wellbeing Average
    Wait Until Element Is Enabled    ${wellbeing_ave}
    Wait Until Keyword Succeeds    20s    1s    SeleniumLibrary.Click Element    ${wellbeing_ave}
    Wait Until Element Is Enabled    ${energy_level_modal}
    Wait Until Page Contains Element    ${energy_level_slider}

# Used in User Reset Values For Well-being And Selects Next

Reset Wellbeing Rating
    Wait Until Element Is Enabled    ${slider_button}
    ${slider_position}    Get Vertical Position    ${slider_button}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        ${base_position}    Convert To Integer    328
    ELSE
        ${base_position}    Convert To Integer    384
    END
    IF    ${slider_position}>${base_position}
        ${target}    Evaluate    (${slider_position}-${base_position})/28
    ELSE IF    ${slider_position}<${base_position}
        ${target}    Evaluate    (${base_position}-${slider_position})/28
    ELSE IF    ${slider_position}==${base_position}
        ${target}    Set Variable    ${base_position}
    ELSE
        ${target}    Set Variable    ${None}
    END
    ${target}    Evaluate    ${target}*28
    IF    ${slider_position}>${base_position} or ${slider_position}==${base_position}
        ${target}    Evaluate    (${target}*(-1))
    ELSE IF    ${slider_position}<${base_position}
        ${target}    Evaluate    ${target}*1
    ELSE
        ${target}    Set Variable    ${None}
    END
    ${target}    Convert To Integer    ${target}
    IF    ${target}!=-10752
        Drag And Drop By Offset    ${slider_button}    0    ${target}
    END

Get Today's Date
    ${current_date}    Get Current Date    exclude_millis=yes    result_format=%a+%e+%-m+%B %Y
    ${current_day}    ${current_day_date}    ${current_month}    ${current_month_year}    Split String
    ...    ${current_date}
    ...    +
    Set Global Variable    ${current_day}
    ${current_day_date}    Convert To Integer    ${current_day_date}
    Set Global Variable    ${monday_day_date}    ${current_day_date}
    ${month}    Convert To Integer    ${current_month}
    ${current_daydate}    Strip String    ${current_day}${current_day_date}.${current_month}.
    Set Global Variable    ${current_daydate}
    Set Global Variable    ${current_month_year}

Get Current Displayed Date On Diary Timeline
    [Arguments]    ${web_or_native_app}=web
    Wait Until Element Is Visible    ${wellbeing_reports_container}
    IF    '${web_or_native_app}'=='web'
        Get Day and Date In Diary Web
    ELSE
        Get Day and Date In Diary Native App
    END

Get Day and Date In Diary Web
    ${day}    Execute Javascript    return document.getElementsByClassName('date-name')[0].innerText;
    ${date}    Execute Javascript    return document.getElementsByClassName('date')[0].innerText;
    ${actual_daydate}    Strip String    ${day}${date}
    Set Global Variable    ${actual_daydate}
    ${actual_month_year}    Execute Javascript
    ...    return document.getElementsByClassName('timeline-month-title type--heading-6')[0].innerText;
    Set Global Variable    ${actual_month_year}

Get Day and Date In Diary Native App
    ${day}    Execute Script    return document.getElementsByClassName('date-name')[0].innerText;
    ${date}    Execute Script    return document.getElementsByClassName('date')[0].innerText;
    ${actual_daydate}    Strip String    ${day}${date}
    Set Global Variable    ${actual_daydate}
    ${actual_month_year}    Execute Script
    ...    return document.getElementsByClassName('timeline-month-title type--heading-6')[0].innerText;
    Set Global Variable    ${actual_month_year}

Verify Well-being Weekly Report On Monday Row
    Get Monday's Date
    # patient should always have a diary entry due to previous cases
    Wait Until Element Is Visible
    ...    xpath=(${diary_entry_day_row})[1]
    ${recent_diary_date_in_ui}    Generic: Execute Javascript
    ...    return document.getElementsByClassName('timeline-day-title')[0];
    ${recent_diary_date_in_ui}    Get text    ${recent_diary_date_in_ui}
    ${recent_diary_date_in_ui}    Remove String    ${recent_diary_date_in_ui}    ${\n}
    ${recent_diary_date_in_ui}    Fetch From Left    ${recent_diary_date_in_ui}    .
    ${recent_diary_date_in_ui}    Fetch From Right    ${recent_diary_date_in_ui}    n
    ${monday_day_date}    Convert To String    ${monday_day_date}
    IF    'Mon' in '${current_daydate}' or 'Mon' in '${recent_diary_date_in_ui}'
        IF    'native' in '${ENVIRONMENT}'
            # If today is Monday or if Monday is the latest diary entry, condition should be assessed
            Should Contain    ${recent_diary_date_in_ui}    ${monday_day_date}
            Wait Until Page Contains Element    ${your_well_being_text_element}    timeout=7s
            Scroll Element Into View    ${your_well_being_text_element}
            Sleep    1s
            Generic: Element Should Contain    xpath=${latest_diary_item}    Your wellbeing last week
        ELSE
            Skip
        END
    ELSE IF    'Mon' not in '${current_daydate}'
        # If today is not Monday, wellbeing summary is not displayed in the recent diary
        Should Not Contain    ${recent_diary_date_in_ui}    ${monday_day_date}
        Generic: Element Should Not Contain    xpath=${latest_diary_item}    Your wellbeing last week
    END

Verify Well-being Weekly Report On Native App
    Get Monday's Date
    # patient should always have a diary entry due to previous cases
    Wait Until Element Is Visible
    ...    xpath=(${diary_entry_day_row})[1]
    ${actual_day_date}    Execute Script    return document.getElementsByClassName('timeline-day-title')[0];
    ${actual_day_date}    Get text    ${actual_day_date}
    ${actual_day_date}    Remove String    ${actual_day_date}    ${\n}
    ${actual_day_date}    Fetch From Right    ${actual_day_date}    .
    IF    '${current_day}'=='Mon'
        ${monday_day_date}    Convert To String    ${monday_day_date}
        Should Contain    ${actual_day_date}    ${monday_day_date}
        ${wellbeing_text_displayed}    Execute Script
        ...    return document.getElementsByClassName('timeline-day-title')[0].parentElement.getElementsByClassName('title-container')[0].innerHTML
        ${wellbeing_text_displayed}    Get text    ${wellbeing_text_displayed}
        Should Contain    ${wellbeing_text_displayed}    Your wellbeing last week
    ELSE
        Should Not Contain    ${actual_day_date}    ${monday_day_date}
        ${status}    Run Keyword And Return Status
        ...    Execute Script
        ...    return document.getElementsByClassName('timeline-day-title')[0].parentElement.getElementsByClassName('title-container')[0].innerHTML
        Should Be Equal    ${status}    ${FALSE}
    END

Get Monday's Date
    Get Today's Date
    IF    '${current_day}'!='Mon'
        ${subtract_from_current}    Set Variable If    '${current_day}'=='Tue'    1 day
        ...    '${current_day}'=='Wed'    2 days
        ...    '${current_day}'=='Thu'    3 days
        ...    '${current_day}'=='Fri'    4 days
        ...    '${current_day}'=='Sat'    5 days
        ...    '${current_day}'=='Sun'    6 days
        ${current_date}    Get Current Date
        ${monday_date}    Subtract Time From Date
        ...    ${current_date}
        ...    ${subtract_from_current}
        ...    result_format=%a+%e+%B+%Y
        ${monday_day}    ${monday_day_date}    ${current_month}    ${current_month_year}    Split String
        ...    ${monday_date}
        ...    +
        Set Test Variable    ${monday_day_date}
        Set Test Variable    ${current_month}
    END
