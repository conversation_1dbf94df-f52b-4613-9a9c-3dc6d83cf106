*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}excel_helper${/}excel_helper.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}modules.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}feedback.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}native_app${/}app_symptom_form.resource
Library     String
Library     OperatingSystem
Library     XML


*** Variables ***
${radio_buttons_patient}                                //fe-radio-list//div[2]/span[1]/label
# ${qol_radio_buttons}    //div[@class='input-area']/child::span[1]/label
${qol_radio_buttons}                                    //nh-radio-list/descendant::input[@type='radio'][1]/following-sibling::label
${text_area}                                            //div//textarea
${number_field}                                         //input[contains(@type, "number")]
${questionnaires_summary_content}                       //div[contains(@class, "questionnaire-summary-content")]
${saved_summary_content}                                //symptom-summary-content
@{patient_list}
...                                                     N54624518
...                                                     N68139911
...                                                     N04078615
...                                                     N04078615
...                                                     N76789238
...                                                     N57901471
...                                                     N61097676
...                                                     N81079251
...                                                     N21522833
${save_questionnaire_button}                            //*[contains(text(),'Save')]
${questionnaire_next_button}                            (//button/descendant::div[contains(text(),"Next")])[last()]
${view_diary_button}                                    //*[@id="move-to-diary"]/button/div
${text_area}                                            //div//textarea
${number_field}                                         //input[contains(@type, "number")]
@{text_list}                                            ${EMPTY}
@{number_list}                                          ${EMPTY}
${diary}                                                Diary
${checkboxes}                                           //nh-checkbox-list/div/div[1]/div/label    # //fe-checkbox-list/div/div[2]/nh-checkbox-list/div[1]/div/label
${all_checkboxes}                                       //nh-checkbox-list/div/div/div/label    # //fe-checkbox-list/div/div[2]/nh-checkbox-list/div/div/label
${hads_psyko_yes_button}                                //*[@id="hads-yes"]/button/div
${other_symptom_description_field}                      xpath=(//*[@id="description"])[last()]
${questionnaire_summary_content}                        //*[contains(@class,'summary') or contains(@class,'content')]
${symptom_entry_sent_text}                              sent to the clinic
${move_to_home_button}                                  //*[@id="move-to-home"]/button
${symptom_options}                                      //div[@class='symptom-illustration-title']/div
${more_symptom_options}                                 //div[@id='wizard-more']/descendant::button
${question_is_mandatory_error}                          //div[@class='error-required-field']
${question_is_mandatory_text}                           REQUIRED FIELD
${information_by_caregiver_checkbox}                    //label[@for='next-of-kin']
${emergency_symptom_instructions_text}
...                                                     Your symptom description indicates that you might require immediate attention
...                                                     from a medical professional. Please seek help from an emergency health service provider in your area if your
...                                                     symptom gets more severe before a cancer nurse contacts you during the next working day.\n\nRead more information
...                                                     from your message inbox. Symptom description is also saved to your diary.
${emergency_symptom_instructions_text_2-1}
...                                                     Your symptom description indicates that you might require immediate attention from a medical professional. Please seek
...                                                     help from an emergency health service provider in your area and describe your situation.
${emergency_symptom_instructions_text_2-2}
...                                                     Read more information
...                                                     from your message inbox. You will be contacted by a cancer nurse during the next working day. The symptom description is also saved to your diary.
${emergency_symptom_ok_button}                          //*[@id='view-inbox']/button
${emergency_symptom_sent_to_clinic}                     //*[contains(text(),'Symptom entry sent to the clinic')]
${emergency_symptom_attention}                          //*[contains(text(),'Attention!')]
${emergency_symptom_instructions}                       //div[contains(@class, 'modal-content')]/descendant::div[@class='paragraph-description']
${add_symptom_close_button}                             //button[@class='close-button']
${symptoms_buttons}                                     //button[contains(@class,'symptom-item')]
${modal_symptom_entry_saved_to_diary}                   //div[@class='modal-dialog noona-dialog']
${modal_symptom_entry_saved_to_diary_instructions}      //div[@class='answer-container']/div/div
${modal_symptom_entry_saved_to_diary_text}
...                                                     The symptom is saved in the diary and will be included in the next symptom questionnaire. You
...                                                     can easily follow up the development of the symptom on the Noona homepage. Please
...                                                     contact the clinic if you need to talk to your nurse.
${modal_view_your_diary}                                //*[@id="move-to-diary"]/button
${create_new_symptom_entry_option}                      //label[contains(text(),'Create a new symptom entry')]
${symptom_entry_next_button}                            button-next
${modal_symptom_sent_to_clinic_instructions}            //p[contains(text(),'Your symptom entry will be reviewed by clinic pers')]
${modal_symptom_sent_to_clinic_text}
...                                                     Your symptom entry will be reviewed by clinic personnel. You will get a
...                                                     response in Noona within 2 working days. Symptom description is
...                                                     also saved to your diary.
${modal_go_to_homepage_button}                          //*[@id="move-to-home"]/button
${questionnaire_was_sent_login_button}                  login-button
${today_option_radio_button}                            //label[contains(text(),'Today')]
${symptom_report_save_button}                           //*[@id="submit-button"]/button
${aeq_questionnaire_next_button}                        //div[contains(text(),'Next')]
${aeq_symptoms_list}                                    //li[starts-with(@class, 'symptom-item')]
${yes_for_symptom}                                      //div[starts-with(@class, 'yes-no-buttons')]/div[2]/label
${no_for_symptom}                                       //div[starts-with(@class, 'yes-no-buttons')]/div[1]/label
${previous_symptom_diary_summary}                       //div[contains(concat(" ", normalize-space(@class), " "), " symptom-card ")]
${send_this_symptom_to_clinic_radio}                    //label[@for='earlier-symptom-action-send']
${modify_this_symptom_and_send_to_clinic_radio}         //label[@for='earlier-symptom-action-modify']
${changes_in_gen_health_title}                          //h1[text()="Changes in general state of health"]
# ${rate_gen_state_radios}    //h4[text()="{}"]/../../../following-sibling::div/div
${rate_gen_state_radios}                                //h4/span[text()="How would you rate your general state of health?"]/../../../../following-sibling::div/div
${distress_title}                                       //h1[text()="Distress"]
${gen_state_health_text_area_title}                     //h4/span[text()="If necessary, please give further details in the empty field."]
${gen_state_health_distress_slider}                     //div[@class='slider slider--vertical slider--invert']
${gen_state_health_weigh_title}                         //h1[text()="Weight"]
${gen_state_health_weight_field}                        weight
${symptom_questionnaire_radio_1}                        //div[starts-with(@class, 'radio')][1]/label
${symptom_questionnaire_checkbox_1}                     //div[starts-with(@class, 'checkbox')][1]/label
${symptom_info_is_up_to_date}                           xpath=(//input[starts-with(@id, 'earlier-')]/following-sibling::label)[1]
${send_to_clinic_button}                                xpath=(//div[contains(text(),"Send to clinic")])[last()]
${close_modal_button}                                   //button[@class='close-button']
${aeq_symptomatic_day_today}                            //td[@class='day middle tdoay-date']
${symptom_report_symptomatic_today}                     //td[@class='day today-date']
${aeq_symptomatic_day_yesterday}                        //td[@class='day middle tdoay-date']/preceding-sibling::td[1]
${symptom_report_symptomatic_yesterday}                 //td[@class='day today-date']/preceding-sibling::td[1]
${symptomatic_days_selected}                            (//div[@class='datepicker-days'])[last()]/descendant::td[starts-with(@class, 'day selected')]
${same_severity_button}                                 //*[@id="severity-same"]/button
${severity_decreased_button}                            //*[@id="severity-decreased"]/button
${patient_feedback_ok_to_contact_checkbox}              //label[text()="It is OK for Noona support to contact me about my feedback"]
${patient_feedback_rating}                              //input[@id='{}']/following-sibling::label
${mark_symptomatic_days_per_symptom}                    //h1[text()='{}']/../../../descendant::label[contains(text(),"Mark symptomatic days")]
${other_symptom_grading_mild}                           //*[@id="otherSymptomForm-other-symptom-grading-mild"]/../label
${previous_diary_symptom_summary}                       //div[@class='earlier-symptom-actions-container']
${patient_feedback_dialog_close_button}                 //*[@id='cancel-feedback']/button
${days_pain_most_severe}                                xpath=//*[@id='surgery-pain-days-since-surgery']
${multiple_automation_rules_emergency_instructions}
...                                                     Your symptom description indicates that you might require immediate attention from a medical professional.
...                                                     Please refer to further instructions in your inbox.
${attention_icon}                                       //*[@class="dialog-icon"]
${symptoms_in_list}                                     //div[@class='symptom-grid']/button
${questionnaires_with_radio_button}                     //nh-radio-list
${required_fields_error_message}                        //div[@class='error-required-field']
${required_fields_red_box}                              //*/div[@style='padding: 20px 8px 8px 8px; border: 2px solid #FA5858; border-radius: 10px']
${required_fields_red_box_1}                            //*/div[@style='padding: 20px 8px 8px; border: 2px solid rgb(250, 88, 88); border-radius: 10px;']
${answer_required_questions_error}                      Please answer the required question(s) above to continue.
${thank_you_header_text}                                Thank you for using Noona
${satisfaction_level_question}                          How satisfied have you been using Noona overall?
${symptom_entry_saved_to_diary_title}                   Symptom entry saved in your diary
${questionnaire_complete_login_button}                  //*[@id='move-to-login']/button
${stomach_eating_bowel_nausea_symptom_checkbox}         //*[@id='stomachEatingAndBowelForm-types-nausea']/..
${none_of_practical_problems_checkbox}                  //*[@id='practicalProblems']/..
${none_of_family_problems_checkbox}                     //*[@id='familyProblems']/..
${none_of_emotional_problems_checkbox}                  //*[@id='emotionalProblems']/..
${none_of_physical_problems_checkbox}                   //*[@id='physicalProblems']/..
${spiritual_religious_concerns_no_option}               //*[@id='distressAndProblemListForm-spiritualOrReligiousConcerns-distressAndProblemListForm-spiritual-or-religious-concerns-false']
${questionnaire_close_button}                           //button[@class="close-button"]
${questionnaire_cancel_button}                          //*[contains(@class, "button-cancel")]
${thank_you_for_your_answers_text}                      Thank you for your answers!
${questionnaire_from_link_sent_instructions_1}          The questionnaire has been sent to the clinic. It has also been saved to your diary.
${questionnaire_from_link_sent_instructions_2}          Please contact your clinic, if you need to talk to your nurse before your nurse reads your answers.
${questionnaire_was_sent_to_clinic_text}                The questionnaire was sent to the clinic
${login_to_continue_using_noona_text}                   To continue using Noona, please log in.
${information_entered_by_caregiver_in_summary}          //*[@class='symptom-summary-content']//span[text()="Information entered by a caregiver"]


*** Keywords ***
Login Patient
    [Arguments]    ${patient_email}
    Login.Login To Patient    ${patient_email}    ${DEFAULT_PASSWORD}
    Wait Until Page Contains    ${diary}

Login To Nurse
    Login.Login To Nurse    ${automated_tests_clinic}[default_user]    ${DEFAULT_PASSWORD}    clinic_name=${automated_tests_clinic}[name]
    ${patient_id}    Get From Dictionary    ${CLINIC_PATIENT}    ssn
    Set Suite Variable    ${PATIENT_ID}    ${patient_id}

Create Schedule
    [Arguments]    ${patient_id}    ${questionnaire}
    Nurse.Questionnaires.Add Schedule    ${patient_id}    ${questionnaire}
    Nurse.Questionnaires.Wait For Schedule To Be Sent

Complete Questionnaire
    [Arguments]    ${questionnaire_type}
    ${questionnaire_type}    Set Variable If
    ...    '${questionnaire_type}'=='Quality of life (15D)'
    ...    Quality of life questionnaire
    ...    ${questionnaire_type}
    ${unanswered_questionnaire}    Set Variable
    ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Unanswered"])[1]
    Wait Until Page Contains Element    ${unanswered_questionnaire}    20s
    SeleniumLibrary.Reload Page
    Wait Until Page Contains Element    ${unanswered_questionnaire}    20s
    SeleniumLibrary.Reload Page
    Wait Until Page Contains Element    ${unanswered_questionnaire}    20s
    Wait Until Element Is Visible    ${unanswered_questionnaire}
    Wait Until Element Is Enabled    ${unanswered_questionnaire}
    Sleep    1s
    Click Element    ${unanswered_questionnaire}
    Wait Until Page Contains Element    ${questionnaire_next_button}
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    Click Element    ${questionnaire_next_button}
    questionnaires.Check Summary

Complete QOL Questionnaire
    [Documentation]    User is already in the questionnaire
    Sleep    1
    Wait Until Page Contains Element    xpath=(${questionnaire_next_button})[last()]
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    Try To Click Element    xpath=(${questionnaire_next_button})[last()]
    Wait Until Page Contains Element    ${questionnaires_summary_content}

Save Questionnaire
    [Arguments]    ${questionnaire_type}    ${via_email_link}=no
    ${questionnaire_type}    Set Variable If
    ...    '${questionnaire_type}'=='Quality of life (15D)'
    ...    Quality of life questionnaire
    ...    ${questionnaire_type}
    Wait Until Page Contains Element    ${save_questionnaire_button}
    Wait Until Element Is Visible    ${save_questionnaire_button}
    Sleep    1s
    Try To Click Element    ${save_questionnaire_button}
    IF    '${questionnaire_type}'=='HADS'
        Click Yes On Mental Professional Question
    END
    IF    '${via_email_link}'=='no'
        Click View Diary After Saving Questionnaire    ${questionnaire_type}
    ELSE IF    '${via_email_link}'=='yes'
        Click Login After Saving Questionnaire
    END

Click View Diary After Saving Questionnaire
    [Arguments]    ${questionnaire_type}
    Wait Until Page Contains Element    ${view_diary_button}
    Text Should Be In The Page    Thank you for your answers!
    Sleep    1s
    Wait Until Element Is Visible    ${view_diary_button}
    Try To Click Element    ${view_diary_button}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Contains Element
        ...    xpath=(//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span)[1]
        Element Should Contain Text
        ...    xpath=(//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span)[1]
        ...    - Answered
    ELSE
        Wait Until Page Contains Element
        ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span)[1]
        Wait Until Element Contains
        ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span)[1]
        ...    - Answered
    END

Click Login After Saving Questionnaire
    Wait Until Page Contains    The questionnaire was sent to the clinic
    Wait Until Element Is Enabled    ${questionnaire_was_sent_login_button}
    Try To Click Element    ${questionnaire_was_sent_login_button}
    Wait Until Page Contains Element    ${landing_page_login_button}
    ${url}    Get Location
    Should Be Equal    ${url}    ${PATIENT_LOGIN_URL}

Check And Write To Text Area
    ${text_area_exists}    Run Keyword And Return Status    Page Should Contain Element    ${text_area}
    IF    ${text_area_exists}==True    questionnaires.Write To Text Area

Check And Write To Number Field
    ${number_field_exists}    Run Keyword And Return Status    Page Should Contain Element    ${number_field}
    IF    ${number_field_exists}==True    questionnaires.Write To Number Field

Check And Tick Checkboxes
    ${checkboxes_exists}    Run Keyword And Return Status    Page Should Contain Element    ${checkboxes}
    IF    ${checkboxes_exists}==True    questionnaires.Tick Checkboxes

Check And Select Radio Buttons
    ${radio_buttons_exists}    Run Keyword And Return Status    Page Should Contain Element    ${radio_buttons_patient}
    ${qol_radio_buttons_exist}    Run Keyword And Return Status    Page Should Contain Element    ${qol_radio_buttons}
    IF    ${radio_buttons_exists}==True
        questionnaires.Select Radio Buttons
    ELSE IF    ${qol_radio_buttons_exist}==True
        questionnaires.Select Radio Buttons    elements=${qol_radio_buttons}
    END

Select Radio Buttons
    [Arguments]    ${elements}=${radio_buttons_patient}
    Wait Until Page Contains Element    ${elements}    20s
    @{elements}    Get WebElements    ${elements}
    FOR    ${element}    IN    @{elements}
        Wait Until Keyword Succeeds    20s    1s    Click Element    ${element}
    END

Tick Checkboxes
    Wait Until Page Contains Element    ${checkboxes}    20s
    @{boxes}    Get WebElements    ${checkboxes}
    FOR    ${box}    IN    @{boxes}
        Wait Until Keyword Succeeds    20s    1    Click Element    ${box}
    END

Tick All Checkboxes
    Wait Until Page Contains Element    ${all_checkboxes}    20s
    @{boxes}    Get WebElements    ${all_checkboxes}
    FOR    ${box}    IN    @{boxes}
        Wait Until Keyword Succeeds    20s    1    Click Element    ${box}
    END

Write To Text Area
    Wait Until Page Contains Element    ${text_area}    20s
    @{text_list}    Create List
    @{text_areas}    Get WebElements    ${text_area}
    FOR    ${area}    IN    @{text_areas}
        ${random_madness}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
        ${status}    Run Keyword And Return Status    Element Should Be Visible    ${area}
        IF    ${status}    Input Text    ${area}    ${random_madness}
        Append To List    ${text_list}    ${random_madness}
    END
    Set Test Variable    @{text_list}

Write To Number Field
    Wait Until Page Contains Element    ${number_field}    20s
    @{number_list}    Create List
    @{number_fields}    Get WebElements    ${number_field}
    FOR    ${field}    IN    @{number_fields}
        ${random_madness}    Generate Random String    2    [NUMBERS]
        Wait Until Keyword Succeeds    20s    1s    Input Text    ${field}    3${random_madness}
        Append To List    ${number_list}    ${random_madness}
    END
    Set Test Variable    @{number_list}

Check Summary
    Wait Until Page Contains Element    ${questionnaires_summary_content}    20s
    FOR    ${number}    IN    @{number_list}
        Element Should Contain    ${questionnaires_summary_content}    ${number}
    END
    FOR    ${text}    IN    @{text_list}
        Element Should Contain    ${questionnaires_summary_content}    ${text}
    END

Click Yes On Mental Professional Question
    Wait Until Page Contains Element    ${hads_psyko_yes_button}
    SeleniumLibrary.Click Element    ${hads_psyko_yes_button}

Check Saved Questionnaire
    [Arguments]    ${questionnaire_type}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Element Is Visible
        ...    xpath=(//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        Wait Until Element Is Visible
        ...    xpath=(//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        Sleep    1s
        Try To Click Element
        ...    xpath=(//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        Wait Until Page Contains Element    ${saved_summary_content}    20s
        Wait Until Element Is Visible    ${saved_summary_content}    20s
    ELSE
        Wait Until Element Is Visible
        ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        SeleniumLibrary.Reload Page
        Wait Until Element Is Visible
        ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        Sleep    1s
        Try To Click Element
        ...    (//nh-timeline-questionary//span[text()="${questionnaire_type}"]/following-sibling::span[text()="- Answered"])[1]
        Wait Until Page Contains Element    ${saved_summary_content}    20s
        Wait Until Element Is Enabled    ${saved_summary_content}    20s
    END
    Sleep    1s
    FOR    ${number}    IN    @{number_list}
        Generic: Element Should Contain    ${saved_summary_content}    ${number}
    END
    FOR    ${text}    IN    @{text_list}
        Generic: Element Should Contain    ${saved_summary_content}    ${text}
    END

Get Random Patient From The List
    Create Patient Dictionary
    ${patient}    Evaluate    random.choice(@{patient_list})    random
    Set Test Variable    ${patient_ssn}    ${patient}
    ${patient_email}    Get From Dictionary    ${patient_dict}    ${patient_ssn}
    Set Test Variable    ${patient_email}

Create Patient Dictionary
    ${patient_dict}    Get File    ${EXECDIR}${/}resources${/}nurse${/}patient_list.json
    ${patient_dict}    Evaluate    json.loads("""${patient_dict}""")    json
    Convert To Dictionary    ${patient_dict}
    Log    ${patient_dict}
    Set Test Variable    ${patient_dict}

Complete Other Symptom Questionnaire
    Wait Until Page Contains Element    ${radio_buttons_option_1}
    Check And Select Radio Button Option One
    ${random_desc}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
    Set Test Variable    ${random_desc}
    Input Text    ${other_symptom_description_field}    ${random_desc}
    Wait Until Element Is Visible    ${questionnaire_next_button}

Complete Other Symptom Questionnaire With Severe Symptom
    Changes In Gen State Of Health Is Displayed
    ${now}    Get Current Date
    Input Text    ${other_symptom_description_field}    Other symptom description ${now}
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Sleep    1
    Click Next Button
    Send Symptom Questionnaire To Clinic

Complete Other Symptom Form
    [Arguments]    ${severity}
    Add A Symptom    Other symptom
    Wait Until Page Contains Element    ${radio_buttons_option_1}
    ${random_desc}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
    Set Test Variable    ${random_desc}
    Input Text    ${other_symptom_description_field}    ${random_desc}
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    ${severity}
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No

Create A New Symptom Entry
    Sleep    3s
    ${create_new_entry}    Run Keyword And Return Status
    ...    Scroll Element Into View
    ...    ${create_new_symptom_entry_option}
    IF    ${create_new_entry}
        Click Element    ${create_new_symptom_entry_option}
        Click Element    ${aeq_questionnaire_next_button}
    END

Select Specific Answer To Question
    [Arguments]    ${question}    ${answer}    ${questionnaire}=default
    ${count}    Get Element Count    //h4/span
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        Set Test Variable    ${current_question}    ${question}
        ${question_text}    Get Text    (//h4/span)[${INDEX}]
        IF    '${question}'=='${question_text}'
            Select Answer Based On Question    ${INDEX}    ${answer}    ${questionnaire}
            BREAK
        END
    END

Select Answer To Question
    # TODO: Moving forward, please use this keyword instead of the one above it Select Specific Answer To Question for hybrid cases
    [Arguments]    ${question}    ${answer}
    ${today_option1}    Set Variable
    ...    //div/h4/span[text()="${question}"]/../../../descendant::div/div/label[contains(text(),"${answer}")]
    ${today_option2}    Set Variable
    ...    //div/h4/span[text()="${question}"]/../../../../following-sibling::div/div/label[text()="${answer}"]
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${today_option1}    timeout=5
    ${element}    Set Variable If    ${status}    ${today_option1}    ${today_option2}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}

Select Symptomatic Day
    [Documentation]    symptom_day could be Today, Symptom is chronic (persistent, long-standing, long-term)
    [Arguments]    ${symptom_day}
    ${element}    Set Variable
    ...    //div/h4/span[text()="When did you have this symptom?"]/../../../descendant::div/div/label[text()=" ${symptom_day} "]
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}

Select Answer Based On Question
    [Arguments]    ${question_index}    ${answer}    ${questionnaire}
    Sleep    1
    ${status1}    Run Keyword And Return Status
    ...    Element Should Be Visible
    ...    (//h4/span)[${question_index}]/../../../../following-sibling::div/descendant::label
    ${status2}    Run Keyword And Return Status
    ...    Element Should Be Visible
    ...    (//h4/span)[${question_index}]/../following-sibling::div/descendant::label
    ${element}    Set Variable If
    ...    ${status1}
    ...    (//h4/span)[${question_index}]/../../../../following-sibling::div/descendant::label
    ...    ${status2}
    ...    (//h4/span)[${question_index}]/../following-sibling::div/descendant::label
    ${count}    Get Element Count    ${element}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${answer_text}    Get Text    ${element}\[${INDEX}]
        IF    '${answer_text}'=='${answer}'
            Try To Click Element    ${element}\[${INDEX}]
            BREAK
        END
    END

Select Mark Symptomatic Days Based On Symptom
    [Documentation]    Used when more than 1 symptom are selected and there are symptomatic days for both
    [Arguments]    ${symptom}
    ${element}    Format String    ${mark_symptomatic_days_per_symptom}    ${symptom}
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}

Information Entered By Caregiver Checkbox Is Visible
    Element Should Be Visible    ${information_by_caregiver_checkbox}
    Element Should Be Enabled    ${information_by_caregiver_checkbox}

Questionnaire Summary Is Displayed
    [Documentation]    Checks only if the summary area is visible and not if the summary content is correct
    Wait Until Page Contains    Summary
    Wait Until Page Contains Element    ${questionnaire_summary_content}

Send Symptom To Clinic
    Wait Until Element Is Visible    xpath=(${submit_symptom_button})[last()]    5s
    Try To Click Element    xpath=(${submit_symptom_button})[last()]

Go To Patient Homepage
    Wait Until Page Contains    ${symptom_entry_sent_text}
    Page Should Not Contain Element    ${attention_icon}
    Page Should Not Contain Element    ${emergency_symptom_attention}
    Try To Click Element    ${move_to_home_button}

Select Symptom From List
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    (${symptom_options})[1]
    ${count}    Get Element Count    ${symptom_options}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${symptom_name}    Get Text    (${symptom_options})[${INDEX}]
        IF    '${symptom_name}'=='${symptom}'
            Scroll Element Into View    (${symptom_options})[${INDEX}]
            Sleep    1s
            Try To Click Element    (${symptom_options})[${INDEX}]
            BREAK
        END
    END

Select Symptom From More Options
    [Arguments]    ${symptom}
    Show More Symptoms
    Wait Until Page Contains Element    ${more_symptom_options}
    Try To Click Element    //div[contains(text(),"${symptom}")]

Question Is Mandatory Error Is Displayed
    [Documentation]    error_count depends on expected number of error messages displayed
    [Arguments]    ${error_count}
    Sleep    1s
    ${count}    Generic: Get Element Count    ${question_is_mandatory_error}
    IF    ${count}==0    Fail    Error message should be displayed but it is not
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${required_fields_error_message_count}    Generic: Get Element Count    ${required_fields_error_message}
        ${required_fields_red_box_count}    Generic: Get Element Count    ${required_fields_red_box}
        ${required_fields_red_box_count_1}    Generic: Get Element Count    ${required_fields_red_box_1}
        ${required_fields_error_message_count}    Convert To Integer    ${required_fields_error_message_count}
        ${required_fields_red_box_count}    Convert To Integer    ${required_fields_red_box_count}
        ${required_fields_red_box_count_1}    Convert To Integer    ${required_fields_red_box_count_1}
        ${required_fields_red_box_count_total}    Evaluate
        ...    ${required_fields_red_box_count} + ${required_fields_red_box_count_1}
        ${error_count}    Convert To Integer    ${error_count}
        Should Be Equal    ${error_count}    ${required_fields_error_message_count}    ${required_fields_red_box_count}
        Should Be Equal    ${error_count}    ${required_fields_red_box_count_total}
        ${text}    Get Text    xpath=(${question_is_mandatory_error})[${INDEX}]
        Should Be Equal    ${question_is_mandatory_text}    ${text}
    END
    Text Should Be In The Page    Please answer the required question(s) above to continue.

Click Next Button
    Wait Until Element Is Visible    xpath=(${questionnaire_next_button})[last()]
    Scroll Element Into View    xpath=(${questionnaire_next_button})[last()]
    Sleep    1s
    Try To Click Element    xpath=(${questionnaire_next_button})[last()]

Emergency Priority Symptom Is Displayed
    [Arguments]    ${expected_instructions}=${emergency_symptom_instructions_text}
    Wait Until Element Is Visible    ${emergency_symptom_sent_to_clinic}
    Element Should Be Visible    ${emergency_symptom_attention}
    Element Should Be Visible    ${emergency_symptom_instructions}
    ${instruction}    Get Text    ${emergency_symptom_instructions}
    Should Be Equal    ${instruction}    ${expected_instructions}
    Click Emergency Symptom Ok Button

Click Emergency Symptom Ok Button
    Try To Click Element    xpath=${emergency_symptom_ok_button}

Add A Symptom
    [Arguments]    ${symptom}
    Run Keyword And Ignore Error    Wait Until Page Contains    Add a symptom    2s
    Run Keyword And Ignore Error    Wait Until Page Contains    Report a symptom to clinic    2s
    ${symptom_exists}    Run Keyword And Return Status    Wait Until Page Contains    ${symptom}
    ${show_more_exists}    Run Keyword And Return Status    Text Should Be In The Page    Show more
    IF    ${symptom_exists}==False and ${show_more_exists}==True
        Select Symptom From More Options    ${symptom}
    ELSE IF    ${symptom_exists}
        Try To Click Element    //div[contains(text(),"${symptom}")]
    END

Symptoms List Is Correct
    [Arguments]    @{symptom_list}
    Wait Until Element Is Visible    ${add_symptom_close_button}
    Sleep    1s
    ${count}    Get Element Count    ${symptoms_buttons}
    @{actual_symptoms}    Create List
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${symptom_name}    Get Text    //button[contains(@class,'symptom-item')][${INDEX}]/div
        Append To List    ${actual_symptoms}    ${symptom_name}
    END
    Lists Should Be Equal    ${symptom_list}    ${actual_symptoms}

Symptom And Icon Are In The Symptom List
    [Arguments]    ${symptom}    ${svg_class_name}
    Wait Until Element Is Visible    ${add_symptom_close_button}
    Sleep    1s
    ${count}    Generic: Get Element Count
    ...    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${symptom_name}    Get Text
        ...    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button[${INDEX}]/div[1]
        ${symptom_icon}    Get Element Attribute
        ...    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button[${INDEX}]/ds-icon[1]
        ...    class
        IF    '${symptom_name}'=='${symptom}'
            Should Contain    ${symptom_icon}    ${svg_class_name}
            BREAK
        END
    END

Get Expected Symptoms Combination From Excel
    [Documentation]    Gets the expected combination of 2 symptoms in excel
    [Arguments]    ${module1}    ${module2}
    # TODO: Combine more than 2 modules
    @{symptoms_1}    Get Expected Symptoms Per Module    ${module1}
    @{symptoms_2}    Get Expected Symptoms Per Module    ${module2}
    ${symptoms}    Combine Lists    ${symptoms_1}    ${symptoms_2}
    ${symptoms}    Remove Duplicates    ${symptoms}
    ${other_symptom}    Run Keyword And Return Status    List Should Contain Value    ${symptoms}    Other symptom
    IF    ${other_symptom}
        Remove Values From List    ${symptoms}    Other symptom
    END
    IF    ${other_symptom}    Append To List    ${symptoms}    Other symptom
    @{expected_symptoms}    Create List
    ${count}    Get Length    ${symptoms}
    FOR    ${INDEX}    IN RANGE    0    ${count}
        ${i}    Evaluate    ${INDEX} + 2
        ${value}    Get From List    ${symptoms}    ${INDEX}
        Append To List    ${expected_symptoms}    ${value}
    END
    Log List    ${expected_symptoms}
    Set Test Variable    ${expected_symptoms}

Get Expected Symptoms Per Module
    [Documentation]    Gets the expected symptoms of module indicated in excel file
    [Arguments]    ${module}
    ${module_index}    Get Index Of Value From Excel Row    1    Symptoms    ${module}
    ${symptoms_list}    Read Excel Column    ${module_index}    sheet_name=Symptoms
    Remove From List    ${symptoms_list}    0    # removes the header
    Remove Values From List    ${symptoms_list}    ${NONE}    # removes blank values
    RETURN    @{symptoms_list}

Get Symptoms List As Patient
    [Documentation]    Gets the list of symptoms through Ask about symptoms
    Wait Until Element Is Visible    //button[@class='close-button']
    Sleep    1s
    ${count}    Generic: Get Element Count
    ...    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button
    @{actual_symptoms}    Create List
    Set Test Variable    ${actual_symptoms}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${symptom_name}    Get Text
        ...    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button\[${INDEX}]/div
        IF    "${symptom_name}"!="Show more"
            Append To List    ${actual_symptoms}    ${symptom_name}
        ELSE IF    "${symptom_name}"=="Show more"
            Get More Symptoms List    ${INDEX}
        END
    END
    Log List    ${actual_symptoms}
    Set Test Variable    ${actual_symptoms}

Get More Symptoms List
    [Arguments]    ${index}
    Click Element    //div[contains(concat(" ", normalize-space(@class), " "), " symptom-grid ")]/button\[${index}]/div
    Wait Until Element Is Visible    ${more_symptom_options}
    ${count}    Generic: Get Element Count    ${more_symptom_options}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${symptom_name}    Get Text    ${more_symptom_options}\[${INDEX}]
        Append To List    ${actual_symptoms}    ${symptom_name}
    END

Symptom Entry Sent To Clinic
    Sleep    1s
    Wait Until Element Is Visible    ${modal_symptom_sent_to_clinic_instructions}
    Compare Modal Instructions
    Click Go To Homepage

Compare Modal Instructions
    Element Should Be Visible    ${modal_symptom_sent_to_clinic_instructions}
    ${modal_message}    Get Text    ${modal_symptom_sent_to_clinic_instructions}
    Should Be Equal    ${modal_message}    ${modal_symptom_sent_to_clinic_text}

Click Go To Homepage
    Wait Until Element Is Visible    xpath=${modal_go_to_homepage_button}
    Try To Click Element    xpath=${modal_go_to_homepage_button}

Symptom Entry Saved In Diary
    Wait Until Element Is Visible    ${modal_symptom_entry_saved_to_diary}
    Element Should Be Visible    ${modal_symptom_entry_saved_to_diary_instructions}
    Element Should Be Visible    ${modal_view_your_diary}
    ${modal_isntructions}    Get Text    ${modal_symptom_entry_saved_to_diary_instructions}
    Should Be Equal    ${modal_isntructions}    ${modal_symptom_entry_saved_to_diary_text}
    Click View Your Diary

Click View Your Diary
    Sleep    1
    ${status_diary}    Run Keyword And Return Status    Wait Until Element Is Visible    ${modal_view_your_diary}    5s
    ${status_home}    Run Keyword And Return Status    Wait Until Element Is Visible    ${move_to_home_button}    5s
    IF    ${status_diary}
        Try To Click Element    ${modal_view_your_diary}
    ELSE IF    ${status_home}
        Try To Click Element    ${move_to_home_button}
    END
    # TODO: Verification of reported symptom to diary

Save Symptom Report
    Wait Until Element Is Visible    ${symptom_report_save_button}
    Sleep    1
    Try To Click Element    ${symptom_report_save_button}

Select Yes For Symptoms
    [Documentation]    Selects No for the ones that are not chosen
    [Arguments]    @{symptoms}
    Wait Until Element Is Visible    ${aeq_questionnaire_next_button}    timeout=20s
    Wait Until Element Is Visible    ${aeq_symptoms_list}\[last()]
    ${count}    Generic: Get Element Count    ${aeq_symptoms_list}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    ${aeq_symptoms_list}\[${INDEX}]/div[1]
        ${status}    Run Keyword And Return Status    List Should Contain Value    ${symptoms}    ${text}
        IF    ${status}
            Click Element    xpath=(${yes_for_symptom})\[${INDEX}]
        ELSE
            Click Element    xpath=(${no_for_symptom})\[${INDEX}]
        END
    END
    Click Element    ${aeq_questionnaire_next_button}

Select No For All Symptoms
    Wait Until Element Is Visible    ${aeq_questionnaire_next_button}    timeout=20s
    Wait Until Element Is Visible    ${aeq_symptoms_list}\[last()]
    ${count}    Generic: Get Element Count    ${aeq_symptoms_list}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        Click Element    xpath=(${no_for_symptom})\[${INDEX}]
    END
    Click Element    ${aeq_questionnaire_next_button}

Verify If Patient Can See Questionnaire Form From Link
    [Arguments]    ${visible}    ${email}    ${clinic}
    @{message_data}    Patient Received Invitation To Answer AEQ  ${email}    ${clinic}
    Open URL In Chrome    ${message_data}[0]
    Accept All Cookies If Visible
    IF    '${visible}'=='Yes'
       Wait Until Element Is Visible    ${aeq_questionnaire_next_button}    timeout=20s
    ELSE
       Sleep    20s    #needs to wait for 20s to make sure questionnaire does not show up
       Element Should Not Be Visible    ${aeq_questionnaire_next_button}
    END

Select Yes For First Symptom
    [Documentation]    Selects No for the ones that are not chosen
    Wait Until Element Is Visible    ${aeq_questionnaire_next_button}
    Wait Until Element Is Visible    ${aeq_symptoms_list}\[last()]
    ${count}    Generic: Get Element Count    ${aeq_symptoms_list}
    Try To Click Element    xpath=(${yes_for_symptom})\[1]
    FOR    ${INDEX}    IN RANGE    2    ${count}+1
        Click Element    xpath=(${no_for_symptom})\[${INDEX}]
    END
    Click Element    ${aeq_questionnaire_next_button}

Select Yes For Last Symptom
    [Documentation]    Selects No for the ones that are not chosen
    Wait Until Element Is Visible    ${aeq_questionnaire_next_button}
    Wait Until Element Is Visible    ${aeq_symptoms_list}\[last()]
    ${count}    Get Element Count    ${aeq_symptoms_list}
    FOR    ${INDEX}    IN RANGE    1    ${count}
        Click Element    (${no_for_symptom})\[${INDEX}]
    END
    Try To Click Element    (${yes_for_symptom})\[last()]

Previously Saved Symptom Summary Is Displayed
    Wait Until Page Contains    Symptom entry in your diary
    Page Should Contain    You have created a symptom entry in your diary within the last 24 hours.
    ...    Do you want to send it to the clinic?
    Scroll Element Into View    ${previous_symptom_diary_summary}
    Element Should Be Visible    ${previous_symptom_diary_summary}

Select "Send This Symptom Entry" To Clinic
    Wait Until Element Is Visible    ${send_this_symptom_to_clinic_radio}
    Scroll Element Into View    ${send_this_symptom_to_clinic_radio}
    Try To Click Element    ${send_this_symptom_to_clinic_radio}

Select "Modify This Symptom Entry"
    Wait Until Element Is Visible    ${modify_this_symptom_and_send_to_clinic_radio}
    Scroll Element Into View    ${modify_this_symptom_and_send_to_clinic_radio}
    Try To Click Element    ${modify_this_symptom_and_send_to_clinic_radio}

Changes In Gen State Of Health Is Displayed
    Gen State Of Health Rate Question Is Displayed
    Click Element    ${symptom_questionnaire_radio_1}
    Click Element    ${aeq_questionnaire_next_button}
    Gen State Of Health Distress Question Is Displayed
    IF    'native' not in '${ENVIRONMENT}'    Rate With Vertical Slider
    Click Element    ${aeq_questionnaire_next_button}
    Gen State Of Health Weight Question Is Displayed
    Input Text    xpath=//*[@id='${gen_state_health_weight_field}']    56
    Click Element    ${aeq_questionnaire_next_button}

Gen State Of Health Rate Question Is Displayed
    Wait Until Element Is Visible    ${changes_in_gen_health_title}
    Wait Until Element Is Visible    ${aeq_questionnaire_next_button}
    Scroll Element Into View    ${aeq_questionnaire_next_button}
    Page Should Contain Element    ${changes_in_gen_health_title}
    Wait Until Page Contains    Please rate your general state of health using the scale.    timeout=5s
    # Both labels are correct but some modules have different wordings.
    ${status}    Run Keyword And Return Status
    ...    Page Should Contain
    ...    How would you rate your general state of health?
    ${rate_gen_health_title}    Set Variable If
    ...    ${status}
    ...    How would you rate your general state of health?
    ...    Rate your general state of health:
    ${rate_gen_state_radios}    Format String    ${rate_gen_state_radios}    ${rate_gen_health_title}
    Page Should Contain Element    ${rate_gen_state_radios}
    Page Should Contain Element    ${gen_state_health_text_area_title}
    Page Should Contain Element    ${gen_state_health_text_area_title}/../../../../../following-sibling::div/textarea

Gen State Of Health Distress Question Is Displayed
    Page Should Contain Element    ${distress_title}
    Text Should Be In The Page    Please evaluate have you experienced distress.
    Text Should Be In The Page
    ...    Please select the number (0-10) that best describes how much distress you have been experiencing in the past week including today.
    Page Should Contain Element    ${gen_state_health_distress_slider}

Gen State Of Health Weight Question Is Displayed
    Page Should Contain Element    ${gen_state_health_weigh_title}
    Text Should Be In The Page    Please update your weight information.
    Text Should Be In The Page
    ...    What is your weight? If you are unable to update the information, you can skip the question.
    Element Should Be Visible    xpath=//*[@id='${gen_state_health_weight_field}']
    IF    'native' not in '${ENVIRONMENT}'
        Element Should Be Enabled    ${gen_state_health_weight_field}
    END

Check And Select Symtom Radio Button
    ${element_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${symptom_questionnaire_radio_1}
    IF    ${element_exists}==True    questionnaires.Select Symptom Radio Buttons

Select Symptom Radio Buttons
    Wait Until Page Contains Element    ${symptom_questionnaire_radio_1}    20s
    @{elements}    Get WebElements    ${symptom_questionnaire_radio_1}
    FOR    ${element}    IN    @{elements}
        Try To Click Element    ${element}
    END

Check And Select Symptom Checkbox
    ${checkboxes_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${symptom_questionnaire_checkbox_1}
    IF    ${checkboxes_exists}==True    questionnaires.Select Symptom Checkbox

Select Symptom Checkbox
    Wait Until Page Contains Element    ${symptom_questionnaire_checkbox_1}    20s
    @{elements}    Get WebElements    ${symptom_questionnaire_checkbox_1}
    FOR    ${element}    IN    @{elements}
        Click Element    ${element}
    END

Evaluate Previous Symptom
    [Arguments]    ${previous_symptom}
    IF    '${previous_symptom}'=='up_to_date'
        Select Symptom Info Is Up To Date
    ELSE IF    '${previous_symptom}'=='new_entry'
        Create A New Symptom Entry
    END

Select Symptom Info Is Up To Date
    Try To Click Element    ${symptom_info_is_up_to_date}
    Click Element    ${aeq_questionnaire_next_button}

Symptom Questionnaire Summary Contains Text
    [Arguments]    ${text}
    Generic: Element Should Contain    xpath=(${questionnaires_summary_content})[last()]    ${text}

Send Symptom Questionnaire To Clinic
    [Documentation]    Send To Clinic button after answering symptom questionnaire as patient
    Try To Click Element    ${send_to_clinic_button}

Close Modal
    Try To Click Element    ${close_modal_button}

Questionnaire Has Been Sent Modal Is Displayed
    Wait Until Page Contains    Thank you for your answers!    timeout=8s
    Wait Until Page Contains Element    ${move_to_home_button}/div

Select All One-Sided Swelling Location
    [Documentation]    usually used when answering One-sided swelling of an arm
    Wait Until Element Is Visible    //*[@id='body-parts-front']
    @{front_pain_points}    Get WebElements    //*[@id='body-parts-front']/*
    @{back_pain_points}    Get WebElements    //*[@id='body-parts-back']/*
    FOR    ${pain_point}    IN    @{front_pain_points}
        Try To Click Element    ${pain_point}
    END
    FOR    ${pain_point}    IN    @{back_pain_points}
        Try To Click Element    ${pain_point}
    END

Add Multiple Same Level Rules
    [Documentation]    use specific symptom to trigger multiple rules
    [Arguments]    ${email}
    Login As Patient    ${email}
    Click Add Menu Button
    Add Symptom Entry
    Sleep    1
    IF    'native' in '${ENVIRONMENT}'
        app_symptom_form.Select A Symptom From List    Pain
    ELSE
        questionnaires.Select Symptom From List    Pain
    END
    Select Answer To Question    Where is the pain located?    Elsewhere in the body
    Try To Click Element    xpath=//*[@id='right-breast']
    Try To Click Element    xpath=//*[@id='left-breast']
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    Does the pain radiate into the neck, left shoulder or left upper arm?    No
    Select Answer To Question    In which situations does the pain occur?    None of the above
    Select Answer To Question    How would you describe the pain?    None of the above
    Select Answer To Question    How often do you feel this pain?    Pain is occasional
    Rate With Vertical Slider    rating=10
    Input How Many Days The Pain Is Most Severe    1
    Select Answer To Question    Have you tried pain medications?    No
    Click Next Button
    Save Symptom Report
    Emergency Priority Symptom Is Displayed    ${multiple_automation_rules_emergency_instructions}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}

Input How Many Days The Pain Is Most Severe
    [Arguments]    ${days}
    Wait Until Element Is Visible    ${days_pain_most_severe}
    Scroll Element Into View    ${days_pain_most_severe}
    Input Text    ${days_pain_most_severe}    ${days}

Tick Info Entered By Caregiver Checkbox
    [Arguments]    ${questionnaire_or_symptom_form}=single-symptom-form
    IF    'native' not in '${ENVIRONMENT}'
        Sleep    1
        Wait Until Element Is Enabled    ${information_by_caregiver_checkbox}
        Sleep    1
        Try To Click Element    ${information_by_caregiver_checkbox}
        Checkbox Should Be Selected    next-of-kin
    ELSE
        Click Element    ${information_by_caregiver_checkbox}
    END
    IF     '${questionnaire_or_symptom_form}'=='qol' or '${questionnaire_or_symptom_form}'=='single-symptom-form'
        Wait Until Element Is Visible    ${information_entered_by_caregiver_in_summary}
    ELSE IF   '${questionnaire_or_symptom_form}'=='aeq'
        IF    'native' not in '${ENVIRONMENT}'
            Wait Until Element Is Not Visible    ${information_entered_by_caregiver_in_summary}
        ELSE
            Log    Another "Information entered by caregiver" text is not visible on the summary, but the page always contains this element. "Wait Until Element Is Not Visible" doesn't work for Appium
        END
    END
    Sleep    1

Mark Symptomatic Days For Symptom Questionnaires
    [Documentation]    Only selects yesterday and today. Use symptom variable when patient has multiple symptoms
    [Arguments]    ${symptom}
    Sleep    1
    ${today_month_year}    Get Current Date    result_format=%B${SPACE}%Y
    ${date_today}    Get Current Date    result_format=%-d
    ${today}    Get Current Date
    ${yesterday_month_year}    Subtract Time From Date    ${today}    1 day    result_format=%B${SPACE}%Y
    ${yesterday_date}    Subtract Time From Date    ${today}    1 day    result_format=%-d
    ${yesterday}    Set Variable
    ...    //h1[text()='${symptom}']/../../../descendant::th[contains(text(),"${yesterday_month_year}")]/../../../descendant::div[text()="${yesterday_date}"]
    ${today}    Set Variable
    ...    //h1[text()='${symptom}']/../../../descendant::th[contains(text(),"${today_month_year}")]/../../../descendant::div[text()="${date_today}"]
    ${status_yesterday}    Run Keyword And Return Status    Element Should Be Visible    ${yesterday}\[2]/..
    ${yesterday}    Set Variable If    ${status_yesterday}    ${yesterday}\[2]/..    ${yesterday}\[1]/..
    ${status_today}    Run Keyword And Return Status    Element Should Be Visible    ${today}\[2]/..
    ${today}    Set Variable If    ${status_today}    ${today}\[2]/..    ${today}\[1]/..
    Set Test Variable    ${yesterday}
    Try To Click Element    ${yesterday}
    Try To Click Element    ${today}

Mark Symptomatic Days For Symptom Report
    [Documentation]    Only selects past 2 days for now
    [Arguments]    ${including_today}=no
    ${today}    Get Current Date
    ${yesterday_month_year}    Subtract Time From Date    ${today}    1 day    result_format=%B${SPACE}%Y
    ${yesterday_date}    Subtract Time From Date    ${today}    1 day    result_format=%-d
    ${2days_ago_month_year}    Subtract Time From Date    ${today}    2 days    result_format=%B${SPACE}%Y
    ${2days_ago_date}    Subtract Time From Date    ${today}    2 days    result_format=%-d
    ${todays_month_year}    Get Current Date    result_format=%B${SPACE}%Y
    ${todays_date}    Get Current Date    result_format=%-d
    ${todays_date}    Set Variable
    ...    //*[contains(text(),"${todays_month_year}")]/../../../descendant::*[not(contains(@class, 'disabled'))]/div[text()="${todays_date}"]
    ${yesterday}    Set Variable
    ...    //*[contains(text(),"${yesterday_month_year}")]/../../../descendant::*[not(contains(@class, 'disabled'))]/div[text()="${yesterday_date}"]
    ${2days_ago}    Set Variable
    ...    //*[contains(text(),"${2days_ago_month_year}")]/../../../descendant::*[not(contains(@class, 'disabled'))]/div[text()="${2days_ago_date}"]
    Set Test Variable    ${2days_ago}
    Set Test Variable    ${todays_date}
    IF    '${including_today}'=='yes'    Try To Click Element    xpath=${todays_date}
    Try To Click Element    xpath=${yesterday}
    Try To Click Element    xpath=${2days_ago}

Patient Feedback Dialog Is Displayed
    Wait Until Page Contains    ${thank_you_header_text}
    Wait Until Page Contains    ${satisfaction_level_question}
    Page Should Contain Element    ${patient_feedback_ok_to_contact_checkbox}

Patient Answers Feedback
    [Documentation]    rating can be veryunhappy, unhappy, content, happy, veryhappy
    [Arguments]    ${rating}    ${message}    ${ok_to_contact}=no
    ${element}    Format String    ${patient_feedback_rating}    ${rating}
    Wait Until Page Contains Element    ${element}
    Try To Click Element    ${element}
    Input Text    ${feedback-message-field}    ${message}
    IF    '${ok_to_contact}'=='yes'
        Try To Click Element    ${patient_feedback_ok_to_contact_checkbox}
    END
    Wait Until Keyword Succeeds    9    1    Try To Click Element    ${save-button-element}
    Wait Until Element Is Visible    ${toaster-message-element}

Patient Closes Feedback Dialog
    [Documentation]    Feedback functionality is removed with NOONA-21850; keyword is obsoleted and should be removed once the usecases are obsoleted.
    Wait Until Page Contains Element   ${patient_feedback_dialog_close_button}
    Scroll Element Into View           ${patient_feedback_dialog_close_button}
    Try To Click Element    ${patient_feedback_dialog_close_button}

Symptom Is Selected
    [Documentation]    Verifies that the symptom is selected or Yes is selected for symptoms by default
    [Arguments]    ${yes_locator}
    ${element}    Get WebElement    ${yes_locator}
    Wait Until Element Is Visible   ${yes_locator}/following-sibling::label
    Checkbox Should Be Selected    ${element}

Tick Specific Checkbox Based On Question
    [Arguments]    ${question}    @{options}
    Sleep    1
    FOR    ${option}    IN    @{options}
        Wait Until Page Contains    ${option}
        Try To Click Element
        ...    //h4/span[contains(text(),"${question}")]/../../../../descendant::label[contains(text(),"${option}")]
    END

Complete Stomach and bowel symptoms Symptom Form
    [Arguments]    ${pendo}=no
    IF    '${pendo}'=='yes'
        ${status}    Run Keyword And Return Status    Wait Until Page Contains    Welcome to your questionnaire!    timeout=3
        IF    ${status}    Close Pendo Guide
    END
    Select Yes For Symptoms    Stomach and bowel symptoms
    Changes In Gen State Of Health Is Displayed
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question
    ...    Has the need to empty your bowels decreased or increased?
    ...    Decreased from normal (constipation)
    Select Answer To Question    How would you rate the severity of your constipation?    Mild: occasional and varying
    Select Answer To Question    Have you used any medication to alleviate your abdominal symptoms?    No
    Click Next Button
    Send Symptom Questionnaire To Clinic
    IF    '${pendo}'=='yes'
        Wait Until Page Contains    Was it easy to answer the questionnaire?
        Close Pendo Guide
    END
    Click View Your Diary

Add Difficulty Eating Symptom To Diary
    [Arguments]    ${reported_by}=patient
    Add A Symptom    Difficulty eating
    Sleep    1s
    Select Answer To Question    Have you experienced any of the following?    Mouth symptoms
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question
    ...    How would you rate the intensity of your mouth symptoms?
    ...    Mild: does not affect eating or cause pain
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Click Next Button
    IF    '${reported_by}' == 'caregiver'
        Information Entered By Caregiver Checkbox Is Visible
        Tick Info Entered By Caregiver Checkbox
        Questionnaire Summary Is Displayed
    END
    Save Symptom Report
    Wait Until Page Contains    Symptom entry saved in your diary
    Click View Your Diary

Add Nausea Or Vomiting Symptom To Diary
    Patient Adds Symptom From Diary    Nausea or vomiting
    Sleep    1s
    Select Answer To Question    Have you had...?    Nausea
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the intensity of your nausea?
    ...    Moderate: interferes with eating and drinking, no significant effect on weight or fluid balance
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Click Next Button
    Save Symptom Report
    Click View Your Diary

Add Respiratory Symptoms To Diary
    Patient Adds Symptom From Diary    Respiratory symptoms
    Sleep    1s
    Select Answer To Question
    ...    Are your respiratory symptoms associated with any of the following?
    ...    Shortness of breath
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question
    ...    How would you rate the intensity of your shortness of breath?
    ...    Mild: occurs during moderate exercise
    Select Answer To Question    Is shortness of breath a new symptom for you?    No
    Select Answer To Question    Have you used any medication for your respiratory symptoms?    No
    Click Next Button
    Save Symptom Report
    Click View Your Diary

Add Changes In Mood Or Emotions Symptoms To Diary
    Patient Adds Symptom From Diary    Changes in mood or emotions
    Sleep    1s
    Select Answer To Question    Have you experienced any of the following?    Sadness
    Select Answer To Question    When did you have this symptom?    Today
    Rate With Vertical Slider    4
    Select Answer To Question    Have you already received help with your problem?    No
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Click Next Button
    Save Symptom Report
    Click View Your Diary

Add Hair Changes Symptoms To Diary
    Patient Adds Symptom From Diary    Hair changes
    Sleep    1s
    Select Answer To Question    Are your hair changes associated with one of the following?    Hair loss
    Select Answer To Question
    ...    How much hair have you lost?
    ...    Less than half: hair loss not necessarily noticeable from a distance
    Click Next Button
    Save Symptom Report
    Click View Your Diary

Get Previously Report Symptoms
    @{current_symptoms}    Create List
    Wait Until Element Is Visible    xpath=(${symptoms_in_list})[last()]
    ${count}    Generic: Get Element Count    ${symptoms_in_list}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    xpath=(${symptoms_in_list})[${INDEX}]
        Append To List    ${current_symptoms}    ${text}
    END
    Set Test Variable    ${current_symptoms}

All QOL Questions Have Required Fields Indicated
    Wait Until Element Is Visible    xpath=(${questionnaires_with_radio_button})[last()]
    ${questionnaire_count}    Generic: Get Element Count    ${questionnaires_with_radio_button}
    ${required_fields_error_message_count}    Generic: Get Element Count    ${required_fields_error_message}
    ${required_fields_red_box_count}    Generic: Get Element Count    ${required_fields_red_box}
    Should Be Equal
    ...    ${questionnaire_count}
    ...    ${required_fields_error_message_count}
    ...    ${required_fields_red_box_count}
    Text Should Be In The Page    ${answer_required_questions_error}

Check Required Field Indicator Per Question
    Wait Until Element Is Visible    xpath=(${questionnaires_with_radio_button})[last()]
    ${questionnaire_count}    Generic: Get Element Count    ${questionnaires_with_radio_button}
    IF    ${questionnaire_count}==0
        Fail    Error message should be displayed but it is not
    END
    FOR    ${INDEX}    IN RANGE    1    ${questionnaire_count}+1
        Try To Click Element    xpath=(${qol_radio_buttons})[${INDEX}]
        ${current_error_count}    Evaluate    ${questionnaire_count}-${INDEX}
        ${required_fields_error_message_count}    Generic: Get Element Count    ${required_fields_error_message}
        ${required_fields_red_box_count}    Generic: Get Element Count    ${required_fields_red_box}
        ${current_error_count}    Convert To Integer    ${current_error_count}
        ${required_fields_error_message_count}    Convert To Integer    ${required_fields_error_message_count}
        ${required_fields_red_box_count}    Convert To Integer    ${required_fields_red_box_count}
        Should Be Equal
        ...    ${current_error_count}
        ...    ${required_fields_error_message_count}
        ...    ${required_fields_red_box_count}
        IF    ${INDEX}==15
            Text Should Not Be In The Page    ${answer_required_questions_error}
        ELSE
            Text Should Be In The Page    ${answer_required_questions_error}
        END
    END
    Page Should Not Contain Element    xpath=(${required_fields_error_message})[1]
    Page Should Not Contain Element    xpath=(${required_fields_red_box})[1]

Answer Symptom Form From Clicking Link
    Select Yes For Symptoms    Other symptom
    Changes In Gen State Of Health Is Displayed
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic

Answer Latest Symptom Questionnaire With Other Symptom
    [Documentation]    ${previous_symptom} is new_entry or up_to_date
    ...    ${severity} is Mild, Moderate, Severe    ${with_photo}=no
    [Arguments]    ${previous_symptom}    ${severity}    ${send}=yes    ${with_photo}=no
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Try To Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=${previous_symptom}
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    ${severity}
    IF    '${with_photo}'=='yes'
        Add Photo To Symptom    file_example_JPG_100kB.jpg
        Wait Until Element Is Visible    ${photo_description_textarea}
        Input Text    ${photo_description_textarea}    Other symptom with attached photo.
    END
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Try To Click Element    ${aeq_questionnaire_next_button}
    IF    '${send}'=='yes'
        Send Symptom Questionnaire To Clinic
        Wait Until Page Does Not Contain    ${send_to_clinic_button}
    END

Answer Latest Symptom Questionnaire With No Symptoms
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select No For All Symptoms
    Try To Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Try To Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic
    Wait Until Page Does Not Contain    ${send_to_clinic_button}

Ask About Stomach Eating And Bowel Symptom
    [Documentation]    Patient should have Abdominal Radiotherapy module. This should result to and urgent rule.
    Navigate To Clinic
    Select Ask About Symptom Option
    Add A Symptom    ${STOMACH_EATING_AND_BOWEL}
    Select Answer To Question    When did you have this symptom?    Today
    Try To Click Element    ${stomach_eating_bowel_nausea_symptom_checkbox}
    Select Answer To Question    How would you rate the intensity of your nausea?    Moderate: Interferes with eating and drinking, no significant effect on weight or fluid balance.
    Select Answer To Question    Do you get full quickly?    No
    Select Answer To Question    Have you been drinking less fluids than usual?    No
    Click Next Button
    Send Symptom To Clinic

Select No To All Problem List
    Try To Click Element    ${none_of_practical_problems_checkbox}
    Try To Click Element    ${none_of_family_problems_checkbox}
    Try To Click Element    ${none_of_emotional_problems_checkbox}
    Try To Click Element    ${spiritual_religious_concerns_no_option}
    Try To Click Element    ${none_of_physical_problems_checkbox}

Cancel Questionnaire To Discard Changes
    [Arguments]    ${discard_by_button}
    Try To Click Element    ${discard_by_button}

Discard Changes Modal Is Visible
    Wait Until Element Is Visible    ${patient_confirmation_modal}//*[contains(text(), "Discard changes?")]
    Wait Until Element Is Visible    ${patient_confirmation_modal}//*[contains(text(), "Your changes have not been saved. Would you like to leave the page and discard the changes?")]
    Wait Until Element Is Visible    ${patient_confirmation_modal}//*[contains(text(), "Continue editing")]
    Wait Until Element Is Visible    ${patient_confirmation_modal}//*[contains(text(), "Discard changes")]

Select To Continue Editing
    Try To Click Element    //*[@id="cancel-confirm"]//*[contains(text(), "Continue editing")]

Select To Discard Changes And Return To Diary Page
    Try To Click Element    //*[@id="ok-confirm"]//*[contains(text(), "Discard changes")]
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/diary-timeline
