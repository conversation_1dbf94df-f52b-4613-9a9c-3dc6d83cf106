*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}/resources/patient/more/more.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource


*** Variables ***
${menu_more_button}             //ds-button[contains(@icon, "icon-more")]
${instructions_link}            //*[@data-testid='more-link-instructions']
${inst_symptom_link}            //*[@class="help-modal"]//ul/li[1]
${inst_contact_link}            //*[@class="help-modal"]//ul/li[2]
${inst_ask_about_link}          //*[@class="help-modal"]//ul/li[3]
${inst_medical_rec_link}        //*[@class="help-modal"]//ul/li[4]
${inst_education_doc_link}      //*[@class="help-modal"]//ul/li[5]
${inst_lab_link}                //*[@class="help-modal"]//ul/li[6]
${inst_appointments_link}       //*[@class="help-modal"]//ul/li[7]
${inst_diary_link}              //*[@class="help-modal"]//ul/li[8]
${inst_profile_link}            //*[@class="help-modal"]//ul/li[9]
${inst_noona_application}       //*[@class="help-modal"]//ul/li[11]
${inst_legal_information}       //*[@class="help-modal"]//ul/li[15]
${inst_close_button}            //*[@id="instructions-modal"]//ds-button/button
${inst_x_button}                //div[@id='instructions-modal']//button[@aria-label='Close dialog button']
${instructions_modal}           //*[@id="instructions-modal"]/div[2]/div


*** Keywords ***
Select Instructions Menu
    Wait Until Page Contains Element    ${instructions_link}
    Try To Click Element    ${instructions_link}

Verify Instructions Content
    [Arguments]    ${instructions}
    ${instructions_content}    Get File    ${EXECDIR}${/}resources${/}patient${/}${instructions}.txt
    @{instructions_content_lines}    Split To Lines    ${instructions_content}
    Wait Until Element Is Visible    xpath=${instructions_modal}
    Scroll Element Into View    xpath=${instructions_modal}
    ${actual_instruction_text}    Get Text    xpath=${instructions_modal}
    FOR    ${line}    IN    @{instructions_content_lines}
        ${is_line_contained}    Run Keyword And Return Status
        ...    Should Contain
        ...    ${actual_instruction_text}
        ...    ${line}
        IF    '${is_line_contained}' == 'False'
            Fail    The line, "${line}" is not visible in the instructions content.
        END
    END

Switch Language And Verify Legal Information Content In ${language}
    [Documentation]    The keyword checks legal information in other languages as part of the instructions sections. See instruction_section locator is used to see
    ...    if legal information content texts are inlcuded in this element
    ${language_prefix}    Set Variable If
    ...    '${language}'=='English'    EN
    ...    '${language}'=='Suomi'    FI
    ...    '${language}'=='Svenska'    SV
    ...    '${language}'=='Norsk'    NO
    ...    '${language}'=='Deutsch'    DE
    ...    '${language}'=='Espanol'    ES
    ...    '${language}'=='Türkçe'    TR
    ...    '${language}'=='Français'    FR
    ...    '${language}'=='Português'    PT
    ...    '${language}'=='Nederlands'    NL
    ...    '${language}'=='Italiano'    IT
    ...    '${language}'=='Polski'    PL
    ${legal_information_content}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}legal_information${/}legal_information_${language_prefix}.txt
    Switch Language In Clinic Preferences    ${language}
    Try To Click More Button
    Select Instructions Menu
    Patient Selects Legal Information
    Wait Until Element Is Visible    xpath=${instructions_modal}
    Scroll Element Into View    xpath=${instructions_modal}
    ${actual_instruction_text}    Get Text    xpath=${instructions_modal}
    @{legal_information_lines}    Split To Lines    ${legal_information_content}
    FOR    ${line}    IN    @{legal_information_lines}
        ${is_line_contained}    Run Keyword And Return Status
        ...    Should Contain
        ...    ${actual_instruction_text}
        ...    ${line}
        IF    '${is_line_contained}' == 'False'
            Fail    The line, "${line}" is not visible in the legal information content.
        END
    END
    Patient Closes Instructions With    Close

Patient Selects Topics
    Try To Click Element    ${inst_symptom_link}
    Try To Click Element    ${inst_contact_link}
    Try To Click Element    ${inst_ask_about_link}
    Try To Click Element    ${inst_medical_rec_link}
    Try To Click Element    ${inst_education_doc_link}
    Try To Click Element    ${inst_lab_link}
    Try To Click Element    ${inst_appointments_link}
    Try To Click Element    ${inst_diary_link}
    Try To Click Element    ${inst_profile_link}
    Try To Click Element    ${inst_noona_application}
    Patient Selects Legal Information

Patient Selects Legal Information
    Try To Click Element    ${inst_legal_information}

Patient Closes Instructions With
    [Arguments]    ${method}
    Sleep    1s
    IF    '${method}'=='Close'    Try To Click Element    ${inst_close_button}
    IF    '${method}'=='X'    Try To Click Element    ${inst_x_button}
    Wait Until Page Does Not Contain    ${instructions_modal}
