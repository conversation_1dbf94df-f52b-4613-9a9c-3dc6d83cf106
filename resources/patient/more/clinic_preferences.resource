*** Settings ***
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources/patient/patient_screens.resource


*** Variables ***
${language_selection_section_container}     //div[@class="form-group"]
${language_selection_section_label}         //label[@class="locale"]
${language_and_notification_label}          //input[@id='{}']/following-sibling::label
${language_and_notification_label}          //label[@for='{}']/preceding-sibling::input
${save_clinic_preferences_button}           //*[@id="save-profile"]/button
${language_radio_buttons}                   localeId
${delegate_users_section}                   //*[@id='delegate-users']
${delegate_user_add_button}                 //*[@id="delegate-add-user-link"]
${delegate_user_fname_textbox}              //*[@id='delegate-add-user-modal']/descendant::input[@id='firstName']
${delegate_user_lname_textbox}              //*[@id='delegate-add-user-modal']/descendant::input[@id='lastName']
${delegate_user_email_textbox}              //*[@id='delegate-add-user-modal']/descendant::input[@id='email']
${add_delegate_add_user_button}             //*[@id="delegate-add-user-modal-save"]/button
${invitation_sent_banner_message}           Invitation sent to the delegate user
${activate_your_noona_account_text}         Activate your Noona account
${activate_delegate_next_button}            //button[@type='submit']
${delegate_new_password_textbox}            newPassword
${delegate_account_is_ready_text}           Congratulations, your account is now ready!
${medical_records_intro_as_delegate}        library-intro-medical-records
${delegate_logout_button}                   //*[@data-testid="library-delegate-logout-button"]
${library_intro_button}                     library-intro-medical-records
${patient_page_nav}                         main-navigation
${delegate_privacy_policy_link}             //*[@data-testid="library-privacy-policy-link"]
${reset_password_option}                    (//li[@id='actions-dropdown-option-1'])[last()]/button
${delegate_user_action_dialog}              //div[@class='modal-dialog noona-dialog']
${renew_password_button}                    //*[@id="ok-confirm"]/button
${cancel_renew_password_button}             //*[@id="cancel-confirm"]/button
${passsword_link_sent_banner}               Password reset link was sent
${delete_user_option}                       (//li[@id='actions-dropdown-option-0'])[last()]/button
${delete_user_button}                       //*[@id="ok-confirm"]/button
${delete_delegate_banner}                   Delegate user deleted successfully
${disconnect_from_clinic_button}            xpath=//*[@id="disconnect-from-clinic-link"]/ds-button/button/div/div/div/div
${delete_account_confirm_button}            xpath=//*[@id="ok-confirm"]/button/div/div
${delete_account_next_button}               xpath=//button[@type='submit']
${password_input_field}                     //*[@id="password"]
${save_profile_button}                      //*[@id="save-profile"]/button
${section_title}                            //h1[@class='section-title']
${clinic_preference_password_field}         //*[@id='password']
${clinic_preferences_header_spanish}        Preferencias de la clínica
${clinic_preferences_header_FIN}            Klinikka-asetukset
${language_selection_section_label_EN}      Choose language for this clinic*
${language_selection_section_label_FIN}     Valitse kieli tälle klinikalle*
${clinic_preferences_header}                //*[@id="clinic-options"]//h1
${clinic_preferences_header_text}           Clinic preferences
${clinic_preferences_clinic_name}           //div[@class="clinic-name"]
${preferences_saved_banner}                 Preferences saved
${selected_language_ENG}                    //*[@id="locale-en_GB"]


*** Keywords ***
### Update Language ###
Select Patient Language
    [Arguments]    ${language}
    ${lang}    Set Variable If
    ...    '${language}'=='English'    locale-en_GB
    ...    '${language}'=='Suomi'    locale-fi_FI
    ...    '${language}'=='Svenska'    locale-sv_FI
    ...    '${language}'=='Norsk'    locale-no_NO
    ...    '${language}'=='Deutsch'    locale-de_DE
    ...    '${language}'=='Espanol'    locale-es_ES
    ...    '${language}'=='Türkçe'    locale-tr_TR
    ...    '${language}'=='Português'   locale-pt_PT
    ...    '${language}'=='Nederlands'   locale-nl_NL
    ...    '${language}'=='Italiano'   locale-it_IT
    ...    '${language}'=='Français'    locale-fr_FR
    ...    '${language}'=='Polski'    locale-pl_PL
    Set Test Variable     ${lang}
    ${language_locale_value}    Remove String    ${lang}    locale-
    Set Test Variable    ${language_locale_value}
    ${element}    Format String    ${language_and_notification_label}    ${lang}
    Set Test Variable    ${selected_language}    ${element}
    Wait Until Element Is Visible    ${element}
    Scroll Element Into View    ${element}
    Try To Click Element    ${element}

Convert Language Id To Labels
    [Arguments]    ${language}
    ${converted_language_value}    Set Variable If
    ...    '${language}'=='en_GB'    English
    ...    '${language}'=='fi_FI'    Finnish
    ...    '${language}'=='sv_FI'    Swedish
    ...    '${language}'=='no_NO'    Norwegian
    ...    '${language}'=='de_DE'    German
    ...    '${language}'=='es_ES'    Spanish
    ...    '${language}'=='tr_TR'    Turkish
    Set Global Variable    ${converted_language_value}

Correct Language Is Selected
    IF    'native' in '${ENVIRONMENT}'
        Radio Button Should Be Set In Native
    ELSE
        Radio Button Should Be Set To    ${language_radio_buttons}    ${language_locale_value}
    END

Radio Button Should Be Set In Native
    ${element}    Format String    ${language_and_notification_label}    ${lang}
    Wait Until Element Is Visible    ${element}
    ${attr}    Get Element Attribute    ${selected_language}/../input    class
    Should Contain    ${attr}    selectedLocale
    IF    '${PLATFORM_NAME}'=='android'
        IF    'test' in '${ENVIRONMENT}'
            Switch To Context    ${webview_noona_app_test}
        ELSE
            Switch To Context    ${webview_noona_app_staging}
        END
    ELSE
        ${contexts}    Get Contexts
        Switch To Context    ${contexts}[1]
    END

Get Original Field Values Clinic Preferences
    ${original_language}    Execute Javascript
    ...    return document.querySelector('input[name="localeId"]:checked').value;
    Set Global Variable    ${original_language}
    Convert Language Id To Labels    ${original_language}
    Set Global Variable    ${original_language}

Get Current Values Clinic Preferences
    ${current_language}    Execute Javascript    return document.querySelector('input[name="localeId"]:checked').value;
    Set Global Variable    ${current_language}
    Convert Language Id To Labels    ${current_language}

Compare Account Preferences Values
    Should Be Equal    ${original_email}    ${current_email}
    Should Be Equal    ${original_phone_number}    ${current_phone_number}

Reset Back To Original Values
    IF    '${current_email}'!='${original_email}'
        Update Email Address    ${original_email}
    END
    IF    '${current_phone_number}'!='${original_phone_number}'
        Update Phone Number    ${original_email}
    END
    IF    '${current_language}'!='English'    Select Patient Language    English
    Click Save Profile Button
    ${password_modal}    Run Keyword And Return Status    Wait Until Element Is Enabled    password
    IF    ${password_modal}
        Input Password To Update Clinic Preferences    And    Wait Until Page Contains    Profile settings saved
    END

### Saving Clinic Preferences ###

Input Password To Update Clinic Preferences
    Wait Until Element Is Visible    ${clinic_preference_password_field}
    IF    'native' not in '${ENVIRONMENT}'
        Set Focus To Element    ${input_password_modal}
    END
    Try To Input Text    ${clinic_preference_password_field}    ${DEFAULT_PASSWORD}
    Try To Click Element    ${input_password_next_button}

Click Clinic Preferences Save Button
    Wait Until Element Is Visible    ${save_clinic_preferences_button}
    Try To Click Element    ${save_clinic_preferences_button}

### Delegate Users ###

Delegate Users Section Is Displayed In My Profile
    Wait Until Element Is Visible    ${delegate_users_section}
    Element Should Be Visible    ${delegate_user_add_button}
    Element Should Be Enabled    ${delegate_user_add_button}
    Generic: Element Should Contain    ${delegate_users_section}
    ...    You can give your family member, or another person you trust, access to the content of your Library.

Add Delegate User
    [Arguments]    ${fname}    ${lname}    ${email}
    Click Add Users
    Wait Until Keyword Succeeds    20s    1s    Input Delegate User Details    ${fname}    ${lname}    ${email}
    Click Add Delegate User Confirm Button
    Delegate User Is Added In Delegate User Section    ${fname}    ${lname}    ${email}

Click Add Delegate User Confirm Button
    Try To Click Element    ${add_delegate_add_user_button}
    Wait Until Page Contains    ${invitation_sent_banner_message}

Input Delegate User Details
    [Arguments]    ${fname}    ${lname}    ${email}
    Wait Until Element Is Visible    ${delegate_user_fname_textbox}
    Try To Input Text    ${delegate_user_fname_textbox}    ${fname}
    Try To Input Text    ${delegate_user_lname_textbox}    ${lname}
    Try To Input Text    ${delegate_user_email_textbox}    ${email}

Delegate User Is Added In Delegate User Section
    [Arguments]    ${fname}    ${lname}    ${email}
    Wait Until Page Contains Element    ${delegate_users_section}
    Scroll Element Into View    ${delegate_users_section}
    ${users}    Get Text    ${delegate_users_section}
    Should Contain    ${users}    ${fname}
    Should Contain    ${users}    ${lname}
    Should Contain    ${users}    ${email}

Click Add Users
    Wait Until Element Is Visible    ${delegate_user_add_button}
    Scroll Element Into View    ${delegate_user_add_button}
    Try To Click Element    ${delegate_user_add_button}/ds-button/button

Verify Delegate User Can Login
    [Arguments]    ${email}
    # add a little more time to login after activation because the account is not registered right away in some cases
    Sleep
    ...    1
    Login As Patient    ${email}
    Wait Until Element Is Visible    ${library_intro_button}
    ${status}    Run Keyword And Return Status    Element Should Not Be Visible    ${patient_page_nav}
    IF    ${status}==${False}
        Fail    Patient Navigation Bar Is Available For Delegate User
    END
    Element Should Be Enabled    ${delegate_logout_button}
    Element Should Be Enabled    ${delegate_privacy_policy_link}

Click Delegate User Options Button
    [Arguments]    ${email}
    Wait Until Element Is Visible    ${delegate_users_section}
    Scroll Element Into View    ${delegate_users_section}
    Wait Until Page Contains    ${email}
    Try To Click Element    //span[text()="${email}"]/../..//button

Reset Delegate User Password
    [Arguments]    ${email}
    Click Delegate User Options Button    ${email}
    Wait Until Element Is Visible    xpath=${reset_password_option}
    Scroll Element Into View    xpath=${reset_password_option}
    Try To Click Element    xpath=${reset_password_option}
    Wait Until Element Is Visible    ${delegate_user_action_dialog}
    Try To Click Element    ${renew_password_button}
    Wait Until Page Contains    ${passsword_link_sent_banner}
    Try To Click Banner Message

Pressing Cancel Or X Closes The Modal
    [Arguments]    ${email}
    Wait Until Page Does Not Contain    ${passsword_link_sent_banner}
    Click Delegate User Options Button    ${email}
    Wait Until Element Is Visible    xpath=${reset_password_option}
    Scroll Element Into View    xpath=${reset_password_option}
    Try To Click Element    xpath=${reset_password_option}
    Wait Until Element Is Visible    ${delegate_user_action_dialog}
    Try To Click Element    ${cancel_renew_password_button}
    Wait Until Page Does Not Contain     ${cancel_renew_password_button}

Delete Delegate User
    [Arguments]    ${email}
    Click Delegate User Options Button    ${email}
    Wait Until Element Is Visible    xpath=${delete_user_option}
    Scroll Element Into View    xpath=${delete_user_option}
    Try To Click Element    xpath=${delete_user_option}
    Wait Until Element Is Visible    ${delegate_user_action_dialog}
    Try To Click Element    ${delete_user_button}
    Wait Until Page Contains    ${delete_delegate_banner}

Delegate User Is Deleted Successfully
    [Arguments]    ${email}
    Text Should Not Be In The Page    ${email}
    Go To Diary
    Prepare Next Login
    Input Login Credentials And Login    ${email}        ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Activate Delegate User Account
    [Arguments]    ${link}    ${email}
    Go To    ${link}
    Accept All Cookies If Visible
    Wait Until Element Is Visible    (${activate_your_account_button})[1]
    ${count}    Get Element Count    ${activate_your_account_button}
    ${expected_count}    Convert To Integer    2
    Should Be Equal    ${count}    ${expected_count}
    Try To Click Element    (${activate_your_account_button})[1]
    Wait Until Page Contains    ${activate_your_noona_account_text_2}
    Wait Until Element Is Visible    ${activate_next_button}
    Try To Click Element    ${activate_next_button}
    Wait Until Page Contains Element    ${new_password_textbox}
    Wait Until Element Is Enabled    ${new_password_textbox}
    Input Text    ${new_password_textbox}    ${DEFAULT_PASSWORD}
    Try To Click Element        ${activate_next_button}
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element        ${activation_login_button}

Delegate Updates Password From Reset Password Link
    [Arguments]    ${link}    ${email}
    Go To    ${link}
    Accept All Cookies If Visible
    Wait Until Page Contains Element    ${new_password_textbox}
    Wait Until Element Is Enabled    ${new_password_textbox}
    Input Text    ${new_password_textbox}    ${DEFAULT_PASSWORD}
    Try To Click Element        ${activate_next_button}
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element        ${activation_login_button}

### Disconnect from clinic and delete data ###

Disconnect From Clinic
    Go To Clinic Preferences
    Try To Click Element    ${disconnect_from_clinic_button}
    Try To Click Element    ${delete_account_confirm_button}
    Try To Input Text       ${password_input_field}    ${DEFAULT_PASSWORD}
    Wait Until Page Contains Element    ${delete_account_next_button}
    Try To Click Element    ${delete_account_next_button}
    Wait Until Page Does Not Contain Element     ${delete_account_next_button}
    Run Keyword And Ignore Error
    ...    Wait Until Page Contains
    ...    Your account deletion process has been started.
    ...    timeout=5s
    # Ignores error for now so test case can proceed verification. Banner is displayed manually but not when running in gitlab.

Save Clinic Preferences
    Try To Click Element    ${save_clinic_preferences_button}
    Wait Until Page Contains    ${preferences_saved_banner}
    Wait Until Page Does Not Contain    ${preferences_saved_banner}

Verify Which Language Is Selected In Clinic Preferences And Change To Expected Language
    [Arguments]    ${language}
    ${selected_language_is_ENG}    Run Keyword and Return status    Element Attribute Value Should Be    ${selected_language_ENG}    class    selectedLocale
    IF    ${selected_language_is_ENG}
        Select Patient Language    ${language}
        Click Clinic Preferences Save Button
    END

Switch Language In Clinic Preferences
    [Arguments]    ${language}
    Go To Clinic Preferences
    Select Patient Language    ${language}
    Click Clinic Preferences Save Button
    Try To Click Banner Message

Delegate Has A Newly Reset Password And Is Able To Login
    [Arguments]     ${email}
    IF    'native' not in '${ENVIRONMENT}'
        ${link}    Convert To String      ${link}
        Delegate Updates Password From Reset Password Link     ${link}     ${email}
        Verify Delegate User Can Login    ${email}
    END