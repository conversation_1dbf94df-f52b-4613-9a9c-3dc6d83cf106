*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource


*** Variables ***
${default_landing_page_login_button}            //*[@id='landing-page-login-button']/button
${menus_nav_panel}                              //nav[@class='panel']
${more_menus_button}                            //ds-button[contains(@icon, "icon-more")]/button
${patient_logout_button}                        //*[@data-testid='more-link-logout']
${problems_logging_in}                          //*[@id='email-and-password-password-reset-link']//div[@class='ds-button__label']
${problems_logging_in_btn_color}                email-and-password-password-reset-link    # specifically for custom branding
${problems_logging_in_oidc}                     //div[@class='reset-password']//a
${problems_logging_in_modal_title}              //*[@id='dialog-title']
${problems_logging_in_modal_instructions}       //div[contains(concat(' ', normalize-space(@class), ' '), ' reset-password-form ')]/descendant::p[starts-with(@class, 'description')]
${problems_logging_in_email_textfield}          //*[@id='password-reset-email']
${problems_logging_in_email_label}              //*[@for='password-reset-email']
${problems_logging_in_modal_cancel}             //*[@id='password-reset-cancel']/button
${problems_logging_in_modal_submit}             //*[@id='password-reset-submit']
${locale}                                       //*[@id='locale']/div
${incorrect_password_text}                      Incorrect username or password
${wrong_password}                               qlktkle-2
${password_reset_modal_close_button}            //div[text()="Close"]
${show_password_button}                         show-password-button
${landing_page_background}                      //*[@class='landing-bg']
${landing_page_button_color}                    landing-page-login-button    # specifically for custom branding test
${oidc_problems_logging_in}                     //a[contains(text(),'Problems logging in?')]
${dont_have_account_link}                       open-information-overlay    # specifically for custom branding test
${privacy_statement_link}                       //ds-button[@aria-label='Open terms and conditions dialog']    # specifically for custom branding test
${about_link_login_page}                        //ds-button[@aria-label='About']    # specifically for custom branding test
${disabled_clinic_back_to_login_button}         //*[@id='clinic-disable-logout']/button
${disabled_clinic_login_message_1}              Your clinic has discontinued the use of Noona.
${disabled_clinic_login_message_2}              You can contact your clinic by phone at
${landing_page_connect_to_clinic_text}          Log in to Noona
${patient_consent}                              //*[@class='login-header'][contains(text(),'Terms of use and privacy statement')]
${approve_terms_consent_next}                   //ds-button/button/div/div[text()="Next"]/..
${landing_page_title_header}                    //h1[@class='login-title']  #Connect to your clinic title element
${verification_code_login_header}               //div[@class='login-form']/legend[text()= 'Verification code']
${privacy_notice_button}                        //nh-multiple-consent/div[2]/ds-action-button[2]/ds-button/button/div/div
${terms_of_use_button}                          //nh-multiple-consent/div[2]/ds-action-button[1]/ds-button/button/div/div
${privacy_text_header}                          Noona Privacy Statement
${privacy_statement_button}                     //div[@class='consent-links']//div[text()=' Privacy statement ']
${terms_of_use_link}                            //div[@class='consent-links']//div[text()=' Terms of use ']
${instructions_to_login_header}                 Check your email
${instructions_to_login_sent_text1}             Please check your email for password reset instructions, and try logging in after resetting your password.
${instructions_to_login_sent_text2}             If you can't find it in your inbox, please also check your spam and trash folders.
${instructions_to_login_sent_text3}             If the problem persists, please contact your clinic for assistance.
${keep_me_logged_in_checkbox}                   //*[@id='rememberMe']
${keep_me_logged_in_label_element}              //*[@id='rememberMe']/..
${login_page_privacy_instruction}               //*[@id='privacy-instruction']
${keep_me_logged_in_instruction}                Only select this on a personal device





*** Keywords ***
Verify Patient Login Is Successful
    Wait Until Page Contains    Diary

Login As Patient
    [Arguments]    ${email}=${CLINIC_PATIENT}[email]    ${remember_login}=Yes
    IF    'native' in '${ENVIRONMENT}'
        Login To Native App In Browserstack    ${email}
    ELSE
        Login To Noona    patient    ${email}
    END

Login As Delegate
    [Arguments]    ${email}=${CLINIC_PATIENT}[email]    ${remember_login}=Yes
    IF    'native' in '${ENVIRONMENT}'
        Login To Native App In Browserstack As Delegate User    ${email}
    ELSE
        Login To Noona    patient    ${email}
    END

Login As Patient With 2FA
    [Arguments]    ${email}=${CLINIC_PATIENT}[email]    ${remember_login}=Yes
    Delete All Messages In Server    ${sms_server_id}    ${sms_api_key}    # mailosaur server id = tiazhwes
    Login To Noona    patient    ${email}
    Input Patient Verification Code

Logout As Patient
    IF    'native' not in '${ENVIRONMENT}'
        ${nav}    Run Keyword And Return Status    Element Should Be Visible    ${menus_nav_panel}
        IF    ${nav}==${False}    Try To Click Element    ${more_menus_button}
        Wait Until Element Is Enabled    ${patient_logout_button}
        Try To Click Element    ${patient_logout_button}
        Patient Is Logged Out
    ELSE
        Logout From Patient App
    END

Logout From Patient App
    Try To Click Element    ${more_menus_button}
    Try To Click Native App Element    xpath=${patient_logout_button}
    Try To Click Native App Element    xpath=${app_logout_modal_logout_button}

Logout As Delegate User
    Try To Click Element    ${delegate_logout_button}
    Patient Is Logged Out

Patient Is Logged Out
    Wait Until Page Contains Element    ${landing_page_title_header}
    Location Should Contain    /patient/#/sign-in

Login As Random Patient
    Random Patient From The List
    Login As Patient    ${patient_email}

Patient Clicks Problems Logging In Link
    [Arguments]    ${language}=English
    ${problems_link_element}    Set Variable    ${problems_logging_in_oidc}
    Wait Until Element Is Visible    ${problems_link_element}
    Wait Until Page Contains    ${current_lang}[0]    timeout=5s
    ${text}    Get Text    ${problems_link_element}
    ${expected_text}    Convert To Uppercase    ${current_lang}[0]
    Should Be Equal    ${text}    ${expected_text}
    Wait Until Keyword Succeeds    9s    1s    Scroll Element Into View    ${problems_link_element}
    Try To Click Element    ${problems_link_element}

Patient Requests Password Reset
    [Arguments]    ${patient_email}    ${clinic}=Noona
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    Try To Click Element    ${problems_logging_in_oidc}
    Wait Until Element Is Visible    ${problems_logging_in_email_textfield}
    Try To Input Text    ${problems_logging_in_email_textfield}    ${patient_email}
    Try To Click Element    ${problems_logging_in_modal_submit}
    Check Password Reset Confirmation Modal    ${patient_email}
    Try To Click Element    ${password_reset_modal_close_button}

Set Problems Logging In Language Variables
    [Arguments]    ${language}
    @{current_lang}    Create List
    IF    '${language}'=='English'
        Append To List
        ...    ${current_lang}
        ...    Problems logging in?
        ...    Enter the email address associated with your account, and we'll email you a link to reset your password.
        ...    If you can't remember the email address, you can contact your clinic for help.
        ...    Email address
        ...    Close
        ...    Send reset link
    ELSE IF    '${language}'=='Suomi'
        Append To List
        ...    ${current_lang}
        ...    Ongelmia sisäänkirjautumisessa?
        ...    Anna tiliisi yhdistetty sähköpostiosoite, niin saat sähköpostitse linkin salasanan palauttamista varten.
        ...    Jos et muista sähköpostiosoitetta, ota yhteyttä klinikkaasi.
        ...    Sähköposti
        ...    Sulje
        ...    Lähetä palautuslinkki
    ELSE IF    '${language}'=='Svenska'
        Append To List
        ...    ${current_lang}
        ...    Problem med inloggningen?
        ...    Ange den e-postadress som är kopplad till ditt konto så skickar vi en länk till dig för att återställa ditt lösenord.
        ...    Om du inte kommer ihåg e-postadressen kan du kontakta din klinik för att få hjälp.
        ...    E-postadress
        ...    Stäng
        ...    Skicka återställningslänken
    ELSE IF    '${language}'=='Norsk'
        Append To List
        ...    ${current_lang}
        ...    Problemer med pålogging?
        ...    Skriv inn e-postadressen som er knyttet til kontoen din, så sender vi deg en lenke på e-post for å tilbakestille passordet ditt.
        ...    Hvis du ikke husker e-postadressen, kan du kontakte sykehuset ditt din for å få hjelp.
        ...    E-postadresse
        ...    Lukk
        ...    Send lenke for tilbakestilling
    ELSE IF    '${language}'=='Deutsch'
        Append To List
        ...    ${current_lang}
        ...    Probleme mit der Anmeldung?
        ...    Geben Sie die mit Ihrem Konto verknüpfte E-Mail-Adresse ein. Wir senden Ihnen dann einen Link zum Zurücksetzen Ihres Passworts zu.
        ...    Wenn Sie sich nicht mehr an die E-Mail-Adresse erinnern können, wenden Sie sich zwecks Hilfe an Ihre Klinik.
        ...    E-Mail-Adresse
        ...    Schließen
        ...    Link zum Zurücksetzen senden
    ELSE IF    '${language}'=='Français'
        Append To List
        ...    ${current_lang}
        ...    Problèmes de connexion ?
        ...    Saisissez l'adresse e-mail associée à votre compte et nous vous enverrons un lien pour réinitialiser votre mot de passe.
        ...    Si vous ne vous souvenez pas de l'adresse e-mail, vous pouvez contacter votre clinique pour obtenir de l'aide.
        ...    Adresse e-mail
        ...    Fermer
        ...    Envoyer un lien de réinitialisation
    ELSE IF    '${language}'=='Español'
        Append To List
        ...    ${current_lang}
        ...    ¿Tiene problemas para iniciar sesión?
        ...    Introduzca la dirección de correo electrónico asociada a su cuenta y le enviaremos un enlace para restablecer la contraseña.
        ...    Si no recuerda la dirección de correo electrónico, puede ponerse en contacto con su clínica para obtener ayuda.
        ...    Dirección de correo electrónico
        ...    Cerrar
        ...    Enviar enlace de restablecimiento
    ELSE IF    '${language}'=='Italiano'
        Append To List
        ...    ${current_lang}
        ...    Problemi di accesso?
        ...    Inserisci l'indirizzo e-mail associato al tuo account e ti invieremo un link per reimpostare la password.
        ...    Se non ricordi l'indirizzo e-mail, puoi rivolgerti alla tua clinica per ricevere aiuto.
        ...    Indirizzo e-mail
        ...    Chiudi
        ...    Invia link di reimpostazione
    ELSE IF    '${language}'=='Português'
        Append To List
        ...    ${current_lang}
        ...    Problemas em iniciar a sessão?
        ...    Introduza o endereço de e-mail associado à sua conta e enviar-lhe-emos um link para redefinir a sua palavra-passe.
        ...    Se não se lembrar do endereço de e-mail, pode contactar a sua clínica para obter ajuda.
        ...    Endereço de e-mail
        ...    Fechar
        ...    Enviar link para redefinição
    ELSE IF    '${language}'=='Nederlands'
        Append To List
        ...    ${current_lang}
        ...    Problemen met inloggen?
        ...    Voer het e-mailadres in dat bij je account hoort en we sturen je een e-mail met een link om je wachtwoord opnieuw in te stellen.
        ...    Als je het e-mailadres niet meer weet, kun je contact opnemen met je kliniek voor hulp.
        ...    E-mailadres
        ...    Sluiten
        ...    Resetlink verzenden
    ELSE IF    '${language}'=='Türkçe'
        Append To List
        ...    ${current_lang}
        ...    Oturum açarken sorun mu var?
        ...    Hesabınızla bağlantılı e-posta adresini girin; parolanızı sıfırlamanız için size e-posta ile bir bağlantı göndereceğiz.
        ...    E-posta adresini hatırlamıyorsanız, yardım için kliniğinizle iletişime geçebilirsiniz.
        ...    E-posta adresi
        ...    Kapat
        ...    Sıfırlama bağlantısı gönder
    ELSE IF    '${language}'=='Polski'
        Append To List
        ...    ${current_lang}
        ...    Problemy z logowaniem?
        ...    Wprowadź adres e-mail powiązany z Twoim kontem, a my wyślemy Ci link do zresetowania hasła.
        ...    Jeśli nie pamiętasz adresu e-mail, możesz skontaktować się z kliniką w celu uzyskania pomocy.
        ...    Adres e-mail
        ...    Zamknij
        ...    Wyślij link resetowania
    END
    Set Test Variable    @{current_lang}

Select Patient Language In Login
    [Arguments]    ${language}=English
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${problems_logging_in_modal_cancel}
    ...    timeout=1s
    IF    ${status}
        Try To Click Element    ${problems_logging_in_modal_cancel}
    END
    Wait Until Element Is Visible    ${locale}    10s
    Try To Click Element    ${locale}
    Try To Click Element    (//*[contains(text(),"${language}")])[last()]

Verify Problems Logging In Language Modal
    [Arguments]    ${language}
    Wait Until Element Is Visible    ${problems_logging_in_modal_title}
    ${modal_title}    Get Text    ${problems_logging_in_modal_title}
    Should Be Equal    ${modal_title}    ${current_lang}[0]
    ${modal_description1}    Get Text    ${problems_logging_in_modal_instructions}\[1]
    Should Be Equal    ${modal_description1}    ${current_lang}[1]
    ${modal_description2}    Get Text    ${problems_logging_in_modal_instructions}\[2]
    Should Be Equal    ${modal_description2}    ${current_lang}[2]
    ${text_field_label}    Get Text    ${problems_logging_in_email_label}
    Should Be Equal    ${text_field_label}    ${current_lang}[3]
    ${expected_close_button_label}    Convert To Uppercase    ${current_lang}[4]
    ${expected_submit_button_label}    Convert To Uppercase    ${current_lang}[5]
    ${cancel_button_text}    Get Text    ${problems_logging_in_modal_cancel}
    Should Be Equal    ${cancel_button_text}    ${expected_close_button_label}
    ${send_me_instructions_button_text}    Get Text    ${problems_logging_in_modal_submit}
    Should Be Equal    ${send_me_instructions_button_text}    ${expected_submit_button_label}

Input Patient Verification Code
    ${sms_message}    User Received SMS
    ${get_sms_code}    Get Substring    ${sms_message}    0    6
    Set Global Variable    ${get_sms_code}
    Type Verification Code When OIDC Is Enabled    ${get_sms_code}

Type Verification Code When OIDC Is Enabled
    [Arguments]    ${code}
    FOR    ${INDEX}    IN RANGE    0    6
        ${element}    Set Variable    //*[@id="sms-facade-{}"]
        ${oidc_sms_input}    Format String    ${element}    ${INDEX}
        Wait Until Page Contains Element    ${oidc_sms_input}    timeout=5s
        ${get_sms_code}    Set Variable    ${code}
        ${index1}    Evaluate    ${INDEX}+1
        ${get_sms_code}    Get Substring    ${get_sms_code}    ${INDEX}    ${index1}
        Try To Input Text    ${oidc_sms_input}    ${get_sms_code}
    END

Type Wrong Password For 6 Times
    [Arguments]    ${patient_email}
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    FOR    ${i}    IN RANGE    6
        Try To Input Text    ${oidc_username_input}    ${patient_email}
        Input Password    ${oidc_password_input}    ${wrong_password}
        Show Password
        Try To Click Element    ${oidc_login_button}
        Sleep    1.5s    # Sleep is needed as per NOONA-18026
        Wait Until Page Contains    ${incorrect_password_text}
    END

Patient Is Locked
    [Arguments]    ${patient_email}
    Try To Input Text    ${oidc_username_input}    ${patient_email}
    Try To Input Text    ${oidc_password_input}    ${DEFAULT_PASSWORD}
    Show Password
    Try To Click Element    ${oidc_login_button}
    Wait Until Page Contains    ${incorrect_password_text}

Show Password
    Try To Click Element    ${show_password_button}

Request For Password Reset As Patient
    [Arguments]    ${patient_email}    ${clinic}=Noona
    Try To Click Element    ${landing_page_login_button}
    Try To Click Element    ${problems_logging_in_oidc}
    Try To Input Text    ${problems_logging_in_email_textfield}    ${patient_email}
    Try To Click Element    ${problems_logging_in_modal_submit}
    Check Password Reset Confirmation Modal    ${patient_email}
    Try To Click Element    ${password_reset_modal_close_button}

Check Password Reset Confirmation Modal
    [Arguments]    ${patient_email}
    Wait Until Page Contains    ${instructions_to_login_header}
    Wait Until Page Contains    ${instructions_to_login_sent_text1}
    Wait Until Page Contains    ${instructions_to_login_sent_text2}
    Wait Until Page Contains    ${instructions_to_login_sent_text3}
    Wait Until Page Contains    ${patient_email}

Patient Is In The Patient's Landing Page
    Wait Until Element Is Visible    ${default_landing_page_login_button}
    Wait Until Page Contains    ${landing_page_connect_to_clinic_text}
    Location Should Contain    ${PATIENT_LOGIN_URL}

