*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}library.resource


*** Variables ***
${clinical_reports_bar}                                     //*[@id='library-intro-clinical-reports']
${clinical_report_content}                                  //*[@class='fhir-message-content']
${clinical_report_modal_title}                              //h4[@class='modal-title']
${close_clinical_reports_modal_button}                      //*[@id='modal-close-button']/button
${close_clinical_reports_modal_x_icon}                      //*[@class='modal-top-actions']/button
${reports_in_list}                                          //*[@class='document']
${path_to_clinical_reports_json}                            ${EXECDIR}${/}resources${/}patient${/}integration${/}clinical_reports.json
${path_to_clinical_reports_with_pdf_json}                   ${EXECDIR}${/}resources${/}patient${/}integration${/}document_reference.json
${clinical_report_contact_clinic_button}                    //*[@data-testid="clinical-reports-contact-clinic-button"]/ds-button/button
${clinical_report_contact_clinic_modal}                     //*[@id="open-question-modal"]
${clinical_report_contact_clinic_modal_title}               //*[@id="open-question-modal"]/form/div/h4[1]
${clinical_report_contact_clinic_modal_paragraph}           //*[@id="open-question-modal"]/form/div/p
${clinical_report_contact_clinic_modal_question_header}     //*[@id="open-question-modal"]/form/div/h4[2]
${clinical_report_contact_clinic_modal_question_area}       //*[@id="question-content"]
${clinical_report_contact_clinic_modal_close}               //*[@id="open-question-modal"]/form/div/ds-button[@data-testid="close-button"]/button
${clinical_report_contact_clinic_modal_send}                //*[@id="open-question-modal"]/form/div/ds-button[@data-testid="send-button"]/button
${clinical_report_confirmation_close_button}                //*[@id="message-sent-modal"]/div/div/ds-button[@data-testid="close-button"]/button
${clinical_report_patient_message}                          //*[@class="clinic-message-modal contactWithoutSymptom"]
${clinical_report_pdf_modal}                                //*[@data-testid="clinical-report-detail-modal"]
${clinical_report_pdf_modal_title}                          //*[@data-testid="clinical-report-detail-modal"]/div/h4[contains(text(),"Visit Summary Report")]
${clinical_report_pdf_modal_date}                           //*[@data-testid="clinical-report-detail-modal"]/div/div[@class="date"]
${clinical_report_pdf_modal_contact_clinic}                 //*[@data-testid="clinical-reports-contact-clinic-button"]
${clinical_report_pdf_modal_side_bar_toggle}                //*[@id="primarySidebarToggle"]
${clinical_report_pdf_modal_first_page_nav}                 //*[@id="primaryFirstPage"]
${clinical_report_pdf_modal_previous_page_nav}               //*[@id="primaryPrevious"]
${clinical_report_pdf_modal_page_number}                    //*[@id="pageNumber"]
${clinical_report_pdf_modal_next_page_nav}                  //*[@id="primaryNext"]
${clinical_report_pdf_modal_last_page_nav}                  //*[@id="primaryLastPage"]
${clinical_report_pdf_modal_zoom_out}                       //*[@id="primaryZoomOut"]
${clinical_report_pdf_modal_zoom_in}                        //*[@id="primaryZoomIn"]
${clinical_report_pdf_modal_scale_selector}                 //*[@id="scaleSelect"]
${clinical_report_pdf_modal_download_button}                //*[@id="download"]
${clinical_report_pdf_modal_pdf_viewer}                     //*[@id="viewerContainer"]
${clinical_report_pdf_modal_close_button}                   //*[@data-testid="clinical-report-detail-modal"]/div/ds-button[@id="modal-close-button"]/button
${clinical_report_header_page}                              //h1[@id="header-page-title"]
&{radiology_report}                                         loin=18726-0    name=Radiology report
${mrn_value}                                                mrn_value
${loin_code}                                                loin_code
${report-title}                                             report_title
${report-date}                                              report_date
${download-wait}                                            2.5 min
${new_clinical_report_status}                               final
${delete_clinical_report_status}                            entered-in-error


*** Keywords ***
Set Header
    [Arguments]    ${token}=${AUTOMATED_TESTS_EHR_TOKEN}
    &{header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${token}
    Set Test Variable    &{header}
    ${now}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S
    ${now}    Add Time To Date    ${now}    3 hours    # to replicate EEST time in gitlab, comment if running locally
    ${now}    Convert Date    ${now}    result_format=%Y-%m-%dT%H:%M:%S
    Set Test Variable    ${now}

Go To Clinical Reports As Patient
    Try To Click Element    ${library_button}
    Try To Click Element    ${clinical_reports_bar}

Go To Clinical Reports As Delegate User
    Try To Click Element    ${clinical_reports_bar}

Select Clinical Report From List
    [Arguments]    ${title}
    Try To Click Element    //*[@class='document' and contains(text(),"${title}")]

Button Is Displayed Even If Patient Doesn't Have Reports
    Login As Patient    ${patient_email}
    Go To Clinical Reports As Patient
    Wait Until Page Contains    No documents yet.
    Logout As Patient
    Close All App Instances

Patient Can Go Back To The Library Page
    Navigate Back Using Header
    Wait Until Element Is Visible    ${discover_library_header}
    Wait Until Page Contains Element    ${clinical_reports_bar}

User Can Click on Close Button or X icon
    Try To Click Element    ${close_clinical_reports_modal_button}
    Select Clinical Report From List    ${clinical_report_title}
    Try To Click Element    ${close_clinical_reports_modal_button}
    Select Clinical Report From List    ${clinical_report_title}

Reports are Displayed From The Most Recent One
    [Arguments]    ${title}
    Wait Until Element Is Visible    xpath=(${reports_in_list})[1]
    ${text}    Get Text    xpath=(${reports_in_list})[1]
    Should Be Equal    ${text}    ${title}

Send Clinical Reports To Patient In Default Clinic
    [Arguments]    ${ssn}    ${code}    ${report}
    Set Header
    Set Test Variable    ${clinical_report_title}    ${report}- ${now}
    ${clinical_report_id}    Generate Random String    16    [UPPER]
    ${body}    Get File    ${path_to_clinical_reports_json}
    ${body_mod}    Replace String    ${body}    ${mrn_value}    ${ssn}
    ${body_mod}    Replace String    ${body_mod}    ${loin_code}    ${code}
    ${body_mod}    Replace String    ${body_mod}    clinical_report_id    ${clinical_report_id}
    ${body_mod}    Replace String    ${body_mod}    ${report-title}    ${clinical_report_title}
    ${body_mod}    Replace String    ${body_mod}    ${report-date}    ${now}
    ${body_mod}    Replace String    ${body_mod}    report_status    ${new_clinical_report_status}
    Set Test Variable    ${body_mod}
    Set Test Variable    ${clinical_report_id}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /patientrecord    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'

Send Clinical Reports To Patient
    [Arguments]    ${ssn}    ${code}    ${report}
    Set Test Variable    ${clinical_report_title}    ${report}- ${now}
    ${clinical_report_id}    Generate Random String    16    [UPPER]
    ${body}    Get File    ${path_to_clinical_reports_json}
    ${body_mod}    Replace String    ${body}    ${mrn_value}    ${ssn}
    ${body_mod}    Replace String    ${body_mod}    ${loin_code}    ${code}
    ${body_mod}    Replace String    ${body_mod}    clinical_report_id    ${clinical_report_id}
    ${body_mod}    Replace String    ${body_mod}    ${report-title}    ${clinical_report_title}
    ${body_mod}    Replace String    ${body_mod}    ${report-date}    ${now}
    ${body_mod}    Replace String    ${body_mod}    report_status    ${new_clinical_report_status}
    Set Test Variable    ${body_mod}
    Set Test Variable    ${clinical_report_id}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /patientrecord    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    RETURN    ${response}

Send Clinical Report With PDF document
    [Arguments]    ${mrn}    ${code}    ${title}
    ${body}    Get File    ${path_to_clinical_reports_with_pdf_json}
    ${clinical_report_id}    Generate Random String    16    [LOWER]
    ${body_mod}    Replace String    ${body}    document_reference_id    ${clinical_report_id}
    ${body_mod}    Replace String    ${body_mod}    mrn_value    ${mrn}
    ${body_mod}    Replace String    ${body_mod}    loin_code    ${code}
    ${body_mod}    Replace String    ${body_mod}    report_title    ${title}
    ${body_mod}    Replace String    ${body_mod}    report_date    ${now}
    ${body_mod}    Replace String    ${body_mod}    report_status    ${new_clinical_report_status}
    Set Test Variable    ${body_mod}
    Set Test Variable    ${clinical_report_id}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /patientrecord    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    RETURN    ${response}

Get Patient's Record ID From The Response
    [Arguments]    ${response}
    ${content}    Convert To String    ${response}
    ${content}    Strip String    ${content}    characters={}
    ${content}    Fetch From Right    ${content}    {
    @{content}    Split String    ${content}    ,
    ${content}    Fetch From Right    ${content}[0]    :
    ${record_id}    Strip String    ${content}    characters="
    RETURN    ${record_id}

Verify Clinical Reports In Patient Account
    [Arguments]    ${is_visible}    ${title}=${sample_title_post}
    Login As Patient    ${intake_service_patient_1}[email]
    Go To Clinical Reports
    IF    '${is_visible}'=='true'
        Wait Until Page Contains    ${title} ${now}    timeout=10s
    ELSE
        Wait Until Page Contains    ${title}    timeout=10s
        Wait Until Page Does Not Contain    ${title} ${now}    timeout=3s
    END

Check Content Of The Clinical Report Contact Clinic Modal
    Try To Click Element    ${clinical_report_contact_clinic_button}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_modal_title}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_modal_paragraph}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_modal_question_header}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_modal_question_area}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_modal_close}
    Element Should Be Disabled    ${clinical_report_contact_clinic_modal_send}
    Input Text    ${clinical_report_contact_clinic_modal_question_area}    aa    # to check minimum input
    Element Should Be Disabled    ${clinical_report_contact_clinic_modal_send}

Close Clinical Report Contact Clinic Modal
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'    #remove this condition when NOONA-25438 is fixed
            ${keyboard_status}    Is Keyboard Shown
            IF    ${keyboard_status} == True
                Press Keycode    61    #hides the keyboard and keeps the clinical reports modal
            END
        END
    END
    Click Element    ${clinical_report_contact_clinic_modal_close}
    Wait Until Page Does Not Contain Element    ${clinical_report_contact_clinic_modal_question_area}


Contact Clinic About Clinical Report
    [Arguments]    ${message}
    Try To Click Element    ${clinical_report_contact_clinic_button}
    Sleep    1s
    Input Text    ${clinical_report_contact_clinic_modal_question_area}    ${message}
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'    #remove this condition when NOONA-25438 is fixed
            ${keyboard_status}    Is Keyboard Shown
            IF    ${keyboard_status} == True
                Press Keycode    61    #hides the keyboard and keeps the clinical reports modal
            END
        END
    END
    Try To Click Element    ${clinical_report_contact_clinic_modal_send}

Patient Message About Clinical Reports Is Displayed
    Wait Until Element Is Visible    //*[contains(string(),'${radiology_report}[name]')]
    IF    'native' in '${ENVIRONMENT}'
        Page Should Contain Text    Question about radiology report${SPACE}${current_date}
        Page Should Contain Text    ${current_date}
        Page Should Contain Text    Results or labs
    ELSE
        Page Should Contain    Question about radiology report${SPACE}${current_date}
        Page Should Contain    ${current_date}
        Page Should Contain    Results or labs
    END

Check Content Of The Clinical Pdf Report Modal
    [Arguments]    ${user}=patient
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Element Is Visible    ${clinical_report_pdf_modal}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_title}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_date}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_side_bar_toggle}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_first_page_nav}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_previous_page_nav}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_page_number}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_next_page_nav}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_last_page_nav}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_zoom_out}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_zoom_in}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_scale_selector}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_download_button}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_pdf_viewer}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_close_button}
        IF     '${user}' == 'delegate'
           Page Should Not Contain Element    ${clinical_report_pdf_modal_contact_clinic}
        ELSE
           Page Should Contain Element    ${clinical_report_pdf_modal_contact_clinic}
        END
    ELSE
        Wait Until Element Is Visible    ${clinical_report_pdf_modal}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_title}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_date}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_side_bar_toggle}
        # Uncomment the below verification step until this ticket is resolved https://vocscs.atlassian.net/browse/NOONA-23801. The page number indicator is still visible on web hence similar step is not commented.
        #Wait Until Element Is Visible    ${clinical_report_pdf_modal_page_number}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_zoom_out}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_zoom_in}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_pdf_viewer}
        Wait Until Element Is Visible    ${clinical_report_pdf_modal_close_button}
        IF     '${user}' == 'delegate'
           Page Should Not Contain Element    ${clinical_report_pdf_modal_contact_clinic}
        ELSE
           Page Should Contain Element    ${clinical_report_pdf_modal_contact_clinic}
        END
    END

Download should be done
    [Documentation]    Verifies that the directory has only one folder and it is not a temp file.
    ...
    ...    Returns path to the file
    [Arguments]    ${directory}
    ${files}    List Files In Directory    ${directory}
    Length Should Be    ${files}    1
    Should Not Match Regexp    ${files[0]}    (?i).*\\.tmp
    ${file}    Join Path    ${directory}    ${files[0]}
    Log    File was successfully downloaded to ${file}
    RETURN    ${file}

Verify PDF Download
    Wait Until Keyword Succeeds    10    ${download-wait}    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds    10    ${download-wait}    Download should be done    ${downloads-dir}
    Wait Until Keyword Succeeds    10    ${download-wait}    File Should Exist    ${downloads-dir}${/}*.pdf

Remove Clinical Reports Sent To Patient
    [Documentation]    This keyword can be used in case the test case has also the clinical report creation step included
    [Arguments]    ${token}
    ${header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${token}
    ${body_mod}    Replace String    ${body_mod}    final    ${delete_clinical_report_status}
    Set Test Variable    ${body_mod}
    ${response}    POST On Session    noona    /patientrecord    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '204'
    RETURN    ${response}
