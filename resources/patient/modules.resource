*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Library     SeleniumLibrary
Library     String
Library     Collections
Library     OperatingSystem


*** Variables ***
${diary}                                                Diary
${plus_button}                                          //*[@id="navigation-add-link"]/button/div/div
${add_symptom}                                          //*[@id="navigation-add-menu-new-symptom"]/button
${symptom_elements}                                     //button[contains(@class, "symptom")]
${radio_buttons_option_1}                               //*[contains(@class, "radio")][1]/label
${radio_button_severe_dizziness}                        //fe-radio-list[4]/div/div[2]/span[3]/label
${radio_buttons_option_2}                               //*[contains(@class, "radio")][2]/label
${radio_buttons_option_3}                               //*[contains(@class, "radio")][3]/label
${checkboxes}                                           //div[contains(@class, "checkbox")]/label
${cancel_button}                                        //*[contains(text(),'Cancel')]
${next_button}                                          //div[contains(text(),'Next')]
${save_button}                                          //div[contains(text(),'Save')]
${submit_symptom_button}                                //*[@id="submit-button-top" or contains(text(),'Send to clinic')]/button
${radio_button_constipation}                            //label[contains(@for, "change-in-stool-frequency-less")]
${radio_button_diarrhea}                                //label[contains(@for, "change-in-stool-frequency-more")]
${radio_button_pain_medication_used_true}               //label[contains(@for, "pain-medication-used-true")]
${radio_button_occasionally}                            //label[contains(text(),'Occasionally')]
${radio_button_rash_severe}                             //label[contains(@for, "rash-severity-severe")]
${radio_button_fever_true}                              //label[contains(@for, "fever-true")]
${radio_button_chest_pain_severe}                       //label[contains(@for, "pain-in-chest-severity-severe")]
${radio_button_bowel_movement_false}                    //label[contains(@for, "stool-within-a-day-false")]
${radio_button_antibiotics_true}                        //label[contains(@for, "antibiotics-true")]
${checkbox_hand-foot_syndrome}                          //label[contains(@for, "plantar-erythrodysesthesia-syndrome")]
${checkbox_dry_skin}                                    //label[contains(@for, "rash-types-dry-skin")]
${checkbox_dry_skin_v2}                                 //label[contains(@for, "rash-v-2-sub-types-dry-skin")]
${checkbox_dry_skin_v3}                                 //label[contains(@for, "rashV2Form-sub-types-dry-skin")]
${checkbox_dry_skin_v4}                                 //label[contains(@for, "mouthOrSkinForm-types-dry-skin")]
${checkbox_mouth_dry_skin}                              //label[contains(@for, "mouth-or-skin-types-dry-skin")]
${checkbox_skin_lesions}                                //label[contains(@for, "paronychia")]
${checkbox_infection_lesions}                           //label[contains(@for, "ash-types-paronychia-with-skin-ulceration")]
${checkbox_rash_infection_lesions}                      //label[contains(@for, "rashForm-types-paronychia-with-skin-ulceration")]
${checkbox_muscle_weakness_lower}                       //label[contains(@for, "muscle-weakness-location-lower")]
${checkbox_muscle_weakness_upper}                       //label[contains(@for, "muscle-weakness-location-upper")]
${checkbox_tooth_or_jaw}                                //label[contains(@for, "tooth-or-jaw")]
${checkbox_hair_loss_v2}                                //label[contains(@for, "other-symptoms-v-2-hair-changes-type-hair-loss")]
${checkbox_hair_color_changes_v2}                       //label[contains(@for, "other-symptoms-v-2-hair-changes-type-hair-color-changes")]
${checkbox_hair_loss}                                   //label[contains(@for, "other-symptoms-hair-changes-type-hair-loss")]
${checkbox_hair_color_changes}                          //label[contains(@for, "other-symptoms-hair-changes-type-hair-color-changes")]
${discard_button}                                       //div[contains(text(),'Discard changes')]
${more_symptom_button}                                  //*[@id="more-symptom"]
${symptom_index}                                        0
${text_area}                                            //div//textarea
@{text_list}                                            ${EMPTY}
@{number_list}                                          ${EMPTY}
${remember_me_no}                                       //*[@id="remember-me"]/div/div/yes-no-radio-button/div[1]/label
${remember_me_next_button}                              //*[@id="remember-me-next-button"]/button/div
${headache_slider}                                      head-ache-intensity
${dizziness_slider}                                     vertigo-intensity
${pain_slider}                                          //*[(@id='intensity')]
${pain_hdache_elsewhere_slider}                         //*[@id="intensity-when-hdache-queried-elsewhere"]
${abdominal_pain_slider}                                //div[contains(@id, "intensity")]
${checkbox_loss_of_urine_control}                       //label[contains(@for, "muscle-weakness-types-loss-of-urine-control")]
${checkbox_decrease_loss_of_urine_control}              //label[contains(@for, "decrease-in-muscle-strength-types-loss-of-urine-control")]
${checkbox_loss_of_bowels_control}                      //label[contains(@for, "muscle-weakness-types-loss-of-bowels-control")]
${checkbox_back_pain}                                   //label[contains(@for, "muscle-weakness-types-back-pain")]
${checkbox_sensitivity_to_cold}                         //label[contains(@for, "muscle-weakness-neuropathy-types-sensitivity-to-cold")]
${checkbox_general_rash}                                //label[contains(@for, "mouth-or-skin-types-general-rash")]
${checkbox_general_rash_2}                              //label[contains(@for, "mouthOrSkinForm-types-general-rash")]
${checkbox_mobility_aid_home}                           //label[contains(@for, "general-condition-movement-fatigue-mobility-aid-needed-home")]
${checkbox_mobility_aid_home_2}                         //label[contains(@for, "generalConditionMovementFatigueForm-mobility-aid-needed-home")]
${checkbox_visual_symptoms}                             //label[contains(@for, "sensations-additional-symptoms-visual-symptoms")]
${blood_in_urine}                                       //label[contains(@for, "urination-urination-symptom-type-bloody-urine")]
${checkbox_neuropathy_muscle_loss_of_urine_control}     //label[contains(@for, "neuropathyOrMuscleWeaknessForm-types-loss-of-urine-control")]
${checkbox_neuropathy_muscle_loss_of_bowels_control}    //label[contains(@for, "neuropathyOrMuscleWeaknessForm-types-loss-of-bowels-control")]
${checkbox_neuropathy_muscle_back_pain}                 //label[contains(@for, "neuropathyOrMuscleWeaknessForm-types-back-pain")]
${checkbox_other_hair_loss}                             //label[contains(@for, "otherSymptomsV2Form-hair-changes-type-hair-loss")]
${checkbox_other_hair_loss_2}                           //label[contains(@for, "otherSymptomsForm-hair-changes-type-hair-loss")]
${checkbox_sensitivity_to_cold_neuro}                   //label[contains(@for, "neuropathyOrMuscleWeaknessForm-neuropathy-types-sensitivity-to-cold")]
${checkbox_headache}                                    //label[contains(@for, "headacheOrVertigoV2Form-types-headache")]
${figure_abdomen_location}                              //*[@id="abdomen"]
${figure_left_front_arm}                                //*[@id="left-front-arm"]
${figure_right_front_elbow}                             right-front-elbow
${prolonged_pain_slider}                                prolonged-pain-intensity


*** Keywords ***
Get Symptoms
    [Arguments]    ${module}
    Set Test Variable    ${module}
    @{list_of_symptoms}    ModuleReader.Get Symptoms    ${EXECDIR}${/}data${/}modules${/}output${/}${module}.txt
    Set Test Variable    @{list_of_symptoms}

Go To Symptoms List
    Wait Until Page Contains Element    ${plus_button}
    Try To Click Element    ${plus_button}
    Wait Until Page Contains Element    ${add_symptom}
    Try To Click Element    ${add_symptom}
    Wait Until Page Contains Element    ${symptom_elements}

Compare Symptoms
    [Documentation]    Check And Select Radio Button Option Two is done twice, just in case
    ...    if the first one opens a new set of radio buttons.
    [Arguments]    ${symptom}
    Wait Until Page Contains Element    ${symptom_elements}
    ${more_symptom_exists}    Run Keyword And Return Status    Page Should Contain Element    ${more_symptom_button}
    IF    ${more_symptom_exists}==True    Show More Symptoms
    Sleep    1s
    Wait Until Page Contains Element    //div[contains(text(),'${symptom}')]
    Wait Until Element Is Visible    //div[contains(text(),'${symptom}')]
    Try To Click Element    //div[contains(text(),'${symptom}')]
    Check And Select Radio Button Option Two
    Check And Select Radio Button Option Two
    modules.Check And Tick Checkboxes
    Run Keyword And Continue On Failure    Check Questions
    Try To Click Element    ${cancel_button}
    IF    '${symptom}'!='Other symptom'    Discard Symptom

Save Entry
    Wait Until Element Is Visible    ${next_button}
    Click Element    ${next_button}
    Wait Until Page Contains Element    ${submit_symptom_button}
    Sleep    1s
    SeleniumLibrary.Click Element    ${submit_symptom_button}

Show More Symptoms
    Wait Until Page Contains Element    ${more_symptom_button}
    Scroll Element Into View    ${more_symptom_button}
    Wait Until Element Is Visible    ${more_symptom_button}
    Sleep    1s
    Try To Click Element    xpath=${more_symptom_button}

Discard Symptom
    Wait Until Page Contains Element    ${discard_button}
    Wait Until Element Is Visible    ${discard_button}
    Try To Click Element    ${discard_button}

Check Questions
    [Documentation]    Run Keyword And Ignore Error is done because there are different forms with the
    ...    same questions using different locators. Set Test Variable EMPTY is done for issues that are not valid or are in TODO list (pain point and female symptoms).
    FOR    ${data}    IN    @{questions_and_answers}
        IF    """${data}"""=="""How would you rate the severity of your constipation?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_constipation}
        END
        IF    """${data}"""=="""Have you had a bowel movement over the last 24 hours?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_constipation}
        END
        IF    """${data}"""=="""Have you passed gas (had flatulence) over the last 24 hours?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_constipation}
        END
        IF    """${data}"""=="""Have you passed gas (had flatulence) over the last 24 hours?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_bowel_movement_false}
        END
        IF    """${data}"""=="""How would you rate the severity of your diarrhea?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_diarrhea}
        END
        IF    """${data}"""=="""Have you used any medication to alleviate your diarrhea?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_diarrhea}
        END
        IF    """${data}"""=="""Have you used any medication to alleviate your constipation?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_constipation}
        END
        IF    """${data}"""=="""Did your diarrhea start after you began a course of antibiotics?"""
            SeleniumLibrary.Click Element    ${radio_button_antibiotics_true}
        END
        IF    """${data}"""=="""How would you rate the changes in mucus or saliva?"""
            Select Radio Button Option Two
        END
        IF    """${data}"""=="""Have you had a temperature over 98.6 °F / 37 °C?"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""Was the onset of your speech difficulty sudden?"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""How high was your temperature?"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""Could you please describe which medication, which strength, how often (per day) and for how long? Please also state whether or not the medication has helped. (Optional)"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""Which product, what dosage and what frequency (during one day) and for how long? Has the medication been effective?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_pain_medication_used_true}
        END
        IF    """${data}"""=="""Do you have rash all over your body?"""
            SeleniumLibrary.Click Element    ${radio_button_rash_severe}
        END
        IF    """${data}"""=="""How would you rate the severity of your skin symptom in your palms and feet?"""
            SeleniumLibrary.Click Element    ${checkbox_hand-foot_syndrome}
        END
        IF    """${data}"""=="""How would you rate the severity of your dry skin?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_dry_skin_v2}
        END
        IF    """${data}"""=="""How much interference does your dry skin cause?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_mouth_dry_skin}
        END
        IF    """${data}"""=="""How would you rate the severity of your nail fold infection and/or skin ulcers?"""
            SeleniumLibrary.Click Element    ${checkbox_skin_lesions}
        END
        IF    """${data}"""=="""How would you rate the severity of your nail fold infection and/or skin ulcers?"""
            Select Radio Button Option Two
        END
        IF    """${data}"""=="""Have you experienced any of the following oral symptoms?"""
            Select Radio Button Option Two
        END
        IF    """${data}"""=="""How would you rate the severity of sense of pressure or pain in the chest?"""
            SeleniumLibrary.Click Element    ${radio_button_chest_pain_severe}
        END
        IF    """${data}"""=="""How severe is the muscle weakness in the lower legs?"""
            SeleniumLibrary.Click Element    ${checkbox_muscle_weakness_lower}
        END
        IF    """${data}"""=="""What mobility aids do you need?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_mobility_aid_home}
        END
        IF    """${data}"""=="""What mobility aids do you need?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_mobility_aid_home_2}
        END
        IF    """${data}"""=="""How severe is the muscle weakness?"""
            SeleniumLibrary.Click Element    ${checkbox_muscle_weakness_upper}
        END
        IF    """${data}"""=="""Is there a large amount of fluid or is the fluid troublesome?"""
            Select Radio Button Option Two
        END
        IF    """${data}"""=="""Please describe the swelling / bloating in more detail."""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        IF    """${data}"""=="""Please describe the swelling / bloating in greater detail."""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        IF    """${data}"""=="""Has your mobility suddenly become reduced?"""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        IF    """${data}"""=="""Has your rate of sweating increased during follow-up?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""Have you used any medication to alleviate your symptoms?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""Redness of skin"""
            Select Radio Button Option Two
        END
        IF    """${data}"""=="""0 = None"""
            Set Test Variable    ${data}    None
        END
        IF    """${data}"""=="""10 = Worst possible"""
            Set Test Variable    ${data}    Worst possible
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_hair_loss}
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_hair_color_changes}
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_hair_loss_v2}
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_hair_color_changes_v2}
        END
        IF    """${data}"""=="""How would you rate the severity of your nail fold infection and/or skin ulcers?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_infection_lesions}
        END
        IF    """${data}"""=="""How would you rate the intensity of your sensitivity to cold/abnormal sensation?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_sensitivity_to_cold}
        END
        IF    """${data}"""=="""In which situations do you experience loss of control of urine (leakage)?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_loss_of_urine_control}
        END
        IF    """${data}"""=="""Please describe your skin rash"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_general_rash}
        END
        IF    """${data}"""=="""How much interference does your dry skin cause?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_dry_skin_v2}
        END
        IF    """${data}"""=="""Has your loss of control of bowel movements occurred suddenly (in less than 2 days)?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""How intense is your back pain on a scale of 0-10?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_back_pain}
        END
        IF    """${data}"""=="""How severe is your loss of control of bowel movements?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_loss_of_bowels_control}
        END
        IF    """${data}"""=="""How would you rate the intensity of your muscle weakness in the lower legs?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${checkbox_muscle_weakness_lower}
        END
        IF    """${data}"""=="""In which situations do you experience loss of control or urine (leakage)"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""Have you experienced sudden reduction in your mobility level?"""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        IF    """${data}"""=="""How often do you have a bowel movement?"""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        IF    """${data}"""=="""Please describe the swelling / bloating in greater detail."""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Do you have the strength to continue your treatment?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Do you feel that you need help at this stage?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Do you need any help for sweating and hot flashes?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Do you wish help for your erection problem?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Did your dizziness begin suddenly?"""
            Run Keyword And Ignore Error    SeleniumLibrary.Click Element    ${radio_button_severe_dizziness}
        END
        IF    """${data}"""=="""Did your dizziness begin suddenly?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""How often do you suffer from a headache?"""
            Run Keyword And Ignore Error    Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""Do you feel that you need help at this stage?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Does the pain radiate into the neck, left shoulder or left upper arm?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""No"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Irregular menstruation"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Change in the amount/duration of menstruation"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""No menses during the last 6 months"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Menstruation has stopped"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Irregular cycle, up to 3 months between menses"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Irregular cycle, up to 4-6 months between menses"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""In what way has the amount/duration of your menstruation changed?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Increased"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Decreased"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Prolonged"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Shortened"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Yes"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Symptom summary"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""Have you already received treatment for dizziness in another healthcare unit?"""
            Set Test Variable    ${data}    ${EMPTY}
        END
        IF    """${data}"""=="""How often do you feel this pain?"""
            Run Keyword And Ignore Error    Click Element    ${figure_abdomen_location}
        END
        IF    """${data}"""=="""How often do you feel this pain?"""
            Run Keyword And Ignore Error    Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""How often do you suffer from dizziness?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_headache}
        END
        IF    """${data}"""=="""How often do you suffer from dizziness?"""
            Run Keyword And Ignore Error    Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""In which situations does the pain occur?""" and """${module}"""=="""active-treatment_chemo-general"""
            Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""Do you feel the pain in bones or muscles?"""
            Run Keyword And Ignore Error    Click Element    ${figure_left_front_arm}
        END
        IF    """${data}"""=="""Is your belly tight and tender to touch?"""
            Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""Have you experienced similar abdominal pain before?"""
            Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""How intense is your pain on a scale of 0 to 10?"""
            Run Keyword And Ignore Error    Click Element    ${figure_right_front_elbow}
        END
        IF    """${data}"""=="""How intense is your pain on a scale of 0 to 10?"""
            Rate With Vertical Slider    10
        END
        IF    """${data}"""=="""When is your dizziness at its most severe?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""In which unit?"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""Has the loss of control of urine (leakage) occurred suddenly (in less than 2 days)?"""
            Run Keyword And Ignore Error    Select Radio Button Option Three
        END
        IF    """${data}"""=="""Which product, what dosage and what frequency (during one day) and for how long? Has the medication been effective? (Optional)"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""Are your visual symptoms associated with any of the following?"""
            Run Keyword And Ignore Error    Click Element    ${checkbox_visual_symptoms}
        END
        IF    """${data}"""=="""Changes to sexual health"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""In which situations do you experience loss of control of urine (leakage)?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_neuropathy_muscle_loss_of_urine_control}
        END
        IF    """${data}"""=="""How severe is your loss of control of bowel movements?"""
            Run Keyword And Ignore Error
            ...    Try To Click Element
            ...    ${checkbox_neuropathy_muscle_loss_of_bowels_control}
        END
        IF    """${data}"""=="""How intense is your back pain on a scale of 0-10?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_neuropathy_muscle_back_pain}
        END
        IF    """${data}"""=="""How would you rate the severity of your dry skin?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_dry_skin_v3}
        END
        IF    """${data}"""=="""How much interference does your dry skin cause?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_dry_skin_v3}
        END
        IF    """${data}"""=="""How much interference does your dry skin cause?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_dry_skin_v4}
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_other_hair_loss}
        END
        IF    """${data}"""=="""How much hair have you lost?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_other_hair_loss_2}
        END
        IF    """${data}"""=="""How would you rate the severity of your nail fold infection and/or skin ulcers?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_rash_infection_lesions}
        END
        IF    """${data}"""=="""How would you rate the intensity of your sensitivity to cold/abnormal sensation?"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_sensitivity_to_cold_neuro}
        END
        IF    """${data}"""=="""Please describe your skin rash"""
            Run Keyword And Ignore Error    Try To Click Element    ${checkbox_general_rash_2}
        END
        IF    """${data}"""=="""How frequent is your diarrhea?"""
            Run Keyword And Ignore Error    Select Radio Button Option Two
        END
        IF    """${data}"""=="""How severe is your constipation?"""
            Run Keyword And Ignore Error    Select Radio Button Option One
        END
        Run Keyword And Continue On Failure    Page Should Contain    ${data}
    END

Click Far Right On Slider
    [Arguments]    ${slider_locator}
    ${width}    ${height}    Get Element Size    ${slider_locator}
    ${slider_far_right}    Evaluate    (${width}/2)-10
    SeleniumLibrary. Click Element At Coordinates    ${slider_locator}    ${slider_far_right}    0

Select Symptom From List And Compare
    [Arguments]    ${module}
    FOR    ${symptom}    IN    @{list_of_symptoms}
        Set Test Variable    ${symptom}
        IF    '${symptom}'=='Vaginal symptoms'    CONTINUE
        @{questions_and_answers}    ModuleReader.Get Questions
        ...    ${EXECDIR}${/}data${/}modules${/}output${/}${module}.txt
        ...    ${symptom}
        Set Test Variable    @{questions_and_answers}
        Compare Symptoms    ${symptom}
    END

Check And Tick Checkboxes
    Sleep    1s
    ${checkboxes_exists}    Run Keyword And Return Status    Page Should Contain Element    ${checkboxes}
    IF    ${checkboxes_exists}==True    modules.Tick Checkboxes

Check And Select Radio Button Option Two
    Sleep    1s
    ${radio_buttons_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${radio_buttons_option_2}
    IF    ${radio_buttons_exists}==True    Select Radio Button Option Two

Check And Select Radio Button Option One
    Sleep    1s
    ${radio_buttons_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${radio_buttons_option_1}
    IF    ${radio_buttons_exists}==True    Select Radio Button Option One

Select Radio Button Option Two
    Wait Until Page Contains Element    ${radio_buttons_option_2}    20s
    @{radio_buttons}    Create List
    @{radio_buttons}    Get WebElements    ${radio_buttons_option_2}
    FOR    ${radio_button}    IN    @{radio_buttons}
        Try To Click Element    ${radio_button}
        Scroll Element Into View    ${radio_button}
    END

Select Radio Button Option One
    Wait Until Page Contains Element    ${radio_buttons_option_1}    20s
    @{radio_buttons}    Create List
    @{radio_buttons}    Get WebElements    ${radio_buttons_option_1}
    FOR    ${radio_button}    IN    @{radio_buttons}
        Try To Click Element    ${radio_button}
        Scroll Element Into View    ${radio_button}
    END

Check And Select Radio Button Option Three
    ${radio_buttons_exists}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${radio_buttons_option_3}
    IF    ${radio_buttons_exists}==True    Select Radio Button Option Three

Select Radio Button Option Three
    Wait Until Page Contains Element    ${radio_buttons_option_3}    20s
    @{radio_buttons}    Get WebElements    ${radio_buttons_option_3}
    FOR    ${radio_button}    IN    @{radio_buttons}
        Try To Click Element    ${radio_button}
        Scroll Element Into View    ${radio_button}
    END

Tick Checkboxes
    Wait Until Page Contains Element    ${checkboxes}    20s
    @{boxes}    Get WebElements    ${checkboxes}
    FOR    ${box}    IN    @{boxes}
        Wait Until Keyword Succeeds    20s    1    SeleniumLibrary.Click Element    ${box}
        Scroll Element Into View    ${box}
    END

Login Patient
    [Arguments]    ${patient_email}
    Login As Patient    ${patient_email}
    Wait Until Page Contains    ${diary}

Check And Write To Text Area
    ${text_area_exists}    Run Keyword And Return Status    Page Should Contain Element    ${text_area}
    IF    ${text_area_exists}==True    Write To Text Area

Write To Text Area
    Wait Until Page Contains Element    ${text_area}    20s
    @{text_list}    Create List
    @{text_areas}    Get WebElements    ${text_area}
    FOR    ${area}    IN    @{text_areas}
        ${random_madness}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
        Wait Until Keyword Succeeds    20s    1s    Input Text    ${area}    ${random_madness}
        Append To List    ${text_list}    ${random_madness}
    END
    Set Test Variable    @{text_list}
