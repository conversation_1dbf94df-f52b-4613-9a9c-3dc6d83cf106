*** Settings ***
Library    SeleniumLibrary
Library    Collections
Library    JSONLibrary
Library    OperatingSystem

*** Variables ***
${CONTAINER_ID}    #container-id           # Leave blank to scan the whole page
${JS_PATH}    ${EXECDIR}${/}data${/}accessibility_tools${/}missing_accessible_names_checker.js
${REPORT_DIR}     ${EXECDIR}${/}output${/}accessibility_reports
${REPORT_FILE}     ${REPORT_DIR}${/}missing_names_report.json
${SCREENSHOT_FILE}    ${REPORT_DIR}${/}missing_names_screenshot.png

*** Keywords ***
Check Missing Accessible Names On Page
    [Documentation]    Check for missing accessible names using custom JavaScript checker
    [Arguments]    ${container_id}=${CONTAINER_ID}    ${fail_on_issues}=True

    # Ensure report directory exists
    Create Directory    ${REPORT_DIR}

    Set Selenium Timeout    60s
    ${script}=    Get File    ${JS_PATH}
    ${raw}=    Execute JavaScript    window.prompt = () => "${container_id}"; ${script}
    Log Many    ${raw}

    # Convert result to JSON string manually
    ${json}=    Evaluate    json.dumps(${raw})    modules=json

    # Generate timestamped filename
    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${timestamped_report}=    Set Variable    ${REPORT_DIR}${/}missing_names_${timestamp}.json
    ${timestamped_screenshot}=    Set Variable    ${REPORT_DIR}${/}missing_names_${timestamp}.png

    Create File    ${timestamped_report}    ${json}
    Log    Accessibility report saved to ${timestamped_report}

    # Capture screenshot if issues exist
    ${has_issues}=    Evaluate    bool($raw)
    IF    ${has_issues}
        Sleep    1s
        Capture Page Screenshot    ${timestamped_screenshot}
        Log    Screenshot saved to ${timestamped_screenshot}
    END

    # Optional: Fail if warnings exist
    IF    ${has_issues} and ${fail_on_issues}
        Fail    Accessibility issues found:\n${json}
    END

    RETURN    ${raw}

Run Custom Accessibility Check
    [Documentation]    Run the custom accessibility checker with options
    [Arguments]    ${container_id}=${EMPTY}    ${fail_on_issues}=False

    ${issues}=    Check Missing Accessible Names On Page
    ...    container_id=${container_id}
    ...    fail_on_issues=${fail_on_issues}

    ${issue_count}=    Get Length    ${issues}
    Log    Found ${issue_count} accessibility issues with custom checker

    RETURN    ${issues}