*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}library.resource


*** Variables ***
${medication_list_tile}                         //*[@data-testid="library-medication-list"]
${medication_list_tile_header}                  //span[contains(text(),"Medication List")]
${medication_list_tile_text}                    //p[contains(text(),"See your medication list found in your medical record.")]
${medication_list_page_title}                   //*[@id="header-page-title"][contains(text(),"Medication List")]
${medication_list_page_back_button}             //*[@id="button-header-back"]
${medication_list_main_heading}                 //*[@id="medication-list-main-heading"][contains(text(),"Medication List")]
${medication_list_disclaimer_p1}                //*[@class="medication-list-header-description1"][contains(text(),"This is the medication list found in your medical record for the ${medication_test_clinic}[name]")]
${medication_list_disclaimer_p2}                //*[@class="medication-list-header-description2"][contains(text(),"Always refer to the most recent medication instructions provided by your prescribing medical provider.")]
${medication_name}                              //*[@class="medication-name"]
${medication_form}                              //*[@class="medication-form ng-star-inserted"]
${medication_instructions}                      //*[@class="medication-instructions ng-star-inserted"]
${expected_first_medication_name}               HM Loperamide HCl
${expected_second_medication_name}              Plecanatide
${expected_third_medication_name}               Coreg
${expected_four_medication_name}                Acremonium
${expected_five_medication_name}                Cyklokapron
${expected_six_medication_name}                 Fluzone Preservative Free
${expected_seven_medication_name}               Anesthetic Maximum Strength
${expected_eight_medication_name}               AMOXICILLIN AND CLAVULANATE POTASSIUM
${expected_nine_medication_name}                Vitaline Biotin Forte
${expected_ten_medication_name}                 Chronic Kidney Disease Support
${expected_first_medication_form}               Capsule
${expected_second_medication_form}              Tablet
${expected_third_medication_form}               Tablet
${expected_four_medication_form}                Solution Pen-injector
${expected_five_medication_form}                Solution
${expected_six_medication_form}                 Suspension Prefilled Syringe
${expected_seven_medication_form}               Gel (jelly)
${expected_eight_medication_form}               Liquid
${expected_nine_medication_form}                Tablet
${expected_ten_medication_form}                 Miscellaneous
${expected_first_medication_instruction}        1 - 10 /L (of 2 mg) Capsule Oral pc (bid) for 1 days
${expected_second_medication_instruction}       0.08 - 1.1 (2%) (of 3 mg) Tablet Oral ac (tid) & hs for 1 days
${expected_third_medication_instruction}        0.09 - 1.5 /mL (of 3.125 mg) Tablet Oral STAT
${expected_four_medication_instruction}         0.01 - 0.04 g (of 20000 PNU/mL) Solution Pen-injector Perfusion four times a day for 5 days
${expected_five_medication_instruction}         0.6 - 0.8 % of total Hgb (of 1000 mg/10mL) Intravenous ac (tid) & hs for 1 weeks
${expected_six_medication_instruction}          1 - 2 g/dose (of 0.5 mL) Intramuscular once
${expected_seven_medication_instruction}        87 mmol (of 20 %) Gel (jelly) Mouth/throat once
${expected_eight_medication_instruction}        Liquid Sublingual b.i.d. & at bedtime for 5 days
${expected_nine_medication_instruction}         1 Capsule (of 0.8 mg) Tablet Oral four times a day for 10 weeks
${expected_ten_medication_instruction}          0.87 g (of THERAPY PACK %) Miscellaneous Oral pc (tid) & hs for 5 weeks
${go_to_top_button}                             //*[@icon='icon-arrow-up']/button
${medication_list_empty_state}                  //*[contains(text(),"No medications to show")]
${medication_list_error_title}                  //div[@id="toast-container"]//div/div/div[@aria-label="Medication list load error"]
${medication_list_error_text}                   //div[@id="toast-container"]//div/div/div[contains(text(),"Your entire medication list could not be loaded. Please retry, or try again later.")]
${medication_list_retry_button}                 //div[@id="toast-container"]//div/div/div/*[@data-testid="toast-retry-button"]
${medication_list_close_error_button}           //div[@id="toast-container"]//div/div/*[@data-testid="toast-close-button"]


*** Keywords ***
Check That Medication List Is Displayed For Patient
    Wait Until Element Is Visible    ${discover_library_header}
    Page Should Contain Element    ${medication_list_tile}
    Element Should Be Visible    ${medication_list_tile_header}
    Element Should Be Visible    ${medication_list_tile_text}
    Try To Click Element    ${medication_list_tile}

Check That All Medication List Page Elements Are Displayed
    Wait Until Element Is Visible    ${medication_list_main_heading}
    Element Should Be Visible    ${medication_list_page_back_button}
    Element Should Be Visible    ${medication_list_disclaimer_p1}
    Element Should Be Visible    ${medication_list_disclaimer_p2}
    Page Should Contain Element    ${go_to_top_button}

Verify Medication List Names
    Wait Until Element Is Visible    ${medication_name}    timeout=${SYS_VAR_PAGE_TIMEOUT}    # needed for medication list to be pulled from ARIA
    ${medication_elements_name_list}    Get Webelements   ${medication_name}
    Set Test Variable    ${medication_elements_name_list}
    ${first_medication_name}    Get Text    ${medication_elements_name_list}[0]
    Should Be Equal As Strings    ${first_medication_name}    ${expected_first_medication_name}
    ${second_medication_name}    Get Text    ${medication_elements_name_list}[1]
    Set Test Variable    ${second_medication_name}
    Should Be Equal As Strings    ${second_medication_name}    ${expected_second_medication_name}
    ${third_medication_name}    Get Text    ${medication_elements_name_list}[2]
    Set Test Variable    ${third_medication_name}
    Should Be Equal As Strings    ${third_medication_name}    ${expected_third_medication_name}
    ${four_medication_name}    Get Text    ${medication_elements_name_list}[3]
    Set Test Variable    ${four_medication_name}
    Should Be Equal As Strings    ${four_medication_name}    ${expected_four_medication_name}
    ${five_medication_name}    Get Text    ${medication_elements_name_list}[4]
    Set Test Variable    ${five_medication_name}
    Should Be Equal As Strings    ${five_medication_name}    ${expected_five_medication_name}
    ${six_medication_name}    Get Text    ${medication_elements_name_list}[5]
    Set Test Variable    ${six_medication_name}
    Should Be Equal As Strings    ${six_medication_name}    ${expected_six_medication_name}
    ${seven_medication_name}    Get Text    ${medication_elements_name_list}[6]
    Set Test Variable    ${seven_medication_name}
    Should Be Equal As Strings    ${seven_medication_name}    ${expected_seven_medication_name}
    ${eight_medication_name}    Get Text    ${medication_elements_name_list}[7]
    Set Test Variable    ${eight_medication_name}
    Should Be Equal As Strings    ${eight_medication_name}    ${expected_eight_medication_name}
    ${nine_medication_name}    Get Text    ${medication_elements_name_list}[8]
    Set Test Variable    ${nine_medication_name}
    Should Be Equal As Strings    ${nine_medication_name}    ${expected_nine_medication_name}
    ${ten_medication_name}    Get Text    ${medication_elements_name_list}[9]
    Set Test Variable    ${ten_medication_name}
    Should Be Equal As Strings    ${ten_medication_name}    ${expected_ten_medication_name}

Verify Medication List Forms
    ${medication_form_list}    Get Webelements    ${medication_form}
    Set Test Variable    @{medication_form_list}
    ${first_medication_form}    Get Text    ${medication_form_list}[0]
    Should Be Equal As Strings    ${first_medication_form}    ${expected_first_medication_form}
    ${second_medication_form}    Get Text    ${medication_form_list}[1]
    Set Test Variable    ${second_medication_form}
    Should Be Equal As Strings    ${second_medication_form}    ${expected_second_medication_form}
    ${third_medication_form}    Get Text    ${medication_form_list}[2]
    Set Test Variable    ${third_medication_form}
    Should Be Equal As Strings    ${third_medication_form}    ${expected_third_medication_form}
    ${four_medication_form}    Get Text    ${medication_form_list}[3]
    Set Test Variable    ${four_medication_form}
    Should Be Equal As Strings    ${four_medication_form}    ${expected_four_medication_form}
    ${five_medication_form}    Get Text    ${medication_form_list}[4]
    Set Test Variable    ${five_medication_form}
    Should Be Equal As Strings    ${five_medication_form}    ${expected_five_medication_form}
    ${six_medication_form}    Get Text    ${medication_form_list}[5]
    Set Test Variable    ${six_medication_form}
    Should Be Equal As Strings    ${six_medication_form}    ${expected_six_medication_form}
    ${seven_medication_form}    Get Text    ${medication_form_list}[6]
    Set Test Variable    ${seven_medication_form}
    Should Be Equal As Strings    ${seven_medication_form}    ${expected_seven_medication_form}
    ${eight_medication_form}    Get Text    ${medication_form_list}[7]
    Set Test Variable    ${eight_medication_form}
    Should Be Equal As Strings    ${eight_medication_form}    ${expected_eight_medication_form}
    ${nine_medication_form}    Get Text    ${medication_form_list}[8]
    Set Test Variable    ${nine_medication_form}
    Should Be Equal As Strings    ${nine_medication_form}    ${expected_nine_medication_form}
    ${ten_medication_form}    Get Text    ${medication_form_list}[9]
    Set Test Variable    ${ten_medication_form}
    Should Be Equal As Strings    ${ten_medication_form}    ${expected_ten_medication_form}

Verify Medication List Instructions
    ${medication_instruction_list}    Get Webelements    ${medication_instructions}
    Set Test Variable    ${medication_instruction_list}
    ${first_medication_instruction}    Get Text    ${medication_instruction_list}[0]
    Set Test Variable    ${first_medication_instruction}
    Should Be Equal As Strings    ${first_medication_instruction}    ${expected_first_medication_instruction}
    ${second_medication_instruction}    Get Text    ${medication_instruction_list}[1]
    Set Test Variable    ${second_medication_instruction}
    Should Be Equal As Strings    ${second_medication_instruction}    ${expected_second_medication_instruction}
    ${third_medication_instruction}    Get Text    ${medication_instruction_list}[2]
    Set Test Variable    ${third_medication_instruction}
    Should Be Equal As Strings    ${third_medication_instruction}    ${expected_third_medication_instruction}
    ${four_medication_instruction}    Get Text    ${medication_instruction_list}[3]
    Set Test Variable    ${four_medication_instruction}
    Should Be Equal As Strings    ${four_medication_instruction}    ${expected_four_medication_instruction}
    ${five_medication_instruction}    Get Text    ${medication_instruction_list}[4]
    Set Test Variable    ${five_medication_instruction}
    Should Be Equal As Strings    ${five_medication_instruction}    ${expected_five_medication_instruction}
    ${six_medication_instruction}    Get Text    ${medication_instruction_list}[5]
    Set Test Variable    ${six_medication_instruction}
    Should Be Equal As Strings    ${six_medication_instruction}    ${expected_six_medication_instruction}
    ${seven_medication_instruction}    Get Text    ${medication_instruction_list}[6]
    Set Test Variable    ${seven_medication_instruction}
    Should Be Equal As Strings    ${seven_medication_instruction}    ${expected_seven_medication_instruction}
    ${eight_medication_instruction}    Get Text    ${medication_instruction_list}[7]
    Set Test Variable    ${eight_medication_instruction}
    Should Be Equal As Strings    ${eight_medication_instruction}    ${expected_eight_medication_instruction}
    ${nine_medication_instruction}    Get Text    ${medication_instruction_list}[8]
    Set Test Variable    ${nine_medication_instruction}
    Should Be Equal As Strings    ${nine_medication_instruction}    ${expected_nine_medication_instruction}
    ${ten_medication_instruction}    Get Text    ${medication_instruction_list}[9]
    Set Test Variable    ${ten_medication_instruction}
    Should Be Equal As Strings    ${ten_medication_instruction}    ${expected_ten_medication_instruction}

Verify Medication List Content
    Verify Medication List Names
    Verify Medication List Forms
    Verify Medication List Instructions

Verify Scroll Up Button Functionality
    Scroll Element Into View    ${medication_instruction_list}[9]
    Wait Until Element Is Visible    ${go_to_top_button}
    Try To Click Element    ${go_to_top_button}
    Element Should Be Visible    ${medication_list_main_heading}

Check That Medication List Is Displayed With Empty State
    Wait Until Element Is Visible    ${medication_list_main_heading}
    Element Should Be Visible    ${medication_list_page_back_button}
    Element Should Be Visible    ${medication_list_disclaimer_p1}
    Element Should Be Visible    ${medication_list_disclaimer_p2}
    Wait Until Element Is Visible    ${medication_list_empty_state}    timeout=10s    # needed for medication list to be pulled from ARIA

Check That Medication List Is Displayed With Error State
    Wait Until Element Is Visible    ${medication_list_main_heading}
    Element Should Be Visible    ${medication_list_page_back_button}
    Element Should Be Visible    ${medication_list_disclaimer_p1}
    Element Should Be Visible    ${medication_list_disclaimer_p2}
    Wait Until Element Is Visible    ${medication_list_error_title}    timeout=5s     # needed for medication error message to be displayed
    Wait Until Element Is Visible    ${medication_list_error_text}
    Wait Until Element Is Visible    ${medication_list_retry_button}
    Wait Until Element Is Visible    ${medication_list_close_error_button}
