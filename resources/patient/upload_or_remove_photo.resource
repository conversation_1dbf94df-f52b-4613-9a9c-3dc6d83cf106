*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource


*** Variables ***
${file_sample_jpg_100kb}                file_example_JPG_100kB.jpg
${add_a_photo_button}                   //div[@class='photo-uploader']    # //div[@class='photo-uploader dz-clickable']
${file_browse_link}                     //input[@type='file'][1]
${wellbeing_save_button}                //div[@class='modal-buttons-container ng-star-inserted']//descendant::button[2]
${diary_entry}                          //section[@class='container timeline-month ng-star-inserted'][1]/descendant::wellbeing-indicator[1]
${diary_entry_modal}                    //div[@class='modal-content']
${preview_image_section}                //div[@id='previewsContainer']
${image_id}                             //div[@id='previewsContainer']//img
${modal_close_button}                   diary-entry-show-modal-button-close
${diary_entry}                          //section[@class='container timeline-month ng-star-inserted'][1]/descendant::wellbeing-indicator[1]
${diary_entry_modal}                    //div[@class='modal-content']
${remove_photo}                         //div[@class='previews']//a[contains(text(),'Remove photo')]
${next_button}                          //*[contains(text(),'Next')]
${error_photo_too_large}                //span[contains(text(),'The photo is too large.')]
${error_unsupported_file_type}          //span[contains(text(),'There was an error uploading the photo. Please try again.')]
${error_photo_upload}                   //span[contains(text(),'An error occured while attaching files')]
${photo_description_textarea}           //*[@name="photo-description"]
${photo_description_instruction}        //h4[contains(text(),'If necessary, please give further details about th')]
${cancel_photo_upload_button}           //div[@class='previews']//div[1]//a[contains(text(),'Cancel')]
${enlarged_image_next_button}           //body/div[@id='patient-app-new']/div[@id='lightbox']/div[1]/div[1]/div[2]/div[2]/button[2]
${enlarged_image_previous_button}       //*[@title="Previous (arrow left)"]
${enlarged_image_close_button}          //*[@title='Close (Esc)']
${image_thumbnail}                      //*[@class="dz-image dz-image__loaded"]
${uploaded_image}                       //*[@id="photo-surgery-drain-form-previews"]/div[2]/div[1]/img
${uploaded_image_complete}              //div[@class='dz-preview dz-file-preview dz-processing dz-success dz-complete']
${diary_note_upload_image}              //img[starts-with(@src, 'blob')]
${noona_photo_preview}                  //noona-photopreview
${noona_photo_preview_uploaded_photo}   //img[@class='pswp__img']
${noona_photo_preview_toggle_fullscreen_button}     //button[@title="Toggle fullscreen"]
${noona_photo_preview_x_close_button}    //button[@title="Close (Esc)"]


*** Keywords ***
Verify Upload Button
    Wait Until Page Contains Element    ${add_a_photo_button}
    Scroll Element Into View    ${add_a_photo_button}
    Element Should Be Visible    ${add_a_photo_button}
    Element Should Be Enabled    ${add_a_photo_button}

Add Diary Note Photo
    [Arguments]    ${file}    ${update}=no
    IF    '${update}'=='no'    Try To Click Element    ${add_a_photo_button}
    IF    '${update}'=='yes'
        ${index}    Set Variable    3
    ELSE
        ${index}    Set Variable    2
    END
    Choose File    //input[@type='file']    ${EXECDIR}${/}data${/}upload_photos${/}JPG${/}${file}
    Scroll Element Into View    ${wellbeing_save_button}
    Wait Until Element Is Visible    ${diary_note_upload_image}    60s

Verify Uploaded Image
    Wait Until Element Is Visible    ${diary_entry}
    Try To Click Element    ${diary_entry}
    Wait Until Element Is Visible    ${diary_entry_modal}
    Wait Until Element Is Visible    ${image_id}
    Click Element    ${modal_close_button}

Add Or Remove Photos
    [Arguments]    ${file}
    ${remove_photo_exists}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${remove_photo}
    ...    timeout=3s
    IF    ${remove_photo_exists}==True
        Remove Uploaded Photo
    ELSE
        Run Keyword    Add Diary Note Photo    ${file}    update=yes
    END

Remove Uploaded Photo
    Wait Until Element Is Visible    ${remove_photo}
    Try To Click Element    ${remove_photo}
    Page Should Not Contain    ${remove_photo}

Verify Photo Upload Error
    Scroll Element Into View    ${next_button}
    ${photo_upload_error}    Run Keyword And Return Status
    ...    Wait For Element To Be Present
    ...    ${error_photo_too_large}
    IF    ${photo_upload_error}==True
        Photo Too Large Error
    ELSE IF    ${photo_upload_error}==False
        Unsupported File Type Error
    END

Photo Too Large Error
    Wait For Element To Be Present    ${error_photo_too_large}
    Remove Uploaded Photo

Unsupported File Type Error
    Wait For Element To Be Present    ${error_unsupported_file_type}
    Remove Uploaded Photo

Add Max Number Of Photo
    Sleep    1    # give a bit of time to upload another set of photos from previous photo upload step
    Repeat Keyword    4 times    Add Photo To Symptom    ${file_sample_jpg_100kb}    click_add_photo=no    multiple=yes
    Wait Until Page Contains    You have uploaded the maximum number of photos.

Write Photo Description
    ${random_description}    Generate Random String    40
    Set Test Variable    ${random_description}
    Try To Input Text    ${photo_description_textarea}    ${random_description}

Verify Photo Group Description Is Hidden
    Wait For Element To Not Be Present    ${photo_description_instruction}
    Wait For Element To Not Be Present    ${photo_description_textarea}
    Wait For Element To Be Present    ${add_a_photo_button}

Cancel Photo Upload
    [Arguments]    ${file}    ${update}=no
    IF    '${update}'=='no'    Try To Click Element    ${add_a_photo_button}
    IF    '${update}'=='yes'
        ${index}    Set Variable    2
    ELSE
        ${index}    Set Variable    1
    END
    Choose File    //input[@type='file'][${index}]    ${EXECDIR}${/}data${/}upload_photos${/}JPG${/}${file}
    Try To Click Element    ${cancel_photo_upload_button}
    Scroll Element Into View    ${next_button}

Click Photo Thumbnail To View Full Screen
    Wait Until Element Is Enabled    (${image_thumbnail})[2]
    Wait Until Element Is Visible    (${remove_photo})[2]
    Try To Click Element    (${image_thumbnail})[1]//img

Click Left/Right Arrow To Move To Previous/Next Photo
    [Documentation]    Multiple images should be uploaded
    ${next_image_button}    Run Keyword And Return Status
    ...    Wait For Element To Be Present
    ...    ${enlarged_image_next_button}
    IF    ${next_image_button}==True
        Try To Click Element    ${enlarged_image_next_button}
    ELSE IF    ${next_image_button}==False
        Try To Click Element    ${enlarged_image_previous_button}
    ELSE
        Run Keyword    Close Image Full Screen View
    END

Close Image Full Screen View
    Try To Click Element    ${enlarged_image_close_button}
    Wait Until Element Is Visible    (${remove_photo})[1]    timeout=3s
