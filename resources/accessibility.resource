*** Settings ***
Documentation    Accessibility testing keywords and utilities
Library          SeleniumLibrary
Library          ${EXECDIR}${/}resources${/}libraries${/}accessibility_library.py
Library          Collections
Library          OperatingSystem

*** Variables ***
${ACCESSIBILITY_REPORT_DIR}    ${EXECDIR}${/}output${/}accessibility_reports
${WCAG_LEVEL}                  AA
${FAIL_ON_VIOLATIONS}          True

*** Keywords ***
Setup Accessibility Testing
    [Documentation]    Initialize accessibility testing environment
    Create Directory    ${ACCESSIBILITY_REPORT_DIR}

Run Accessibility Scan
    [Documentation]    Run basic axe-core accessibility scan
    [Arguments]    ${context}=None    ${options}=None

    # Call the Python library method directly
    ${results}=    AccessibilityLibrary.Run Accessibility Scan    context=${context}    options=${options}

    RETURN    ${results}

Run Full Accessibility Scan
    [Documentation]    Run a comprehensive accessibility scan using axe-core
    [Arguments]    ${report_name}=accessibility_report    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${timestamp}=    Get Current Date    result_format=%Y%m%d_%H%M%S
    ${report_file}=    Set Variable    ${ACCESSIBILITY_REPORT_DIR}${/}${report_name}_${timestamp}.json

    # Run axe-core scan using the library instance
    ${results}=    AccessibilityLibrary.Run Accessibility Scan
    ${violations}=    Get From Dictionary    ${results}    violations

    # Save report using the library instance
    AccessibilityLibrary.Save Accessibility Report    ${report_file}

    # Log summary
    ${violation_count}=    Get Length    ${violations}
    Log    Found ${violation_count} accessibility violations

    IF    ${violation_count} > 0 and ${fail_on_violations}
        Fail    Found ${violation_count} accessibility violations
    END

    RETURN    ${violations}

Check WCAG Compliance
    [Documentation]    Check WCAG compliance at specified level using axe-core
    [Arguments]    ${level}=${WCAG_LEVEL}    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    # Call the Python library method directly using the library instance
    ${violations}=    AccessibilityLibrary.Check Wcag Compliance    level=${level}    fail_on_violations=False
    ${violation_count}=    Get Length    ${violations}

    IF    ${violation_count} > 0
        Log    WCAG ${level} violations found: ${violation_count}
        FOR    ${violation}    IN    @{violations}
            ${rule_id}=    Get From Dictionary    ${violation}    id    default=unknown
            ${description}=    Get From Dictionary    ${violation}    description    default=No description
            Log    Violation: ${rule_id} - ${description}
        END

        IF    ${fail_on_violations}
            Fail    Found ${violation_count} WCAG ${level} violations
        END
    ELSE
        Log    No WCAG ${level} violations found
    END

    RETURN    ${violations}

Check Keyboard Navigation
    [Documentation]    Test keyboard navigation accessibility
    
    # Test Tab navigation
    Press Keys    None    TAB
    ${focused_element}=    Get WebElement    css:*:focus
    Element Should Be Visible    ${focused_element}
    
    # Test Enter/Space on focusable elements
    ${tag_name}=    Get Element Attribute    ${focused_element}    tagName
    IF    '${tag_name.lower()}' in ['button', 'a', 'input']
        Press Keys    None    RETURN
        Sleep    0.5s
    END

Check Color Contrast
    [Documentation]    Check color contrast compliance
    [Arguments]    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${rules}=    Create List    color-contrast
    ${violations}=    AccessibilityLibrary.Check Specific Rules    ${rules}    fail_on_violations=${fail_on_violations}

    RETURN    ${violations}

Check Form Accessibility
    [Documentation]    Check form-specific accessibility requirements
    [Arguments]    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${form_rules}=    Create List
    ...    label
    ...    form-field-multiple-labels
    ...    duplicate-id-aria
    ...    aria-required-attr
    ...    aria-valid-attr-value

    ${violations}=    AccessibilityLibrary.Check Specific Rules    ${form_rules}    fail_on_violations=${fail_on_violations}

    RETURN    ${violations}

Check Image Accessibility
    [Documentation]    Check image accessibility (alt text, etc.)
    [Arguments]    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${image_rules}=    Create List
    ...    image-alt
    ...    image-redundant-alt
    ...    object-alt
    ...    input-image-alt

    ${violations}=    AccessibilityLibrary.Check Specific Rules    ${image_rules}    fail_on_violations=${fail_on_violations}

    RETURN    ${violations}

Check Heading Structure
    [Documentation]    Check heading hierarchy and structure
    [Arguments]    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${heading_rules}=    Create List
    ...    heading-order
    ...    empty-heading
    ...    p-as-heading

    ${violations}=    AccessibilityLibrary.Check Specific Rules    ${heading_rules}    fail_on_violations=${fail_on_violations}

    RETURN    ${violations}

Check Focus Management
    [Documentation]    Check focus management and visibility
    [Arguments]    ${fail_on_violations}=${FAIL_ON_VIOLATIONS}

    ${focus_rules}=    Create List
    ...    focus-order-semantics
    ...    focusable-content
    ...    tabindex

    ${violations}=    AccessibilityLibrary.Check Specific Rules    ${focus_rules}    fail_on_violations=${fail_on_violations}

    RETURN    ${violations}

Run Accessibility Test Suite
    [Documentation]    Run a comprehensive accessibility test suite
    [Arguments]    ${page_name}=current_page    ${wcag_level}=${WCAG_LEVEL}
    
    Log    Starting accessibility test suite for: ${page_name}
    
    # Setup
    Setup Accessibility Testing
    
    # Run comprehensive tests
    ${wcag_violations}=    Check WCAG Compliance    level=${wcag_level}    fail_on_violations=False
    ${contrast_violations}=    Check Color Contrast    fail_on_violations=False
    ${form_violations}=    Check Form Accessibility    fail_on_violations=False
    ${image_violations}=    Check Image Accessibility    fail_on_violations=False
    ${heading_violations}=    Check Heading Structure    fail_on_violations=False
    ${focus_violations}=    Check Focus Management    fail_on_violations=False
    
    # Test keyboard navigation
    TRY
        Check Keyboard Navigation
        Log    Keyboard navigation test passed
    EXCEPT
        Log    Keyboard navigation test failed    WARN
    END
    
    # Generate summary report
    ${total_violations}=    Evaluate    len($wcag_violations) + len($contrast_violations) + len($form_violations) + len($image_violations) + len($heading_violations) + len($focus_violations)
    
    Log    Accessibility test suite completed for ${page_name}
    Log    Total violations found: ${total_violations}
    
    # Save comprehensive report
    Run Full Accessibility Scan    ${page_name}_comprehensive    fail_on_violations=False
    
    RETURN    ${total_violations}

Accessibility Test Teardown
    [Documentation]    Clean up after accessibility tests
    
    # Archive old reports (keep last 10)
    ${reports}=    List Files In Directory    ${ACCESSIBILITY_REPORT_DIR}    *.json
    ${report_count}=    Get Length    ${reports}
    
    IF    ${report_count} > 10
        ${sorted_reports}=    Evaluate    sorted($reports)
        ${reports_to_remove}=    Get Slice From List    ${sorted_reports}    0    ${report_count - 10}
        FOR    ${report}    IN    @{reports_to_remove}
            Remove File    ${ACCESSIBILITY_REPORT_DIR}${/}${report}
        END
    END
