#!/bin/bash

create_virtualenv() {
  echo -e "\033[1;37m## Configuring virtualenv \033[1;34m$VENV_NAME\033[1;37m...\033[0m"

  python3 -m venv "$VENV_HOME/$VENV_NAME"
  source "$VENV_HOME/$VENV_NAME/bin/activate"
  python3 -m pip install --upgrade pip wheel
  pip install -r requirements.txt

  echo "virtualenv configured."
}

install_homebrew() {
  echo -e "\033[1;37m## Installing Homebrew...\033[0m"

  if [[ $(command -v brew 1>/dev/null) ]]; then
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"

    # The single quotes around eval are there on purpose
    # so that the variable expansion happens at the time
    # the profile is loaded instead of now
    # shellcheck disable=SC2016
    if [[ $(uname -m) = "arm64" ]]; then
      echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> "$HOME/.zprofile"
      eval "$(/opt/homebrew/bin/brew shellenv)"
    else
      echo 'eval "$(/usr/local/bin/brew shellenv)"' >> "$HOME/.zprofile"
      eval "$(/usr/local/bin/brew shellenv)"
    fi

    echo "Homebrew installed."
  else
    echo "Homebrew already installed."
  fi
}

install_homebrew_bundle() {
  echo -e "\033[1;37m## Installing Homebrew bundle...\033[0m"

  brew bundle --no-lock 1>/dev/null

  echo "Homebrew bundle installed."
}

echo -e "\033[1;37m# Setting up environment\033[0m"

mkdir -p "$VENV_HOME"

install_homebrew

install_homebrew_bundle

brew install python3

create_virtualenv

# Convenience to install chromedriver and getting it to added to
# the local PATH environment variable
which chromedriver
if [ $? = 1 ]
then
  brew install chromedriver
fi

brew upgrade chromedriver

# Mark the above installed chromedriver as a trusted application
CHROMEDRIVER_PATH=$(brew info chromedriver | grep "/chromedriver" | grep -v ".rb$" |grep -v "^http" | cut -d' ' -f1  | paste -d'/'  - -)
xattr $CHROMEDRIVER_PATH | grep "com.apple.quarantine"
if [ $? -eq 0 ]
then
  echo "Removing apple quarantine attribute from brew installed chromedriver binary path ${CHROMEDRIVER_PATH}"
  xattr -d com.apple.quarantine $CHROMEDRIVER_PATH
fi
