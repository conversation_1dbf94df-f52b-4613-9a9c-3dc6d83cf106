*** Settings ***
Documentation     RPA for Jama.
...               - Preconditions: Get client id and secret from your Jama profile. Go to "My Details" and "Set API Credentials".
...               - Search API id for the cycle under test. This you can find by using filtering.
...               - Project ID is set to Noona
...               REMEMBER TO UPDATE THE CYCLE ID VARIABLE!
...               - Update number_of_max_test_cases accordingly
...               - To run all tasks: robot  jama/jama.robot
...               - To run individual task: robot  --include "tag name" jama/jama.robot
Library           OperatingSystem
Library           String
Library           RequestsLibrary
Library           JSONLibrary
Library           Collections

*** Variables ***
${CLIENT_ID}
${CLIENT_SECRET}
${CYCLE_ID}
${PROJECT_ID}
#${PROJECT_ID}   22
${file_upload_path}     ${EXECDIR}${/}reports${/}${/}${ZIP_NAME}.zip
${attachment_name}  Test automation regression results
${attachment_description}  Test automation regression results
${expected_result_text}   Automated test case passed. Details included in the attached report file(s) in ${TEST_CASE_ID} and can be searched by the test case id.
...    For failed test cases, investigation results are logged under the actual failed test case.
${number_of_max_test_cases}    356

*** Tasks ***
Set Test Run Status And Add Expected Result Text
    [Tags]  update_test_status
    Get Test Run IDs From The Test Cycle    ${number_of_max_test_cases}
    Patch Update Test Runs    PASSED    ${expected_result_text}
    [Teardown]  Delete All Sessions

Attach File To Test Runs In The Cycle
    [Tags]  add_attachment
    Get Test Run IDs From The Test Cycle   ${number_of_max_test_cases}
    Create An Attachment Item
    Upload Attachment
    Associate The Attachment With Test Run
    [Teardown]  Delete All Sessions

*** Keywords ***
Get authToken by Password Authentication
    Create Session  hook    https://${CLIENT_ID}:${CLIENT_SECRET}@varian-medical.jamacloud.com/rest  verify=${True}
    ${data}      Create Dictionary     grant_type=client_credentials
    ${headers}      Create Dictionary     Content-Type=application/x-www-form-urlencoded
    ${resp}        POST On Session    hook    /oauth/token    data=${data}     headers=${headers}
    Should Be Equal As Strings  ${resp.status_code}     200
    ${accessToken}    evaluate    $resp.json().get("access_token")
    Set Test Variable   ${accessToken}

Create Session And Headers
    Create Session  hook    https://varian-medical.jamacloud.com/rest
    ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
    Set Test Variable   ${headers}

Get Test Run IDs From The Test Cycle
    [Arguments]    ${number_of_max_test_cases}
    Get authToken by Password Authentication
    Create Session And Headers
    ${test_run_ids}     Create List
    FOR     ${i}    IN RANGE    ${number_of_max_test_cases}
        ${resp}        GET On Session    hook    url=/v1/testruns?testCycle=${CYCLE_ID}&startAt=${i}&maxResults=1   headers=${headers}  data=${EMPTY}
        Should Be Equal As Strings  ${resp.status_code}     200
        ${json_content}     Convert String to JSON  ${resp.content}
        ${test_run_id}    Get Value From Json     ${json_content}     $..id
        Exit For Loop If    '${test_run_id}'=='[]'
        ${test_run_id}     Convert JSON To String  ${test_run_id}
        ${test_run_id}    Remove String  ${test_run_id}  [   ]
        ${test_run_id}  Convert To Integer  ${test_run_id}
        Append To List  ${test_run_ids}     ${test_run_id}
    END
    Set Test Variable   ${test_run_ids}

Create An Attachment Item
    ${json_data}    Load JSON From File     ${EXECDIR}${/}jama${/}attachment_payload.json
    ${updated_file_data}    Update Value To Json    ${json_data}    $..name    ${attachment_name}
    ${updated_file_data}    Update Value To Json    ${updated_file_data}    $..description    ${attachment_description}
    ${resp}        POST On Session    hook    /v1/projects/${PROJECT_ID}/attachments    json=${updated_file_data}     headers=${headers}
    Should Be Equal As Strings  ${resp.status_code}     201
    ${body}    Evaluate    json.loads('''${resp.content}''')    json
    ${attachment_id}    Get Value From Json     ${body}     $..id
    ${attachment_id}    Get From List   ${attachment_id}    0
    Set Test Variable   ${attachment_id}
    Log To Console  ATTACHMENT ID: ${attachment_id}

Upload Attachment
    &{fileParts}=    Create Dictionary
    Create Multi Part    ${fileParts}    file    ${FILE_UPLOAD_PATH}    multipart/form-data
    ${headers}      Create Dictionary     Accept=*/*  Authorization=Bearer ${accessToken}
    ${resp}        PUT On Session    hook    /v1/attachments/${attachment_id}/file    files=${fileParts}   headers=${headers}
    Should Be Equal As Strings  ${resp.status_code}     200

Associate The Attachment With Test Run
    FOR    ${test_run_id}    IN     @{test_run_ids}
        IF    '${test_run_id}'=='${TEST_CASE_API_ID}'
            ${json_data}    Load JSON From File     ${EXECDIR}${/}jama${/}associate_attachment_payload.json
            ${updated_file_data}    Update Value To Json    ${json_data}    $..attachment    ${attachment_id}
            ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
            ${resp}        POST On Session    hook    /v1/testruns/${test_run_id}/attachments    json=${updated_file_data}     headers=${headers}
            Should Be Equal As Strings  ${resp.status_code}     201
        END
    END

Get Projects
    Get authToken by Password Authentication
    Create Session And Headers
    ${resp}        GET On Session    hook    url=/v1/projects    headers=${headers}  data=${EMPTY}
    Should Be Equal As Strings  ${resp.status_code}     200
    ${body}    Evaluate    json.loads('''${resp.content}''')    json
    ${project_names}    Get Value From Json     ${body}     $..name

Create Multi Part
    [Arguments]    ${addTo}    ${partName}     ${filePath}  ${contentType}    ${content}=${None}
    ${fileData}=    Run Keyword If    '''${content}'''!='''${None}'''   Set Variable    ${content}
    ...            ELSE    Get Binary File    ${filePath}
    ${fileDir}    ${fileName}=    Split Path    ${filePath}
    ${partData}=    Create List    ${fileName}    ${fileData}    ${contentType}
    Set To Dictionary    ${addTo}    ${partName}=${partData}

Patch Update Test Runs
    [Arguments]     ${status}   ${result_text}
    FOR    ${test_run_id}    IN     @{test_run_ids}
        ${json_data}    Load JSON From File     ${EXECDIR}${/}jama${/}test_run_status_payload.json
        ${updated_file_data}    Update Value To Json    ${json_data}    $..status    ${status}
        ${updated_file_data}    Update Value To Json    ${updated_file_data}    $..result    ${result_text}
        ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
        ${resp}        PUT On Session    hook    /v1/testruns/${test_run_id}    json=${updated_file_data}     headers=${headers}
        # Commenting the keyword below, otherwise fill fail if extra steps in test case
        #Should Be Equal As Strings  ${resp.status_code}     200
    END
