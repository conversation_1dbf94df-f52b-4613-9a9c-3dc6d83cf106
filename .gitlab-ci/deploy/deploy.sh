#!/bin/sh
set -ex
AWS_ECR="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

aws ecr get-login-password --region "${AWS_REGION}" \
| docker login --username AWS --password-stdin "$AWS_ECR"

date=$(date +%Y%m%d-%H%M%S)
tag1="${AWS_ECR}/noona-test:${CI_COMMIT_REF_NAME}.${CI_PIPELINE_ID}"
tag2="${AWS_ECR}/noona-test:${date}"
tag3="${AWS_ECR}/noona-test:${CI_COMMIT_REF_NAME}.latest"
tag4="${AWS_ECR}/noona-test:latest"

docker build --network host --tag "${tag1}" --tag "${tag2}" --tag "${tag3}" --tag "${tag4}" .
docker push "${tag1}"
docker push "${tag2}"
docker push "${tag3}"
docker push "${tag4}"
