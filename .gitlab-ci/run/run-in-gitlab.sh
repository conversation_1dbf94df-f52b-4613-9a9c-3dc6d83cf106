#!/bin/sh

set -e

apk update -q
apk add --no-cache python3 build-base libffi-dev openssl-dev python3-dev py3-pip curl
pip install pip==24
## latest pyyaml has issues with wheel and python version in use, remove specific version when issue is gone
## can only be found out by testing without the specific version and pipeline run
pip install awscli docker-compose pyyaml==5.3.1 urllib3
pip install docker==7.1.0
pip freeze && python3 -V

aws --version
docker-compose --version
docker info

AWS_ECR="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

aws ecr get-login-password --region "${AWS_REGION}" \
| docker login --username AWS --password-stdin "$AWS_ECR"

mkdir output
RUN_PATH=$(pwd)

# Edit the .env file to include the working directory (RUN_PATH) for docker volume use.
# Also inject INCLUDE_TAGS, EXCLUDE_TAGS and ENVIRONMENT variables in it to choose which tests are run
# and against which environment.

sed -i "s|RUN_PATH=.*|RUN_PATH=$RUN_PATH|" .env
sed -i "s|DC_NOONA_TEST_IMAGE=.*|DC_NOONA_TEST_IMAGE=${AWS_ECR}/noona-test:${CI_COMMIT_REF_NAME}.latest|" .env
sed -i "s|INCLUDE_TAGS=.*|INCLUDE_TAGS=$INCLUDE_TAGS|" .env
sed -i "s|EXCLUDE_TAGS=.*|EXCLUDE_TAGS=$EXCLUDE_TAGS|" .env
sed -i "s|ENVIRONMENT=.*|ENVIRONMENT=$ENVIRONMENT|" .env
cat .env

echo
echo "Got job ID: $CI_JOB_ID"
echo "Got job Name: $CI_JOB_NAME"

docker-compose pull -q
docker images
docker-compose up --abort-on-container-exit
until docker-compose ps --services --all --filter status=exited | grep noona-test; do
  echo "Still running"
  >&2 sleep 1
done
docker-compose logs noona-test
docker-compose logs noona-test >noona-test.logs
docker-compose down

export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev

echo "Downloading test reports from S3..."
OUTPUT_DIR=$(cat noona-test.logs | grep "outputdir output" | awk '{print $4}' | head -1)
if [[ "$NIGHTLY_RUN" == "true" ]]; then
  S3_DIR=s3://noona-test-reports/web/$OUTPUT_DIR
elif [[ "$DAILY_RUN" == "true" ]]; then
  S3_DIR=s3://noona-test-reports/daily_run/web/$OUTPUT_DIR
else
  S3_DIR=s3://noona-test-reports/others/web/$OUTPUT_DIR
fi
echo "Output directory: $OUTPUT_DIR"

echo "Reports stored in S3 at $S3_DIR"
if [[ "$NIGHTLY_RUN" == "true" ]]; then
  aws s3 --region eu-west-1 cp s3://noona-test-reports/web/$OUTPUT_DIR ./output/ --recursive 2>&1 > /dev/null
elif [[ "$DAILY_RUN" == "true" ]]; then
  aws s3 --region eu-west-1 cp s3://noona-test-reports/daily_run/web/$OUTPUT_DIR ./output/ --recursive 2>&1 > /dev/null
else
  aws s3 --region eu-west-1 cp s3://noona-test-reports/others/web/$OUTPUT_DIR ./output/ --recursive 2>&1 > /dev/null
fi

FAILED=$(cat noona-test.logs | grep -o "Smoke tests failed" | tail -1)
if [[ "$FAILED" == "Smoke tests failed" ]]; then
  echo "$FAILED exit 1"
  exit 1
fi