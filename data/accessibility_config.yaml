# Accessibility testing configuration

# Global settings
ACCESSIBILITY_ENABLED: True
WCAG_LEVEL: AA
FAIL_ON_VIOLATIONS: True
ACCESSIBILITY_REPORT_DIR: output/accessibility_reports

# Severity thresholds
SEVERITY_THRESHOLD: minor  # minor, moderate, serious, critical

# Rules to disable (if needed for legacy compatibility)
DISABLED_RULES:
  - color-contrast  # Disable if design system not ready
  - landmark-one-main  # Disable for single-page apps

# Custom rule configurations
CUSTOM_RULES:
  color-contrast:
    enabled: True
    options:
      noScroll: True
  keyboard-navigation:
    enabled: True
  focus-management:
    enabled: True

# Environment-specific settings
ENVIRONMENTS:
  local:
    FAIL_ON_VIOLATIONS: False  # Don't fail in local development
    WCAG_LEVEL: A
  test:
    FAIL_ON_VIOLATIONS: True
    WCAG_LEVEL: AA
  staging:
    FAIL_ON_VIOLATIONS: True
    WCAG_LEVEL: AA
  production:
    FAIL_ON_VIOLATIONS: True
    WCAG_LEVEL: AAA

# Page-specific configurations
PAGE_CONFIGS:
  login_pages:
    rules:
      - label
      - form-field-multiple-labels
      - color-contrast
  dashboard_pages:
    rules:
      - heading-order
      - landmark-one-main
      - color-contrast
  form_pages:
    rules:
      - label
      - aria-required-attr
      - color-contrast
      - keyboard-navigation

# Browser-specific settings
BROWSER_CONFIGS:
  chrome:
    accessibility_extensions: []
  firefox:
    accessibility_extensions: []
  safari:
    accessibility_extensions: []
