#TA clinic Native_App
f07u02app_patient_main:
  ssn: 280378-587J
  name: f07u02app patient_main
  email: <EMAIL>
f01p08_patient:
  ssn: N38593431
  email: <EMAIL>
f01p01s09_patient:
  ssn: N11423511
  email: <EMAIL>
f02p01_f07p11_android:
  ssn: N15175781
  email: <EMAIL>
f02p01_f07p11_ios:
  ssn: N57687899
  email: <EMAIL>
f01p04_patient:
  ssn: N76032747
  email: <EMAIL>
f01p07_patient:
  ssn: N22607416
  email: <EMAIL>
f01p07_patient_exta:
  ssn: N30291143
  email: <EMAIL>
f01p13_patient:    # the patient always has a baseline questionnaire scheduled in a year, re-send the questionnaire after scheduled date has passed.
  ssn: N14602000
  email: <EMAIL>
f01p01_patient_male:
  ssn: N16002877
  email: <EMAIL>
f01p01_patient_female:
  ssn: N72440196
  email: <EMAIL>
f07p10_a_patient:
  ssn: N64884722
  email: <EMAIL>
f07p10_c_patient:
  ssn: N10388176
  email: <EMAIL>
f01p01_stateofhealth_app:
  ssn: N1987931
  email: <EMAIL>
f07u05_patient:
  ssn: N6787989
  email: <EMAIL>
f07p12_patient_android:
  ssn: N5656761
  email: <EMAIL>
f07p12_patient_ios:
  ssn: N5656762
  email: <EMAIL>
f14p02_patient_main:
  ssn: N47387209
  email: <EMAIL>
f14p04_extb:
  ssn: 200669-230X
  email: <EMAIL>
f07u01_patient:
  ssn: N8373636
  email: <EMAIL>
f07u01_app_delegate:
  email: <EMAIL>
f02p05_patient_extb:
  ssn: N7867676
  name: F02p05 Test Reset
  email: <EMAIL>
  delegate: <EMAIL>
intake_service_patient_app:
  ssn: N186377
  name: Intake Service Patient App
  email: <EMAIL>
  delegate: <EMAIL>
f07p14_patient:
  mrn: 231223-1
  ssn: 050365-819U
  email: <EMAIL>
f07p14_patient_exta:
  ssn: N56434343
  email: <EMAIL>

#TA clinic Patient_Education
f14p04_re_doc:
  ssn: RMV-001
  email: <EMAIL>
f13p02_1_patient:
  ssn: N47311565
  name: F13P02-1 gIyisle
  email: <EMAIL>
f14p04_patient_2:
  ssn: PE-001
  name: Paiva Karhunen
  email: <EMAIL>
  delegate: <EMAIL>
f13p02_patient:
  ssn: 004-003-004-02
  name: Mario Gomez
  email: <EMAIL>


#TA clinic Automated_tests
expired_password_patient_email:
  ssn: N91506185
  email: <EMAIL>
f14p01_patient1:
  ssn: N06823560
  name: F14p01 Test
  email: <EMAIL>
f07p13_patient:
  ssn: 170662-390C
  email: <EMAIL>
f03n01_patient_native:
  ssn: N7454859
  name: Bianca 2 Winner
  email: <EMAIL>
f07u08_labelling:
  name: Product Labelling
  ssn: PL0000001
  email: <EMAIL>
f01p01s18_patient:
  ssn: N47669190
  name: Breastcancer Surgery Follow up
  email: <EMAIL>
f07p01_patient:
  ssn: 1lhbzcY3
  email: <EMAIL>
f07p01_spanish:
  ssn: peTU8jac
  email: <EMAIL>
f07p16_cookie_setting:
  ssn: Y262626626
  name: f07p16 cookie
  email: <EMAIL>
f07u05_ext_e_patient:
   ssn: ***********
   name: F07U05 Exte
   email: <EMAIL>
   delegate: <EMAIL>

#TA clinic Appointment_Clinic
f13p02_patient_1:  #disabled clinical reports under Appointment Clinic
  ssn: lmtAUYyv
  name: f13p02 test
  email: <EMAIL>
f12sy07_patient_1:
  mrn: ZMpamF0u
  name: f12sy07 LbNgiZGG
  email: <EMAIL>
  id_staging: c2b87589-17ec-4170-b550-a0169d511a6a
  id_test: cd216f35-edc3-45bb-92dc-22dc8c1f7fe9
f12sy07_patient_2:
  mrn: kkEEdSD5
  ssn: A217HlNZ
  name: f12sy07 Patient 2
  email: <EMAIL>
  id_staging: 15876cdc-1cc7-455f-ab2b-ffcc5e489856
  id_test: 8279200c-4a6b-4a69-bce9-06a43133dc0b
f12sy07_patient_3:
  mrn: McBfUbze
  ssn: NXLHGHAJ
  name: f12sy07 Patient 3
  email: <EMAIL>
  id_staging: cfa9a9b2-6fc4-4cbb-9582-9e8f5ca57905
  id_test: 96bf5be2-3194-4dbf-96ce-4423ef2e5766
f12sy07_patient_5:
  mrn: 9Qt803NW
  ssn: eo8baM9i
  name: f12sy07 Patient 5
  email: <EMAIL>
  id_test: 131d8a54-c122-47cc-aa79-0f50ce6b2d86
  id_staging: 97f78368-599a-470e-9da7-4f2a99516282
f12sy07_patient_4:
  mrn: VjvnW6EF
  ssn: oSPqJezY
  name: f12sy07 Patient 4
  email: <EMAIL>
  id_test: 38588eef-0f75-4cbe-b5fa-853d6161f26a
  id_staging: 3af94451-bffa-4090-9e44-eca85fe06a6f
f13p02_1_patient_disabled:
  ssn: N47311566
  id_test: c558c341-3c6e-456c-b6dc-3245ab2b58af
  id_staging: e67d120a-fc34-4c3d-83b7-a42a649fd076
  name: F13P02-1 gIyisle
  email: <EMAIL>
f13p02_3_patient_1:
  name: f13p02-3 medication
  ssn: M6272888
  email: <EMAIL>

#TA clinic disabled clinic
f07u01_app_disabled_clinic:
  email: <EMAIL>
  ssn: N673637
  delegate: <EMAIL>
f07u01_app_active_disabled_clinic:
  email: <EMAIL>
  ssn: N673627

#TA clinic disabled clinic and disabled two clinic
f07u01_app_disabled_two_clinics:
  email: <EMAIL>
  ssn: N673639

#TA clinic Automated_tests_4
f01p01s18_extd:
  ssn: *********
  name: f01p01s18 extd
  email: <EMAIL>

#TA clinic Language Testing1
f07u11_finnish_patient1:
  ssn: 240124-1
  name: Finnish Default Patient1
  email: <EMAIL>
  delegate: <EMAIL>
f07u11_finnish_patient2:
  ssn: 070324-1
  name: Finnish Default Patient2
  email: <EMAIL>
  delegate: <EMAIL>

#TA clinic Automated_tests_3
f01p01s18_extb:
  ssn: R95467813
  name: f01p01s18 extb
  email: <EMAIL>

#TA SPMC
f07p14_unapproved_unapproved:
  ssn: N8980909
  name: f07p14 unapproved unapproved
  email: <EMAIL>

# New Integration Project Test Clinic
f13p02_3_patient_2:
  name: f13p02-3 TA1
  ssn: H52626662
  email: <EMAIL>

f13p02_3_patient_3:
  name: f13p02-3 TA2
  ssn: H52626636
  email: <EMAIL>

f13p02_3_patient_4:
  name: f13p02-3 TA3
  ssn: H526266541
  email: <EMAIL>

#Mailosaur-SMS
f07u02app_patient_extD:
    ssn: ***********
    email: <EMAIL>  #number should be +*************
    name: f07u02app patient_extd

# TA clinic Language Testing2
f07u11_finnish_spmc1: # this patient has also an account in TA clinic Language Testing1
  ssn: 110424-1
  name: Finnish SPMC1
  email: <EMAIL>