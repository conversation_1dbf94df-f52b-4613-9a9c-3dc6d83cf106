# TA clinic Patient_Education
patient_education_clinic:
  name: TA clinic Patient_Education
  tunit: unitTest
  user_email: <EMAIL>
  manager_email: <EMAIL>
  f01p08_tunit: unitf01p08
  f01p08_care_team: f01p08 care team
  care_team_1: Care Team 1
# TA clinic SPMC_Consent_A - spmc patients with consent
spmc_consent_a_clinic:
  name: TA clinic SPMC_Consent_A
  manager_email: <EMAIL>
  tunit: unitTest
# TA clinic SPMC_Consent_B - spmc patients with consent
spmc_consent_b_clinic:
  name: TA clinic SPMC_Consent_B
  manager_email: <EMAIL>
  tunit: unitTestB
  id_test: c0833ee4-ed30-41ac-b6e3-dbf3602c827a
  id_staging: 8c13c60c-4166-4ca7-b28b-c5152584f76d
# No Multiple clinics-patient groups
automated_tests_4_clinic:
  name: TA clinic Automated_tests_4
  user_email: <EMAIL>
  manager_email: <EMAIL>
  nurse_email: <EMAIL>
  auto4_manager: <EMAIL>
  admin_email: <EMAIL>
  tunit: unitTest
# TA clinic SPMC_Clinic_A - spmc patients without consent
spmc_clinic_a:
  name: TA clinic SPMC_Clinic_A
  manager_email: <EMAIL>
  spmca_ehr_token_test: VsVcoe071iCtg1GoXwsA6g==
  spmca_subscriber id_test: d3538c51-6d2e-4fae-84d1-811c49bd9892
# TA clinic SPMC_Clinic_B - spmc patients without consent
spmc_clinic_b:
  name: TA clinic SPMC_Clinic_B
  manager_email: <EMAIL>
  spmcb_ehr_token_test: QbgSTNSqAcqQvKzCd1jTxw==
  spmcb_subscriber id_test: 97db88c3-55ef-49c4-a41b-966bf495fea5
# TA clinic Appointment_Clinic - mainly appointment and integration setings related tests
appointment_clinic:
  name: TA clinic Appointment_Clinic
  user_email: <EMAIL>
  tunit: unitTest
  tunit1: 888
  f10n01_care_team: f10n01 care team
  f10n01_tunit: f10n01unit
  appointment_care_team: Appointment Care Team
  manager_email: <EMAIL>
# Test Custom Clinic's - updating custom branding settings
custom_branding_automated_test:
  name: Test Custom Clinic's
  manager_email: <EMAIL>
# TA clinic Custom_Branding_Enabled - custom branding is always enabled
custom_branding_enabled_clinic:
  name: TA clinic Custom_Branding_Enabled
  manager_email: <EMAIL>
  custom_clinic_name: Custom Branding Enabled
# TA clinic Native_App - mostly patients used in native app tests
native_app_automated_tests:
  name: TA clinic Native_App
  tunit: unitTest
  manager_email: <EMAIL>
  f01p05app_user_email: <EMAIL>
# TA clinic Test_Clinic_Setting_1 - updates clinics settings
test_clinic_setting_1:
  name: TA clinic Test_Clinic_Setting_1
  tunit: unitTest
  user_email: <EMAIL>
  f03ca01_user_name:  F03ca01 User
  f03ca01_user_email:  <EMAIL>
  manager_email: <EMAIL>
  administrator_email: <EMAIL>
  clinic_setting_user_email: <EMAIL>
# TA clinic Automated_tests - main clinic with most patients
automated_tests_clinic:
  name: TA clinic Automated_tests
  tunit: unitTest
  tunit_f03n04: unitf03n04
  f01n16_nurse_1: <EMAIL>
  f01n16_nurse_2: <EMAIL>
  f01n16_nurse_3: <EMAIL>
  f01n14_nurse_1: <EMAIL>
  f01n14_nurse_consultant: <EMAIL>
  wq_user: <EMAIL>
  f07cu02_user: <EMAIL>
  f07cu02_name: f07cu02 clinicuser1
  f01n18_user: <EMAIL>
  f04cu01_nurse_email: <EMAIL>
  f04cu01_new_email: <EMAIL>
  f04cu01_fname: F04cu01
  f04cu01_lname: Nurse
  f04cu01_manager_email: <EMAIL>
  f04cu01_manager_new_email: <EMAIL>
  f04cu01_manager_fname: F04cu01
  f04cu01_manager_lname: Manager
  f04cu01_manager_new_fname: TEST
  f04cu01_manager_new_lname: F04CU01
  f07u01_2fa_user: <EMAIL>
  f07u01_2fa_user2: <EMAIL>  #2fa should be enabled +3584573966771 (under Mailosaur clinic in staging)
  f07u01_2fa_user3: <EMAIL>  #2fa should be enabled +3584573966771 (under Mailosaur clinic in staging)
  f01p01s18_tunit: unitf01p01s18
  f13na01_2fa_manager: <EMAIL>    #2fa enabled +3584573966771, has data export requesting right
  default_manager: <EMAIL>
  clinic_admin: <EMAIL>
  default_user: <EMAIL>
  default_user_name: Clinic User
  f01p02_tunit: unitf01p02
  f03n01_care_team: f03n01 care team
  f03n01_native_app_team: f03n01 native app team
  f03n01_native_app_id_test: d4645505-46d1-406e-99a7-78ec4903fe9b  #care team: f03n01 native app team
  f03n01_native_app_id_staging: 9c284bc8-6633-419e-bc65-7cc57ab44b8a  #care team: f03n01 native app team
  f01n09_tunit: unitf01n09
  f01n09_bc_care_team: f01n09-bc care team
  f05p01_tunit: unitf05p01
  f05p01_care_team: f05p01 care team
  f01n13_tunit: unitf01n13
  f01n13_care_team: Provider Filter Careteam
  f10n01_care_team: f10n01 care team
  f01n13_care_team_main: f01n13 care team
  f01p03_care_team: f01p03 care team
  f01p01s18_care_team: f01p01s18 care team
  single_wq_user_email: <EMAIL>
  single_wq_user_name: Single Care team User
  f15cm01_manager: <EMAIL>
  f13cu02_manager: <EMAIL>
  f01n14_care_team: f01n14 care team
  f01n01_care_team: f01n01 care team
  f01n08_manager: <EMAIL>
# TA clinic disabled - disabled clinic
disabled clinic:
  name: TA clinic disabled
  nurse: <EMAIL>
  manager: <EMAIL>
# TA clinic disabled two - disabled clinic used in spmc tests
disabled clinic two:
  name: TA clinic disabled two
  nurse: <EMAIL>
# Pendo Test Clinic - clinic with pendo enabled
pendo_test_clinic:
  name: Pendo Test Clinic
  manager_email: <EMAIL>
  user_email: <EMAIL>
  user2_email: <EMAIL>
  tunit: unitTest
# Mailosaur - SMS - has patients with 2fa enabled (note: can be combined with others)
mailosaur_sms:
  name: Mailosaur - SMS
  nurse_email: <EMAIL>
  nurse_name: Mailosaur Clinic B User
  f04cu01_user2_email: <EMAIL>  #number should be +3584573966771
  f07u02_user_email: <EMAIL>  #number should be +3584573966771
# Time Constraint Testing - use for time constraint tests, usually for data prep
time_constraint_testing:
  name: Time Constraint Testing
  manager_email: <EMAIL>
# Questionnaires Test clinic - use for medical content TA
questionnaires_test_clinic:
  name: TA clinic Questionnaires_Test
  manager_email: <EMAIL>
# TA clinic Language Testing1 - updating clinic's language
language_testing_clinic:
  name: TA clinic Language Testing1
  manager_emai: <EMAIL>
  nurse_email: <EMAIL>
  id_test: c4b88427-b1ec-493f-b621-29ddf7bfad8b
# TA clinic Language Testing2 - updating clinic's language
language_testing_secondary_clinic:
  name: TA clinic Language Testing2
  manager_email: <EMAIL>
  nurse_emaiL: <EMAIL>
  id_test: 63f1e1b5-99da-4f06-8c60-b8cd0f6069af
# Medication Test Clinic - setup with medication integration
medication_test_clinic:
  name: New Integration Project Test Clinic
  manager_email: <EMAIL>
# Pre-Treatment Clinic - settings without symptom reporting
pre_treatment_clinic:
  name: TA clinic Pre_Treatment
  manager_email: <EMAIL>
# TA 2fa Enabled - 2fa in enabled in clinic settings by default
2fa_enabled_clinic:
  name: TA clinic 2fa Enabled
  manager_email: <EMAIL>
  user_email: <EMAIL>
# TA clinic All Settings - clinic settings are updated
test_clinic_setting_2:
  name: TA clinic Test_Clinic_Setting_2
  user_email: <EMAIL>
# TA clinic Test_Clinic_Setting_3 - clinic settings are updated
test_clinic_setting_3:
  name: TA clinic Test_Clinic_Setting_3
  manager_email: <EMAIL>
  manager_name: Settings Nurse
  clinic_user_email: <EMAIL>
# TA clinic Test_Clinic_Setting_4 - clinic settings are updated
test_clinic_setting_4:
  name: TA clinic Test_Clinic_Setting_4
  user_email: <EMAIL>
# TA clinic Test_Clinic_Setting_5 - clinic 2fa is enabled/disabled
test_clinic_setting_5:
  name: TA clinic Test_Clinic_Setting_5
  user_email: <EMAIL>
# TA clinic F13CU03-6
f13cu03-6_clinic:
  name: TA clinic F13CU03-6
  user1_email: <EMAIL>
  user2_email: <EMAIL>
# TA clinic Quiet Hours Set - Quiet hours are changing to test notifications
ta_clinic_quiet_hours_set:
  name: TA clinic Quiet Hours Set
  f07p08_manager_email: <EMAIL>
#TA Clinic Users_Test
ta_clinic_users_test:
  name: TA Clinic Users_Test
#TA clinic Automated_tests OIDC on
automated_tests_clinic_oidc_on:
  name: TA clinic Automated_tests OIDC ON
  clinic_user_name: Clinic User
  clinic_manager_name: Clinic Manager
#TA clinic Password Expiry
ta_pasword_expiry:
  name: TA clinic Password_Expiry
  clinic_user_email: <EMAIL>
  clinic_manager: <EMAIL>