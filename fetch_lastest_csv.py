### Documenation: This file will be used to download latest questionnaire csvs from dev repository .
# ...   And after renaming correctly paste in Test repository.
# ...   This way Test Automation script will use latest csv to compare against UI.
# ...   To run use command: python3 Fetch_lastest_csv.py

import os
import pandas as pd
import shutil

#Delete all files under data/questionnaires path
print("starting to delete files from data/questionnaires")
dir = 'data/test_questionnaires'
for f in os.listdir(dir):
    os.remove(os.path.join(dir, f))
print("deleting files from data/questionnaires is completed")

# #Todo: Download all csv files from gitlab to temp folder. Right now manually download in data/questionnaire/temp folder

#Copy all CSV files from temp folder to data/questionnaires folder
#Providing the folder path
print("starting to copy all csv files from tempt folder to data/questionnaires")
origin = "data/test_questionnaires_temp/"
target = "data/test_questionnaires/"
# Fetching the list of all the files
files = os.listdir(origin)
# Fetching all the files to directory
for file_name in files:
   shutil.copy(origin+file_name, target+file_name)
print("Files are copied successfully")

# Delete all files under data/questionnaires/temp including temp folder
print("Deleting temp folder")
dir = 'data/test_questionnaires_temp'
shutil.rmtree(dir)
print("temp folder deleted")

# Capture questionnaire name from CSV column and rename to csv file
print("Staring to rename csv files as per script")
for filename in os.listdir('data/test_questionnaires/'):
    df = pd.read_csv("data/test_questionnaires/"+filename, usecols = ['en_GB'], sep=';')
    #print (df.keys())
    quest_name_from_csv = (str(df.en_GB[0]) + ".csv")
    #print(quest_name_from_csv)
    #print ("data/questionnaires/"+str(quest_name_from_csv)+".csv")
    #print ("data/questionnaires/"+str(filename))
    # if (str(quest_name_from_csv)+".csv") == str(filename):
    #     print (str(quest_name_from_csv)+" Same")
    # else:
    os.rename("data/test_questionnaires/" + filename, "data/test_questionnaires/" + quest_name_from_csv)
    print("data/test_questionnaires/" + quest_name_from_csv + " Renamed")
print("Renaming csv files as per script is completed")
