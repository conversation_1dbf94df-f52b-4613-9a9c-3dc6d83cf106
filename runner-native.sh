#!/bin/sh

# This is common robot runner script used by both CI and local runner scripts

source ./common-utils.sh

timestamp="$(date +%Y%m%d-%H%M%S)"
SLACK_WEBHOOK="*******************************************************************************"
RDR_SLACK_WEBHOOK="*******************************************************************************"
ENVIRONMENT_ARG='native-test'
#Variables below are used only when running locally, uncomment if needed
#DEVICE_NAME='iPhone 13'
#APP='cf4fd604fc9fce423c030f7f239e1f0563ca4122'
#PLATFORM_NAME='ios'
#PLATFORM_VERSION='15'
#ENABLE_PASSCODE='true'
#OIDC_LOGIN='true'

# function to run robot test cases
run_test_cases() {
  pabot --pabotlib \
  --processes "$CONCURRENCY" \
  --outputdir "${outputdir}" \
  --report final_report \
  --argumentfile arguments_native.txt \
  --variable TEST_TIMEOUT:30m \
  "$@" \
  --include "$INCLUDE_TAGS" \
  --exclude "$EXCLUDE_TAGS" \
  --variablefile data/vars.yaml \
  --variablefile data/patients/native-app.yaml \
  --variablefile data/vars-$ENVIRONMENT_ARG.yaml \
  --variablefile data/clinics/clinics.yaml \
  --variablefile data/symptoms/symptom_types.yaml \
  --variablefile data/modules/modules.yaml \
  --variablefile data/questionnaires/questionnaire_types/questionnaire_types.yaml \
  --variable DEVICE_NAME:"${DEVICE_NAME}" \
  --variable APP:"${APP}" \
  --variable PLATFORM_NAME:"${PLATFORM_NAME}" \
  --variable PLATFORM_VERSION:"${PLATFORM_VERSION}" \
  --variable ENABLE_PASSCODE:"${ENABLE_PASSCODE}" \
  $(if [ "$DEBUG" ]; then echo "$DEBUG_VARIABLES"; else echo ""; fi) \
  --xunit xunit \
  test

  retval=$?
  return $retval
}

# function to run failed robot test cases again
rerun_failed_cases() {
  pabot --pabotlib \
  --processes "$CONCURRENCY" \
  --rerunfailed "${outputdir}"/output.xml \
  --outputdir "${outputdir}" \
  --output output_rerun.xml \
  --argumentfile arguments_native.txt \
  --variable TEST_TIMEOUT:30m \
  --timestamp \
  --variablefile data/vars.yaml \
  --variablefile data/patients/native-app.yaml \
  --variablefile data/vars-$ENVIRONMENT_ARG.yaml \
  --variablefile data/clinics/clinics.yaml \
  --variablefile data/symptoms/symptom_types.yaml \
  --variablefile data/modules/modules.yaml \
  --variablefile data/questionnaires/questionnaire_types/questionnaire_types.yaml \
  --variable DEVICE_NAME:"${DEVICE_NAME}" \
  --variable APP:"${APP}" \
  --variable PLATFORM_NAME:"${PLATFORM_NAME}" \
  --variable PLATFORM_VERSION:"${PLATFORM_VERSION}" \
  --variable ENABLE_PASSCODE:"${ENABLE_PASSCODE}" \
  test

  retval=$?
  return $retval
}

# function to combine two robot run results
combine_robot_runs() {
  rebot \
  --flattenkeywords for \
  --flattenkeywords while \
  --outputdir "${outputdir}" \
  --report final_report \
  --output output.xml \
  --xunit xunit \
  --merge "${outputdir}"/output.xml "${outputdir}"/output_rerun.xml
  echo "Reports merged."
}

# function to send a success message to slack channel
post_about_success_to_slack() {
  if  [[ "$RUN_IN_NATIVE" == "true" ]] || [[ "$RUN_ANDROID" == "true" ]] || [[ "$RUN_IOS" == "true" ]] ; then
    if [[ "$NIGHTLY_RUN" == "true" ]] ; then
      curl -X POST -H 'Content-type: application/json' --data '{"text":":large_green_circle::iphone: Native app tests for '"${PLATFORM_NAME}"' '"${PLATFORM_VERSION}"' passed in '"${ENVIRONMENT_ARG}"'
      Click the link below to see the report:
      https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'/artifacts/'"$outputdir"'/final_report.html
      To download, go to the link below and click Download:
      https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'
      "}' $SLACK_WEBHOOK
    elif [[ "$NIGHTLY_RUN" != "true" ]] ; then
      curl -X POST -H 'Content-type: application/json' --data '{"text":":large_green_circle::iphone: Native app tests for '"${PLATFORM_NAME}"' '"${PLATFORM_VERSION}"' passed in '"${ENVIRONMENT_ARG}"'
      Click the link below to see the report:
      https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'/artifacts/'"$outputdir"'/final_report.html
      To download, go to the link below and click Download:
      https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'
      "}' $SLACK_WEBHOOK
    fi
  fi
}

# function to send a warning message to various slack channel in case of failed test run
post_about_error_to_slack() {
  if  [[ "$RUN_IN_NATIVE" == "true" ]] || [[ "$RUN_ANDROID" == "true" ]] || [[ "$RUN_IOS" == "true" ]] ; then
    if [[ "$NIGHTLY_RUN" == "true" ]] ; then
      curl -X POST -H 'Content-type: application/json' --data '{"text":":warning::iphone: Some native app robot cases for '"${PLATFORM_NAME}"' '"${PLATFORM_VERSION}"' failed in '"${ENVIRONMENT_ARG}"'!
      Click the link below to see the report:
      https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'/artifacts/'"$outputdir"'/final_report.html
      To download, go to the link below and click Download:
      https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'
      "}' $SLACK_WEBHOOK
    elif [[ "$NIGHTLY_RUN" != "true" ]] ; then
      curl -X POST -H 'Content-type: application/json' --data '{"text":":warning::iphone: Some native app robot cases for '"${PLATFORM_NAME}"' '"${PLATFORM_VERSION}"' failed in '"${ENVIRONMENT_ARG}"'!
      Click the link below to see the report:
      https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'/artifacts/'"$outputdir"'/final_report.html
      To download, go to the link below and click Download:
      https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$CI_JOB_ID"'
      "}' $SLACK_WEBHOOK
    fi
  fi
}

if [[ "$CONCURRENCY" == "" ]]; then
  CONCURRENCY=5
  echo "Using default pabot concurrency of $CONCURRENCY threads"
fi

if [[ "$INCLUDE_TAGS" == "" ]]; then
  echo "Including every test suite in the test run."
else
  echo "Including test suites that match the tag \"$INCLUDE_TAGS\" in the test run."
fi

if [[ "$EXCLUDE_TAGS" == "" ]]; then
  echo "No test suites are being excluded."
else
  echo "Excluding test suites that match the tag \"$EXCLUDE_TAGS\" from the test run."
fi

[[ "$ENVIRONMENT" != "" ]] && ENVIRONMENT_ARG="$ENVIRONMENT"
echo "Using variables from the data/vars-$ENVIRONMENT_ARG.yaml file"

[[ "$outputdir" == "" ]] && outputdir="output/$ENVIRONMENT_ARG/$timestamp"
echo "outputdir $outputdir"
echo_relevant_envvars

run_test_cases "$@"
retval=$?
if [ "$retval" -ne 0 ] && [ "$ENVIRONMENT_ARG" != "local-patient" ];
then
  if ! rerun_failed_cases;
  then
    post_about_error_to_slack
  else
    post_about_success_to_slack
  fi
  combine_robot_runs
else
  if [ "$ENVIRONMENT_ARG" != "local-patient" ]
  then
    post_about_success_to_slack
  fi
fi

# Upload results to Amazon S3
#[[ "$RUN_IN_NATIVE" == "true" ]] && (aws s3 cp $outputdir s3://noona-test-reports/$outputdir --recursive || echo Failed to upload reports to S3)

if [[ "$NIGHTLY_RUN" == "true" ]]; then
  [[ "$RUN_IN_NATIVE" == "true" ]] || [[ "$RUN_ANDROID" == "true" ]] || [[ "$RUN_IOS" == "true" ]] && (aws s3 cp $outputdir s3://noona-test-reports/$PLATFORM_NAME/$outputdir --recursive || echo Failed to upload reports to S3)
else
  [[ "$RUN_IN_NATIVE" == "true" ]] || [[ "$RUN_ANDROID" == "true" ]] || [[ "$RUN_IOS" == "true" ]] && (aws s3 cp $outputdir s3://noona-test-reports/others/$PLATFORM_NAME/$outputdir --recursive || echo Failed to upload reports to S3)
fi

# for convenience a second log as gitlab cuts lenghty logs in preview
echo_relevant_envvars

echo Finished.
