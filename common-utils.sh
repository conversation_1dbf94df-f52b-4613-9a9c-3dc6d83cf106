#!/bin/sh

#################################################################
# collection of shared shell utils for various scrips


# Prints collection of interesting env vars, if not defined, will not output
# Avoiding to print full env for compact ordered output and not disclosing unwanted secrets
echo_relevant_envvars() {
  echo "-----------------------------------------------------"
  echo "Currently effective environment variables 💻"

  ENV_PARAM="ENVIRONMENT ENVIRONMENT_ARG JOB_ID INCLUDE_TAGS EXCLUDE_TAGS RUN_IN_GITLAB RUN_IN_GITLAB_2 RUN_PATH CONCURRENCY OUTPUT_DIR DEBUG_VARIABLES DEBUG DEVICE_NAME PLATFORM_NAME PLATFORM_VERSION ENABLE_PASSCODE RUN_IN_NATIVE"

  for val in ${ENV_PARAM}; do
    LINE=$(printenv |grep "$val")
    if [ -n "$LINE" ]; then
      echo "$LINE"
    fi
  done
  echo "-----------------------------------------------------\n"

}
