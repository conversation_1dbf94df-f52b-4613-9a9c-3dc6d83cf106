# MODULE QUESTION RETRIEVER
# BEFORE USE PLEASE CHANGE/CREATE FILE PATHS ACCORDING YOUR SPECIFIC FILE PATHS
# CREATES FILES FOR EACH MODULE AND PRINTS QUESTIONS IN THE MODULE PER SYMPTOM FORM
# CHANGE 'LANGUAGE_COLUMN' VALUE FOR DIFFERENT LANGUAGES

import os
import csv
from configparser import ConfigParser
import stringcase
import numpy
import io

# STATICS
# STATIC COLUMN NUMBERS IN CSV
SHOW_CONDITIONS = 9
STATIC_CONDITIONS = 10
#  FINNISH_COLUMN = 11
#  ENGLISH_COLUMN = 12, FOR EXAMPLE.
ENUMERATION_KEY = 1
FIELD_TYPE = 0
ID_COLUMN = 4
TYPE_COLUMN = 3
MAX = 5
MIN = 6
SEPARATOR_DUPLICATES = (
    "--------------------------------------------------------------------------------"
)
SEPARATOR = "------------------------------------------------------------------------------------------------------------------------"
SEPARATOR_START = "START FOR SECTION "
SEPARATOR_END = "END OF MODULE"
HEADER = "header:"
QUESTION = "question:"
ANSWER_OPTION = "answer option:"
actual_occurrences = 0  # FOR CHECKING PURPOSES
ln_simple = False
# html_spaces = "•<i>"
html_spaces = ""
# html_spaces_end = "</i>"
html_spaces_end = ""

# FILE PATHS --> CHANGE IF NEEDED
FILE_PATH_FOR_MODULES = "/Users/<USER>/noona-core/noona-content/src/main/resources/forms/csv/noona/cancer/"
FILE_PATH_FOR_MODULES_TWO = "/Users/<USER>/noona-core/noona-content/src/main/resources/forms/csv/noona/cancer/"
FILE_PATH_FOR_SYMPTOM_FORMS_TWO = "/username/cll1159/noona-core/noona-content/src/main/resources/forms/csv/noona/cancer/forms/"
FILE_PATH_FOR_SYMPTOM_FORMS = "/Users/<USER>/noona-core/noona-content/src/main/resources/forms/csv/noona/cancer/forms/"
FILE_PATH_FOR_OUTPUT = (
    "/Users/<USER>/modules/"
)
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES_FI = "/Users/<USER>/noona-core/noona-common/src/main/resources/symptom_names_fi_FI.properties"
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES = "/Users/<USER>/noona-core/noona-common/src/main/resources/symptom_names.properties"
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES_SV = "/Users/<USER>/noona-core/noona-common/src/main/resources/symptom_names_sv_FI.properties"
FILE_PATH_MESSAGES = "/Users/<USER>/noona-core/noona-common/src/main/resources/messages.properties"
forwarding_module_key = "nurse.managePatients.treatmentModule."
RULES = "/Users/<USER>/noona-core/noona-content/src/main/resources/forms/rules/"


def delete(path):
    for the_file in os.listdir(path):
        file_path = os.path.join(path, the_file)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(e)


def get_symptom_name_prop(FILE_PATH_FOR_SYMPTOM_NAME, symptom):
    forwarding_form_key = "fe.formNames."
    skip = False
    global displayed_symptom_name

    if "messages" in FILE_PATH_FOR_SYMPTOM_NAME:
        forwarding_form_key = ""
        symptom = symptom.replace("=", "")
        skip = True
    else:
        forwarding_form_key = forwarding_form_key

    with open(FILE_PATH_FOR_SYMPTOM_NAME) as f:
        file_cont = "[dummy_section]\n" + f.read()
    config_parser = ConfigParser(strict=False)
    config_parser.read_string(file_cont)
    if skip is False:
        symptom = symptom.replace(" ", "_").replace(",", "").lower()
        symptom = stringcase.camelcase(symptom)

    displayed_symptom_name = config_parser.get(
        "dummy_section", forwarding_form_key + symptom
    )
    try:
        if displayed_symptom_name != "":
            displayed_symptom_name = (
                str(displayed_symptom_name)
                .replace("\\u00E4", "ä")
                .replace("\\u00F6", "ö")
                .replace("\\u00C5", "Å")
                .replace("\\u00E5", "å")
                .replace("\\u00C4", "Ä")
                .replace("\\u00D6", "Ö")
            )
            return displayed_symptom_name
        if displayed_module_name == "":
            print(
                "Found empty translation for key "
                + (forwarding_form_key + symptom)
                + "."
            )
            displayed_symptom_name = "?" + (forwarding_form_key + symptom) + "?"
        return displayed_symptom_name
    except:
        print(
            "Couldn't retrieve symptom name for key "
            + (forwarding_form_key + symptom)
            + " from "
            + FILE_PATH_FOR_SYMPTOM_NAME
            + "."
        )
        displayed_symptom_name = forwarding_form_key + file
        return displayed_symptom_name


def get_ln():
    global LANGUAGE_COLUMN
    global ln_one
    global ln_two
    global ln_locations
    global ln_duplicates
    global ln_asked
    global ln_simple
    global locale
    global FILE_PATH_MESSAGES

    locales = ["", "_fi_FI", "_sv_FI"]

    determine = input(
        "What language? (11 for Finnish, 12 for English and 13 for Swedish.) "
    )
    if determine == "11":
        locale = locales[1]
        use_simplified = input("Use simplified projection (y/n)? ")
        if use_simplified.lower().strip(" ") == "y":
            ln_simple = True
    elif determine == "13":
        locale = locales[2]
        use_simplified = input("Use simplified projection (y/n)? ")
        if use_simplified.lower().strip(" ") == "y":
            ln_simple = True
    else:
        locale = locales[0]
        use_simplified = input("Use simplified projection (y/n)? ")
        if use_simplified.lower().strip(" ") == "y":
            ln_simple = True
    LANGUAGE_COLUMN = int(determine)

    # DETERMINE FINNISH OR ENGLISH
    if LANGUAGE_COLUMN == 11 or LANGUAGE_COLUMN == 13:
        ln_one = "Oirelomakkeessa "
        ln_two = " potilaalle näytetään:"
        ln_locations = "Määrittele kivun sijainti: "
        ln_duplicates = "Seuraavat esitetään käyttäjälle vähintään kahteen kertaan: "
        ln_asked = " kysytään oireissa "
    else:
        ln_one = "In the symptom form concerning "
        ln_two = " the patient is shown the following content: "
        ln_locations = "Pain locations in the body (Pain pointer):"
        ln_duplicates = "The following are straight duplicates in this module: "
        ln_asked = " is asked in symptom forms "

    print("Parsing...")

    FILE_PATH_MESSAGES = FILE_PATH_MESSAGES.replace(
        ".properties", (locale) + ".properties"
    )

    return (
        LANGUAGE_COLUMN,
        ln_one,
        ln_two,
        ln_locations,
        ln_duplicates,
        ln_asked,
        ln_simple,
        locale,
        FILE_PATH_MESSAGES,
    )


def determine_path():
    global FILE_PATH_FOR_MODULES, FILE_PATH_FOR_MODULES_TWO, FILE_PATH_FOR_SYMPTOM_FORMS, FILE_PATH_FOR_SYMPTOM_FORMS_TWO

    if os.path.isdir(FILE_PATH_FOR_SYMPTOM_FORMS_TWO) and len(
        os.listdir(FILE_PATH_FOR_SYMPTOM_FORMS_TWO)
    ) >= len(os.listdir(FILE_PATH_FOR_SYMPTOM_FORMS)):
        FILE_PATH_FOR_SYMPTOM_FORMS = FILE_PATH_FOR_SYMPTOM_FORMS_TWO
    else:
        FILE_PATH_FOR_SYMPTOM_FORMS = FILE_PATH_FOR_SYMPTOM_FORMS

    if os.path.isdir(FILE_PATH_FOR_MODULES_TWO) and len(
        os.listdir(FILE_PATH_FOR_MODULES_TWO)
    ) >= len(os.listdir(FILE_PATH_FOR_MODULES)):
        FILE_PATH_FOR_MODULES = FILE_PATH_FOR_MODULES_TWO
    else:
        FILE_PATH_FOR_MODULES = FILE_PATH_FOR_MODULES

    return FILE_PATH_FOR_MODULES, FILE_PATH_FOR_SYMPTOM_FORMS


def get_module_symptoms(file):
    global module_symptoms, actual_occurrences, FILE_PATH_FOR_MODULES, FILE_PATH_FOR_OUTPUT, f2
    # CLEAR OUT LIST FOR NEXT MODULE
    module_symptoms = []
    f2 = open(FILE_PATH_FOR_OUTPUT + file, "w+")
    FILE_PATH_FORM_MODULES = determine_path()[0]
    # OPEN MODULE SPECIFICATION FILE AND SEE LISTED SYMPTOM FORMS
    if (FILE_PATH_FOR_MODULES + file).endswith(".txt"):
        with open(FILE_PATH_FOR_MODULES + file) as f:
            for line in f:
                if "=" in line or line == "":
                    dummy = True
                else:
                    if line == "eating problems ":  # HARDCODED WORKAROUND
                        line = "eating problems"  # HARDCODED WORKAROUND
                    else:
                        line = line.replace("\n", "").replace(" ", "_")
                        if line.endswith("_"):
                            line = line[:-1]
                    module_symptoms.append(line)  # APPEND SYMPTOM FORMS INTO A LIST
                    module_symptoms = list(
                        filter(None, module_symptoms)
                    )  # REMOVE BLANKS
            actual_occurrences += len(
                module_symptoms
            )  # FOR CHECKING PURPOSES --> WE HAVE ALL THE SYMPTOM OCCURRENCES IN THE MODULES

        return actual_occurrences, module_symptoms


def get_show_id(row_list):
    global checklist
    global answerlist
    operands = [
        " contains any ",
        " over ",
        " is null ",
        " is not ",
        " is ",
        " does not contain any ",
    ]

    # returns id of a rows show condition if show condition is not empty
    if row_list.strip() != "":
        # if row_list == "prolonged pain intensity over 4":
        #    print("Hei.")
        for item in operands:
            if item in row_list:
                checklist = row_list[: row_list.index(item)].lower()
                answerlist = row_list[row_list.index(item) + len(item) :].lstrip()
                return checklist, answerlist
            else:
                continue
    else:
        return


def eval_show(show_condition, ids, enums, misses, missed_enums, questions, mock):
    show_condition = show_condition.lower()
    operands = [" contains any ", " over ", " is null "]

    parts = []
    conditions = []
    hits = 0
    bothHaveToBeTrue = False
    eitherHaveToBeTrue = False
    not_break = False

    if mock == "Please describe your skin rash":
        print("Jee")

    # TODO: AND OR SPLITTAUS LÖYTYYKÖ OPERANDI OSISTA!
    # TODO: EKA ID ENNEN EKAA OPERANDIA, SITTEN TSEKKI ONKO SIELLÄ LOPPUSTRINGISSÄ OPERANDEJA, JOS ON

    if " and " in show_condition:
        parts = show_condition.split(" and ")
        for part in parts:
            for operand in operands:
                if operand.strip() in part:
                    hits += 1
        if hits == len(parts):  # Eli jos on "oikea" && tilanne ehdon kanssa.
            bothHaveToBeTrue = True
            for p in parts:
                change = (
                    p.replace("is null", "' in misses")
                    .replace("contains any", "hörönplöö")
                    .replace("over", "hörönplöö")
                )
                if "' in misses" in change:
                    change = (
                        '"'
                        + change[: change.find("' in misses")].strip()
                        + '" in misses'
                    )
                i = parts.index(p)
                parts.remove(p)
                if change.find("hörönplöö") != -1:
                    changeid = p[0 : change.find("hörönplöö")].strip()
                    foundEnumsFromCond = (
                        change[change.find("hörönplöö") :]
                        .replace("hörönplöö", "")
                        .strip()
                        .split(",")
                    )
                    change = 'questions.get("' + changeid + '")'
                    foundEnumsFromDict = eval(change)
                    lenChange = 'len(questions.get("' + changeid + '"))'
                    if foundEnumsFromDict is None:  # Avainta ei löydy.
                        foundEnumsFromDict = []
                    change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                if (
                    change
                    == "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                ):
                    parts.insert(i, str(('"' + str(eval(change)) + '"')))
                else:
                    parts.insert(i, change)

            if bothHaveToBeTrue:
                for k in parts:
                    not_break = eval(k)
                    if not_break is False:
                        return False
                    else:
                        continue
                if parts[parts.index(k)] == parts[-1] and not_break:
                    return True

    elif " or " in show_condition:
        parts = show_condition.split(" or ")
        for part in parts:
            for operand in operands:
                if operand.strip() in part:
                    hits += 1
        if hits == len(parts):  # Eli jos on "oikea" OR tilanne ehdon kanssa.
            eitherHaveToBeTrue = True
            for p in parts:
                change = (
                    p.replace("is null", "' in misses")
                    .replace("contains any", "hörönplöö")
                    .replace("over", "hörönplöö")
                )
                if "' in misses" in change:
                    change = (
                        '"'
                        + change[: change.find("' in misses")].strip()
                        + '" in misses'
                    )
                i = parts.index(p)
                parts.remove(p)
                if change.find("hörönplöö") != -1:
                    changeid = p[0 : change.find("hörönplöö")].strip()
                    foundEnumsFromCond = (
                        change[change.find("hörönplöö") :]
                        .replace("hörönplöö", "")
                        .strip()
                        .split(",")
                    )
                    change = 'questions.get("' + changeid + '")'
                    foundEnumsFromDict = eval(change)
                    lenChange = 'len(questions.get("' + changeid + '"))'
                    if foundEnumsFromDict is None:  # Avainta ei löydy.
                        foundEnumsFromDict = []
                    change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                if (
                    change
                    == "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                ):
                    parts.insert(i, str(('"' + str(eval(change)) + '"')))
                else:
                    parts.insert(i, change)

            if eitherHaveToBeTrue:
                for k in parts:
                    try:
                        not_break = eval(k)
                        if not_break:
                            return True
                        else:
                            continue
                    except:
                        continue
                if parts[parts.index(k)] == parts[-1] and not_break is False:
                    return False

    if bothHaveToBeTrue is False or eitherHaveToBeTrue is False:
        change = (
            show_condition.replace("is null", "' in misses")
            .replace("contains any", "hörönplöö")
            .replace("over", "hörönplöö")
        )
        if "' in misses" in change:
            change = '"' + change[: change.find("' in misses")].strip() + '" in misses'
        if change.find("hörönplöö") != -1:
            changeid = show_condition[0 : change.find("hörönplöö")].strip()
            foundEnumsFromCond = (
                change[change.find("hörönplöö") :]
                .replace("hörönplöö", "")
                .strip()
                .split(", ")
            )
            change = 'questions.get("' + changeid + '")'
            foundEnumsFromDict = eval(change)
            lenChange = 'len(questions.get("' + changeid + '"))'
            if foundEnumsFromDict is None:  # Avainta ei löydy.
                return False
            pekka = len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond)))
            sari = len(foundEnumsFromDict)
            change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
            kirjoitetaan = eval(change)
            return kirjoitetaan


"""
    global printable
    these_ids = []
    these_enums = []
    these_misses = []
    nd = ""
    orrr = ""
    base_nd = ""
    if len(enums) != 0:
        base_nd = " and "
    if len(enums) > 1:
        orrr = " or "
    if " AND " in show_condition:
        nd = " and "



    for i in list(filter(None, ids)):
        if i in show_condition.lower() or i.capitalize() in show_condition:
            these_ids.append(i.lower())
            show_condition = show_condition.replace(i, "").strip()
    for k in list(filter(None, enums)):
        if k in show_condition.lower() or k.capitalize() in show_condition:
            these_enums.append(k.lower())
            show_condition = show_condition.replace(k, "").strip()
    for j in list(filter(None, misses)):
        if j in show_condition.lower() or j.capitalize() in show_condition:
            these_misses.append(j.lower())
            show_condition = show_condition.replace(j, "").strip()

    for item in operands:
        if "is null" in show_condition and these_ids == [] and these_misses != []:
            return True
        elif [i for i in ids if i == "locations"] and "contains any" in show_condition:
            return True
        elif show_condition == item:
            return True
        elif "contains any" in show_condition and len(these_enums) > 0:
            return True
        elif show_condition.startswith("over"):
            return True
        else:
            return False

    python_string = ",".join(merged_list).replace(",", "").strip()
    if python_string.endswith( "or"):
        python_string = python_string[:-len(" or")]
    if python_string.endswith(" and"):
        python_string = python_string[:-len(" and")]
    if python_string.endswith("and "):
        python_string = python_string[:-len("and ")]

    python_string = python_string.strip()
    evaluate = eval(python_string)

    return evaluate
"""


def get_questions():

    # GLOBAL VARIABLES
    global skip
    global file
    global text
    global FILE_PATH_FOR_SYMPTOM_FORMS
    global FILE_PATH_FOR_MODULES
    global questions_shown
    global shown_enums
    global not_shown_questions
    global questions

    #  LANGUAGE CHECK
    get_ln()

    #  DETERMINE PATH, MIGHT BE OBSOLETE
    FILE_PATH_FOR_MODULES = determine_path()[0]

    FILE_PATH_FOR_SYMPTOM_FORMS = determine_path()[1]

    # ID:S FOR MISSES AS A LIST
    # QUESTIONS THAT ARE BEING SHOWN PER SYMPTOM --> USED FOR TOGGLING WHAT ANSWER OPTIONS WE SHOW
    questions_shown = []
    # DICTIONARY FOR CHECKING IF STRAIGHT DUPLICATES ARE FOUND
    all_questions = {}
    # MISSING QUESTIONS AS A DICTIONARY TO LET KNOW WHAT LANGUAGE(S) ARE MISSING

    # DELETE FILES FROM OUTPUT
    delete(FILE_PATH_FOR_OUTPUT)

    numbers = 0  # FOR CHECKING PURPOSES
    numbers_two = 0
    errors = False  # FOR CHECKING PURPOSES
    add = 0  # IDEA FOR PDF
    question = []

    # "The" Script
    for file in os.listdir(FILE_PATH_FOR_MODULES):

        # IN EACH ITERATION GET MODULE'S SYMPTOMS
        get_module_symptoms(file)

        # file = the module name, symptom_name = symptom form file name
        for symptom_name in module_symptoms:
            file_match = (
                file.replace("active-treatment", "")
                .replace("recovery_", "")
                .replace("-", " ")
                .replace(".txt", "")
            )
            if file_match.startswith("recovery"):
                file_match = file_match[len("recovery") :]
            if file_match.startswith("_"):
                file_match = file_match[len("_") :]
                file_match = file_match.replace("_", " ").lower()
            # handle_file = exact file name with directory for symptom form file name
            handle_file = (
                FILE_PATH_FOR_SYMPTOM_FORMS + symptom_name + ".csv"
            )  # FULL FILE PATH

            try:

                with open(handle_file) as handle_file_open:

                    ö = 0
                    if symptom_name != "":
                        symptom_name = symptom_name.replace("_", " ").capitalize() + ","
                        numbers += (
                            1
                        )  # AMOUNT OF ACTUAL ITERATIONS SHOULD EQUAL WITH actual_occurrences
                    questions_shown = []  # EMPTY THE LIST AFTER EVERY FILE ITERATION
                    questions = {}
                    shown_enums = []  # EMPTY THE LIST AFTER EVERY FILE ITERATION
                    not_shown_questions = []
                    missed_enums = []
                    went = False
                    id_for_show_condition = (
                        []
                    )  # EMPTY THE LIST AFTER EVERY FILE ITERATION
                    if numbers >= 1 and ln_simple:
                        add += 1
                        f2.write(
                            "\n"
                            + SEPARATOR
                            + "\n"
                            + "<b>"
                            + "."
                            + get_symptom_name_prop(
                                (
                                    FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES.replace(
                                        ".properties", (locale)
                                    )
                                    + ".properties"
                                ),
                                symptom_name,
                            )
                            + "\n"
                            + "\n"
                        )
                    else:
                        f2.write(
                            "\n"
                            + SEPARATOR
                            + "\n"
                            + ln_one
                            + symptom_name
                            + ln_two
                            + "\n"
                            + "\n"
                        )

                    handler = csv.reader(handle_file_open, delimiter=";")  # CSV READER

                    ###############################
                    ####START PARSING FILE HERE####
                    ###############################

                    # TODO: GI surgery, urination.csv bugaa
                    # TODO: Rash on outo lomake.
                    # TODO: Immunologic treatments, rash.csv
                    # TODO: Jos tekstikenttä, joku indikaattori siitä, tai kirjoitusboksin piirto pdfään.
                    # TODO: Käy moduuleja läpi ja jos ne on kunnossa, push
                    # TODO: ENTÄ JOS MODUULIN NIMI ON SUBSET JOSTAIN PIDEMMÄSTÄ? SILLOIN 'IN' EI TOIMI!

                    for (
                        row
                    ) in handler:  # START PARSING OF CSV! TÄMÄ ON KOPIOITUNA ALHAALLA
                        # GET QUESTIONS IN SYMPTOM AND IN MODULE
                        """
                            checkQuestion = get_show_id(str(row[SHOW_CONDITIONS]))
                            if checkQuestion != None:
                                checkQuestion = checkQuestion[0]
                            checkAnswer = get_show_id(str(row[SHOW_CONDITIONS]))
                            if checkAnswer != None:
                                checkAnswer = checkAnswer[1]
                            """

                        if ö > 0:
                            additional = "\n"
                        else:
                            additional = ""

                        if (
                            (file_match) in row[STATIC_CONDITIONS].lower()
                            and row[FIELD_TYPE] != ""
                            and row[STATIC_CONDITIONS]
                            .lstrip()
                            .lower()
                            .startswith("treatment module contains any")
                            or row[STATIC_CONDITIONS].strip() == ""
                            and row[FIELD_TYPE].strip() != ""
                            or row[FIELD_TYPE].strip() != ""
                            and row[STATIC_CONDITIONS]
                            .lstrip()
                            .lower()
                            .startswith("gender")
                            or row[FIELD_TYPE].strip() != ""
                            and row[STATIC_CONDITIONS].strip() == "form is full"
                        ):  # IF IS AN INCLUDED QUESTION
                            if (
                                row[LANGUAGE_COLUMN] == "=patient.wizard.symptom.date"
                                or row[FIELD_TYPE].lower() == "wizard section"
                                or row[FIELD_TYPE].lower() == "form begin"
                            ):  # DON'T WANT THESE
                                if (
                                    row[LANGUAGE_COLUMN].strip()
                                    == "=patient.wizard.symptom.date"
                                ):
                                    f2.write(
                                        "\n"
                                        + get_symptom_name_prop(
                                            FILE_PATH_MESSAGES, row[LANGUAGE_COLUMN]
                                        ).strip()
                                        + "\n"
                                    )
                                    all_questions.setdefault(
                                        get_symptom_name_prop(
                                            FILE_PATH_MESSAGES, row[LANGUAGE_COLUMN]
                                        ),
                                        [],
                                    ).append(
                                        symptom_name.replace(",", "")
                                    )  # ADD QUESTIONS TO DICTIONARY
                                    f2.write(
                                        html_spaces
                                        + get_symptom_name_prop(
                                            FILE_PATH_MESSAGES,
                                            "patient.wizard.symptomDateOption.symptomToday",
                                        ).strip()
                                        + html_spaces_end
                                        + "\n"
                                    )
                                    f2.write(
                                        html_spaces
                                        + get_symptom_name_prop(
                                            FILE_PATH_MESSAGES,
                                            "patient.wizard.symptomDateOption.symptomConstant",
                                        ).strip()
                                        + html_spaces_end
                                        + "\n"
                                    )
                                    f2.write(
                                        html_spaces
                                        + get_symptom_name_prop(
                                            FILE_PATH_MESSAGES,
                                            "patient.wizard.symptomDateOption.symptomOtherDates",
                                        ).strip()
                                        + html_spaces_end
                                        + "\n"
                                    )
                                if row[FIELD_TYPE].lower() == "wizard section":
                                    f2.write(next(handler)[LANGUAGE_COLUMN] + "\n")
                                continue
                            # if not questions_shown == [] and checkQuestion not in questions_shown and checkQuestion != None:
                            # continue
                            if (
                                row[LANGUAGE_COLUMN].strip() == ""
                                and row[FIELD_TYPE].strip().lower() == "pain pointer"
                            ):
                                # (A clickable body image is shown in this part of the symptom form for selecting symptomatic areaaaas.)
                                # "(Klikattava vartalokuva näytetään tässä oireellisten alueiden valintaa varten.)"
                                #   f2.write(additional + "(A clickable body image is shown in this part of the symptom form for selecting symptomatic areas.)" + "\n")  # PLACEHOLDER
                                f2.write(additional + "" + "\n")
                            elif row[LANGUAGE_COLUMN] != "":
                                if (
                                    row[SHOW_CONDITIONS].strip() != ""
                                ):  # and checkQuestion != None and checkAnswer != None:
                                    if eval_show(
                                        row[SHOW_CONDITIONS].strip(),
                                        questions_shown,
                                        shown_enums,
                                        not_shown_questions,
                                        missed_enums,
                                        questions,
                                        row[LANGUAGE_COLUMN],
                                    ):
                                        this_question = row[ID_COLUMN].lower().strip()
                                        questions.setdefault(this_question, [])
                                        f2.write(
                                            additional
                                            + row[LANGUAGE_COLUMN]
                                            .replace('""', '"')
                                            .strip()
                                            + "\n"
                                        )
                                        if (
                                            row[FIELD_TYPE].lower().strip()
                                            == "pain pointer"
                                        ):
                                            # f2.write(additional + "(A clickable body image is shown in this part of the symptom form for selecting symptomatic areas.)" + "\n")  # PLACEHOLDER
                                            f2.write(additional + "" + "\n")
                                        all_questions.setdefault(
                                            str(row[LANGUAGE_COLUMN]).strip(), []
                                        ).append(
                                            symptom_name.replace(",", "")
                                        )  # ADD QUESTIONS TO DICTIONARY
                                        questions_shown.append(row[ID_COLUMN].lower())
                                        if (
                                            row[ENUMERATION_KEY].lower().strip()
                                            == "boolean"
                                        ):
                                            questions.setdefault(
                                                this_question, [].append("true")
                                            )
                                            questions.setdefault(
                                                this_question, [].append("false")
                                            )
                                            shown_enums.append("true")
                                            shown_enums.append("false")
                                        went = True
                                    else:
                                        continue
                                elif row[SHOW_CONDITIONS].strip() == "":
                                    if row[FIELD_TYPE].lower() == "pain pointer":
                                        #    f2.write(additional + "(A clickable body image is shown in this part of the symptom form for selecting symptomatic areas.)" + "\n") #PLACEHOLDER
                                        f2.write(additional + "" + "\n")

                                    this_question = row[ID_COLUMN].lower().strip()
                                    questions.setdefault(this_question, [])
                                    f2.write(
                                        additional
                                        + row[LANGUAGE_COLUMN].replace('""', '"').strip()
                                        + "\n"
                                    )
                                    all_questions.setdefault(
                                        str(row[LANGUAGE_COLUMN]).strip(), []
                                    ).append(
                                        symptom_name.replace(",", "")
                                    )  # ADD QUESTIONS TO DICTIONARY
                                    questions_shown.append(row[ID_COLUMN].lower())
                                    if row[ENUMERATION_KEY].lower().strip() == "boolean":
                                        questions.setdefault(
                                            this_question, [].append("true")
                                        )
                                        questions.setdefault(
                                            this_question, [].append("false")
                                        )
                                        shown_enums.append("true")
                                        shown_enums.append("false")
                                    went = True

                        elif went and row[LANGUAGE_COLUMN] != "":
                            if (
                                (file_match) in row[STATIC_CONDITIONS].lower()
                                and row[STATIC_CONDITIONS].strip() != ""
                                and row[STATIC_CONDITIONS]
                                .lstrip()
                                .lower()
                                .startswith("treatment module contains any")
                                or row[STATIC_CONDITIONS].strip() == ""
                                or row[STATIC_CONDITIONS]
                                .lstrip()
                                .lower()
                                .startswith("gender")
                                and "treatment module contains any"
                                not in row[STATIC_CONDITIONS].lower()
                                or row[STATIC_CONDITIONS]
                                .lstrip()
                                .lower()
                                .startswith("gender")
                                and (file_match) in row[STATIC_CONDITIONS].lower()
                            ):  # TODO: Or show condition
                                if row[LANGUAGE_COLUMN].strip() != "":
                                    f2.write(
                                        html_spaces
                                        + row[LANGUAGE_COLUMN].replace('""', '"').strip()
                                        + html_spaces_end
                                        + "\n"
                                    )
                                    shown_enums.append(
                                        row[ENUMERATION_KEY].lower().strip()
                                    )
                                    if this_question is not None:
                                        questions.setdefault(this_question, []).append(
                                            row[ENUMERATION_KEY].lower().strip()
                                        )
                            else:
                                if row[FIELD_TYPE].strip() != "":
                                    went = False
                                missed_enums.append(row[ENUMERATION_KEY].lower().strip())
                                continue
                            went = True
                        else:
                            if (
                                row[FIELD_TYPE].strip() != ""
                                and row[LANGUAGE_COLUMN].strip() != ""
                                and row[ID_COLUMN].strip().lower() != "id"
                            ):
                                not_shown_questions.append(
                                    row[ID_COLUMN].strip().lower()
                                )
                            went = False
                        ö += 1
                        ###############################
                        ##DONE WITH PARSING FILE HERE##
                        ###############################

            except Exception as e:  # TÄHÄN ASTI KOPIOITU!
                print(
                    str(e).capitalize()
                )  # IF CAN'T OPEN OR HANDLE A SYMPTOM FORM FILE, ALTHOUGH TOO BIG OF A SCOPE FOR TRY!
                if symptom_name != "":
                    print(
                        "I had problems with either opening or handling "
                        + handle_file
                        + "."
                    )
                    # CATCH INCORRECT SYMPTOM FORM NAMES (BUT ALSO OTHER...).
                    errors = True  # FOR CHECKING PURPOSES IN THE END OF SCRIPT
                continue

        # FIND OUT IF WE HAVE DUPLICATES AND WRITE THEM
        text = False
        for key in all_questions:
            if len(all_questions[key]) > 1:  # DUPLICATE KEYS GET MORE VALUES
                text = True

        if text:
            f2.write(
                "\n"
                + SEPARATOR_END
                + "\n"
                + SEPARATOR_DUPLICATES
                + "\n"
                + ln_duplicates
                + "\n"
                + SEPARATOR_DUPLICATES
                + "\n"
            )
            for key in all_questions:
                if len(all_questions[key]) > 1:
                    f2.write(
                        "\n"
                        + "'"
                        + str(key).strip()
                        + "'"
                        + ln_asked
                        + str(all_questions[key])
                        .replace("[", "")
                        .replace("]", "")
                        .replace("'", "")
                        + "."
                    )

        # CLEAR OUT FOR NEXT MODULE
        all_questions = {}

        # CLOSE WRITABLE FILE
        add = 0
        f2.close()

    # DELETE FORMS DIRECTORY FROM OUTPUT
    try:
        os.remove(FILE_PATH_FOR_OUTPUT + "forms")
    except:
        print("Forms directory not found.")

    # INFORM USER
    if text:
        print("Duplicates found and they are written to each module .txt file.")

    #  SLEDGEHAMMER CHECK THAT WE HAVE THE INFO WE NEED
    if errors == False and actual_occurrences == numbers:
        print(
            "Was able to handle "
            + str(numbers)
            + " occurrences of symptom form files in modules and there are "
            + str(actual_occurrences)
            + " occurrences of symptom files in the module spec files in total."
            + "\n"
            + "We should have all the info we need in the 'Output' folder as long as the spec files are correct and design of the forms is not altered."
        )
    else:
        print(
            "\n"
            + "I have "
            + str(actual_occurrences)
            + " symptom forms (that would need to be opened from module spec files), "
            + "and I opened "
            + str(numbers)
            + " symptom form(s)."
        )
        print(
            "Errors occurred. Check prints above for further information."
            + "\n"
            + "If no errors are printed your module spec files might be incorrect or form files may be missing or the design of the forms altered."
        )


def create_csv_or_write(header, values, prefix):
    if os.path.exists(prefix + ".csv"):
        with open(prefix + ".csv", "a") as f:
            writer = csv.writer(f, delimiter=";")
            writer.writerow(values)
    else:
        with open(prefix + ".csv", "wt", newline="", encoding="utf-8") as file:
            writer = csv.writer(file, delimiter=";")
            writer.writerow(i for i in header)
            writer.writerow(s for s in values)


def find_rules(module):
    doable = True
    stupid = []
    if module.lower().endswith("_store"):
        doable = False
    else:
        wanted_module_symptoms = get_module_symptoms(module)
        stupid.append(module)
    module = (
        module.replace("active-treatment", "")
        .replace("recovery_", "")
        .replace("-", " ")
        .replace(".txt", "")
    )
    if module.startswith("recovery"):
        module = module[len("recovery") :]
    if module.startswith("_"):
        module = module[len("_") :]
        module = module.replace("_", " ").lower()

    if doable:
        for file in wanted_module_symptoms[1]:
            if os.path.exists(RULES + file + ".csv"):
                with open(RULES + file + ".csv") as handle_file:
                    handler = csv.reader(handle_file, delimiter=";")  # CSV READER
                    for row in handler:
                        if row[0].lower().startswith("module"):
                            continue
                        else:
                            comparable_list = row[0].split(",")
                            comparable_list = [
                                x.strip().lower() for x in comparable_list
                            ]
                            if [i for i in comparable_list if module == i] != []:
                                fixed_start = (
                                    row[1]
                                    + "#"
                                    + row[2].lower()
                                    + "#"
                                    + row[7]
                                    + "#"
                                    + row[22]
                                    + "#"
                                )
                                priority = row[2].lower()
                                dialogs = row[36:50]
                                dialogs = [
                                    ("Default " + priority + " text")
                                    if x.strip() == ""
                                    else x
                                    for x in dialogs
                                ]
                                messages = row[8:22]
                                messages = [
                                    "N/A" if x.strip() == "" else x for x in messages
                                ]
                                whole_content = (
                                    fixed_start
                                    + "#".join(dialogs)
                                    + "#"
                                    + "#".join(messages)
                                    + "#"
                                    + row[50]
                                ).split("#")
                                create_csv_or_write(
                                    (
                                        "Rule name;Priority;Condition;Description_EN;Dialog text for patient_EN;Dialog text for patient_FI;Dialog text for patient_SV;Dialog text for patient_DA;Dialog text for patient_NO;Dialog text for patient_DE;Dialog text for patient_FR;Dialog text for patient_ES;Dialog text for patient_IT;Dialog text for patient_PT;Dialog text for patient_AR;Dialog text for patient_HE;Dialog text for patient_NL;Dialog text for patient_TR;Message_to_patient’s_inbox_EN;Message_to_patient’s_inbox_FI;Message_to_patient’s_inbox_SV;Message_to_patient’s_inbox_DA;Message_to_patient’s_inbox_NO;Message_to_patient’s_inbox_DE;Message_to_patient’s_inbox_FR;Message_to_patient’s_inbox_ES;Message_to_patient’s_inbox_IT;Message_to_patient’s_inbox_PT;Message_to_patient’s_inbox_AR;Message_to_patient’s_inbox_HE;Message_to_patient’s_inbox_NL;Message_to_patient’s_inbox_TR;Possible hazard"
                                    ).split(";"),
                                    whole_content,
                                    stupid[0].replace(".txt", ""),
                                )
            else:
                print(
                    "No rule file for "
                    + file
                    + ".csv when gathering rules for "
                    + module
                    + "."
                )


# run()
get_questions()
