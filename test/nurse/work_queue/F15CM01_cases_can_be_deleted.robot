*** Settings ***
Documentation       F15CM01 Clinic manager can delete a case
...                 Preconditions:
...                 - Logged in as clinic manager
...                 - Case management is enabled for clinic.
...                 - Case is created for a patient.

Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_note.resource

Suite Setup         Run Keywords    Set Libraries Order
...                     AND    Login As Clinic Manager
Suite Teardown      Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cm01    clinic-web    work-queue


*** Variables ***
${clinic_users_link}                //*[@id="manage-nurses-link"]
${log_tab_link}                     //*[@data-testid="users-logs"]
${first_log}                        (//*[@data-testid="logs"]//tr[1])[2]
${home_link}                        //*[@id="home-link"]
${patient_cases_tab}                //*[@id="tab-patient-messaging"]
${last_case_note}                   (//span[text()='Reminder'])[last()]
${settings_icon}                    //*[@data-testid='settings-button']
${select_reason_dropdown}           //*[@data-testid="selectedReason"]//input
${first_reason_in_dropdown}         //*[@data-testid="option-0"]
${test_topic_case}                  //div[contains(@class, "case-title") and contains(text(), "Test Topic")]
${first_diary_questionnaire}        (//nh-timeline-month/nh-timeline-day//nh-timeline-questionary)[1]
${first_case_card}                  //*[@data-testid="patient-case-list"]//noona-patient-case-list-item[1]
${delete_button}                    //*[@data-testid='delete-button']//*[contains(text(),'Error')]
${add_note_btn}                     //*[@data-testid='add-note-btn']
${case_type_symptom_management}     Symptom Management
${cancel_survey_modal}              //a[@id='cancel-feedback']


*** Test Cases ***
Patient cases can be deleted
    [Tags]    nms9-ver-325
    Create Case And Go To Work Queue
    Open case details from patient cases
    Open additional options menu
    Select Delete case
    Select Reason
    Select Delete
    Case is deleted and the action cannot be reversed
    Access clinic user log and see "Case deleted" transaction

Extension A - Delete a patient note
    [Tags]    nms9-ver-326
    Search Patient By Identity Code    ${f15cm01_patient_2}[ssn]
    Add Note    Reminder
    Try To Click Banner Message
    Open patient note from patient cases
    Open additional options menu
    Select Delete case
    Select Reason
    Select Delete
    Case is deleted and the action cannot be reversed
    Access clinic user log and see "Case deleted" transaction

Extension B - Delete a patient questionnaire report
    [Tags]    nms9-ver-327
    Schedule Questionnaire For A Patient
    Patient Answers The Questionnaire
    Login As Clinic Manager
    Search Patient By Identity Code    ${f15cm01_patient_3}[ssn]
    Go To Patient Cases And Delete Questionnaire
    Questionnaire Is Shown As Expired
    Close Browser
    Deleted Questionnaire For Patient Is Shown As Expired And Message Is Removed

Extension C - Delete a message sent to patient
    [Tags]    nms9-ver-328
    Login As Clinic Manager
    Search Patient By Identity Code    ${f15cm01_patient_4}[ssn]
    Contact Patient    Test Topic    Instructions
    Try To Click Banner Message
    Cancel User Satisfaction Survey Modal
    Open Test Topic Case
    Open additional options menu
    Select Delete case
    Select Reason
    Select Delete
    Case is deleted and the action cannot be reversed
    Access clinic user log and see "Case deleted" transaction
    Close Browser
    Patient Can See The Message As Removed


*** Keywords ***
Patient Can See The Message As Removed
    Login As Patient    ${f15cm01_patient_4}[email]
    Navigate to Clinic
    Select Latest Clinic Message
    Wait Until Page Contains    Message removed by your clinic
    Wait Until Page Contains    This message was sent to you in error. It has been removed by your clinic.
    Wait Until Page Contains
    ...    Please contact the clinic, if you have any questions or concerns regarding this message.

Open Test Topic Case
    Try To Click Element    ${test_topic_case}

Open case details from patient cases
    Open patient case with symptom management

Open patient case with symptom management
    Remove All Care Team Filter
    Wait Until Keyword Succeeds    9s    0.3s    Select Case Type Filter In Work Queue    Symptom Management
    Remove Selected Primary Providers
    Wait Until Keyword Succeeds    9s    0.3s    Select My Patients In Work Queue
    Wait Until Keyword Succeeds    9s    0.3s    Remove Patient Group Filters In Work Queue
    ${patient}=    Set Variable    //*[@id="workqueue-0"]
    Select Patient Card    ${f15cm01_patient_1}[name]

Open patient case
    Try To Click Element    ${case_type_filter}
    Try To Click Element    (//div[@class="dropdown-filter"])[2]/descendant::li//label[@for="single-select-1"]/..
    Try To Click Element    ${open_cases_tab}
    ${patient}=    Set Variable    //*[@id="workqueue-0"]
    Try To Click Element    ${patient}

Open additional options menu
    Wait Until Page Contains Element    ${settings_icon}    30s
    Try To Click Element    ${settings_icon}

Select Delete case
    Try To Click Element    ${delete_button}

Confirmation dialog is displayed
    ${modal}=    Set Variable
    ...    xpath=//general-confirm-modal/div[@class = "general-modal-title"][text()[contains(., "Delete case")]]
    Wait For Element To Be Present    ${modal}

Select Reason
    Wait Until Page Contains Element    ${select_reason_dropdown}
    Try To Click Element    ${select_reason_dropdown}
    Try To Click Element    ${first_reason_in_dropdown}

Select Delete
    ${confirm}=    Set Variable    //*[@id="confrim-delete-case-button"]
    Try To Click Element    ${confirm}
    Wait Until Page Contains    The case was set to Error status
    Try To Click Banner Message

Case is deleted and the action cannot be reversed
    ${modal}=    Set Variable
    ...    xpath=//general-confirm-modal/div[@class = "general-modal-title"][text()[contains(., "Delete case")]]
    Wait For Element To Not Be Present    ${modal}
    Wait Until Element Is Not Visible    ${modal}

Access clinic user log and see "Case deleted" transaction
    Return to front page
    Try To Click Element    ${clinic_users_link}
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Enabled    ${clinic_menu}
    Try To Click Element    ${log_tab_link}
    ${current_date}=    Get Current Date    result_format=%d.%m.%Y
    Wait For Element To Be Present    ${first_log}
    Wait Until Keyword Succeeds
    ...    4s
    ...    0.2s
    ...    Element Should Contain
    ...    (//*[contains(text(),'${current_date}')]/../*[contains(text(),'${automated_tests_clinic}[f15cm01_manager]')])/..
    ...    Case status set to Error

Open patient note from patient cases
    ${status}=    Run Keyword and Return Status    Wait Until Page Contains Element    ${add_note_btn}
    IF    ${status} != True    Try To Click Element    ${patient_cases_tab}
    ${new_note}=    Set Variable    (//div[text()=' ${random_string_topic} '])    # from Add Note keyword
    Wait Until Page Contains Element    ${new_note}
    Scroll Element Into View    ${new_note}
    Try To Click Element    ${new_note}

Open patient questionnaire report from patient cases
    Return to front page
    ${reports}=    Set Variable    //*[@id="side-effect-reports"]
    Try To Click Element    ${reports}
    ${first_report}=    Set Variable    xpath=//div[@class = "name-container"][1]
    Try To Click Element    ${first_report}

Return to front page
    ${frontpage}=    Set Variable    //*[@id="back-to-button"]
    Run Keyword And Ignore Error    Try To Click Element    ${frontpage}
    Try To Click Element    ${home_link}

After deletion, the questionnaire appears as "Expired" in the patient application (diary timeline and message) and in the clinic UI in the patient Questionnaires tab
    Fail test execution    Not yet implemented
    # Login As Patient

Login As Clinic Manager
    Login As Nurse    email=${automated_tests_clinic}[f15cm01_manager]

Go To Patient Cases And Delete Questionnaire
    Try To Click Element    ${patient_cases_tab}
    Try To Click Element    ${first_case_card}
    Open additional options menu
    Select Delete case
    Select Reason
    Select Delete

Questionnaire Is Shown As Expired
    Navigate To Questionnaires Tab
    Questionnaire Status Is Correct    PACT    EXPIRED

Schedule Questionnaire For A Patient
    Search Patient By Identity Code    ${f15cm01_patient_3}[ssn]
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    PACT
    Close Browser

Patient Answers The Questionnaire
    Login As Patient    ${f15cm01_patient_3}[email]
    questionnaires.Complete Questionnaire    PACT
    questionnaires.Save Questionnaire    PACT
    Close Browser

Deleted Questionnaire For Patient Is Shown As Expired And Message Is Removed
    Login As Patient    ${f15cm01_patient_3}[email]
    Wait Until Page Contains Element    ${first_diary_questionnaire}
    Element Should Contain    ${first_diary_questionnaire}    PACT
    Element Should Contain    ${first_diary_questionnaire}    Expired
    Navigate to Clinic
    Select Latest Clinic Message
    Wait Until Page Contains    Message removed by your clinic
    Wait Until Page Contains    This message was sent to you in error. It has been removed by your clinic.
    Wait Until Page Contains
    ...    Please contact the clinic, if you have any questions or concerns regarding this message.

Set Case Details
    [Arguments]    ${case_type}
    Set Test Variable    @{acute_symptoms}    Chest Pain
    &{case_details}=    Create Dictionary    case_type=${case_type}    care_team=f15cm01 care team
    ...    assigned_to=f15cm01 manager    case_description=Test case description
    ...    acute_symptom=${acute_symptoms}    case_priority=High
    ...    case_origin=Patient Initiated Call
    Set Test Variable    &{case_details}

Create Case And Go To Work Queue
    Set Case Details    ${case_type_symptom_management}
    Open A New Case    ${f15cm01_patient_1}[ssn]    save
    Wait Until Page Contains    ${case_created_successfully_banner}
    Try To Click Banner Message
    Return To Work Queue

Cancel User Satisfaction Survey Modal
    ${status}=    Run Keyword And Return Status    Page Should Contain    'How satisfied have you been using Noona?'
    IF    ${status}==${TRUE}    Try To Click Element    ${cancel_survey_modal}
