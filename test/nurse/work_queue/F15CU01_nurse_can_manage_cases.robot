*** Settings ***
Documentation       F15CU01 Clinic user can create, process and edit a case
...                 Preconditions:
...                 - Case management is enabled in clinic settings.
...                 - User is logged in.
...                     # TODO: Sort case opened column

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources/nurse/list_of_patients_tab.resource

Suite Setup         Set Libraries Order
Test Setup          Add An Activated Patient Under Default Clinic
                    ...    f15cu01
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu01    clinic-web    work-queue


*** Test Cases ***
Open a case
    [Tags]    nms9-ver-306
    Login As Nurse
    Set Case Details    Chemo / Tx Questions
    Open A New Case    ${patient_ssn}    save
    Wait Until Page Contains    Case created successfully
    First Case Type Is Correct    Chemo / Tx Questions
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A – Process a case
    [Tags]    nms9-ver-307
    ##TODO: Open a case from the Open cases work queue
    ##TODO: User can select to view the work queue as patient cards or as a list
    Login As Nurse
    Set Case Details    Appointment Questions
    Open A New Case    ${patient_ssn}    save
    Wait Until Page Contains    Case created successfully
    First Case Type Is Correct    Appointment Questions
    Process The Case
    Close Case
    First Case Status Is Correct    Closed
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B – Edit a case
    [Tags]    nms9-ver-308
    ##TODO: Open a case in the patient's cases list
    Login As Nurse
    Set Case Details    Other
    Open A New Case    ${patient_ssn}    save
    Process The Case
    Click Edit Case Button
    Sleep    1
    Input Case Description    New case description
    Click Save And Exit Case Button
    Wait Until Page Contains    The patient case was successfully updated
    Wait Until Page Contains    New case description
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension C – Edit the case type
    [Tags]    nms9-ver-309
    ##TODO: Open a case in the patient's cases list
    Login As Nurse
    Set Case Details    Medication Refill
    Set Medication Details
    Open A New Case    ${patient_ssn}    continue
    Input Medication Refill Details
    Click Case Management Next Button
    Click Save And Exit Case Button
    Click Edit Case Button
    Select Case Type    Other
    Change Case Type
    Input Case Description    Case type changed from Medication Refill to Other
    Click Save And Exit Case Button
    Wait Until Page Contains    The patient case was successfully updated
    First Case Type Is Correct    Other
    Wait Until Page Contains    Case type changed from Medication Refill to Other
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension D – Create and process a symptom management case
    [Tags]    nms9-ver-310
    [Setup]    Add An Activated Patient Under Default Clinic    f15cu01-extd    module=${BONE_RADIOTHERAPY}
    Set Case Details    Symptom Management
    Set Test Variable    @{acute_symptoms}    Chest Pain    Trouble Breathing
    Login As Nurse
    Open A New Case    ${patient_ssn}    continue
    Select Case Symptom    Other symptom
    Click Case Management Next Button
    Complete Case Symptom Details
    Select Patient Symptomatic Days In Calendar
    Click Case Management Next Button
    Case Summary Is Visible
    Case Symptom Summary Is Visible
    Click Save And Exit Case Button
    Wait Until Page Contains    Case created successfully    5s
    First Case Type Is Correct    Symptom Management
    Acute Symptoms Are Visible In Case Summary    @{acute_symptoms}
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E – Create and process a medication refill case
    [Tags]    nms9-ver-311
    Login As Nurse
    Set Case Details    Medication Refill
    Set Medication Details
    Open A New Case    ${patient_ssn}    continue
    Input Medication Refill Details
    Click Case Management Next Button
    Case Summary Is Visible
    Medication Summary Is Visible
    Click Save And Exit Case Button
    Wait Until Page Contains    Case created successfully
    First Case Type Is Correct    Medication Refill
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension F – Using open cases work queue Filter patient cases in work queue based on case type
    [Tags]    nms9-ver-312
    [Setup]    Set Variable And Close Cases
    Create New Cases With Different Priority
    Go To Work Queue
    Select Case Type Filter In Work Queue    Select all
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    f15cu01 care team
    Patient Cards Are Displayed In Work Queue
    Click List View Button
    Patients Are Displayed In List View
    Case Statuses Order In List View Is Correct    @{expected_statuses}
    Previous Work Queue Selection Is Remembered
    Columns Can Be Sorted
    Select Case Type Filter In Work Queue    Paperwork
    Wait Until Keyword Succeeds    20s    1s    Compare Lists And Cases Count Is Correct
    Open Cases Count In Tab Is Correct    1    3

Extension G – Closed cases are sent to EMR as pdf files
    [Documentation]    Preconditions: Integration to EMR is enabled
    [Tags]    nms9-ver-313
    [Setup]    Add An Activated Patient Under Default Clinic    f15cu01-extg    module=${BONE_RADIOTHERAPY}
    Set Case Details    Chemo / Tx Questions
    Login As Nurse
    Open A New Case    ${patient_ssn}    save
    Wait Until Page Contains    Case created successfully
    Try To Click Banner Message
    Wait Until Page Does Not Contain      Case created successfully
    Close Case
    Sleep    1
    First Case Status Is Correct    Closed
    Select Case From List    Chemo / Tx Questions    Closed
    Archived To EMR Internal Message Is Displayed
    Information Is Displayed In Latest Change Log    Content archived to EMR
    PDF Icon Is Enabled
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension H – Clinic user can call patient by clicking the phone number of the patient
    [Documentation]    Preconditions: Patient has a valid phone number set to his/her profile information
    [Tags]    nms9-ver-314
    [Setup]    Login As Nurse
    Go To List Of Patient And Select First Patient
    Patient's Phone Number Link Is Clickable


*** Keywords ***
Set Case Details
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=Care Team 1
    ...    assigned_to=Clinic User    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}

Set Medication Details
    &{medication_details}    Create Dictionary
    ...    category=Controlled substance 1-2
    ...    medication_name=Medication Name Test
    ...    number_refills=3
    ...    dose=2
    ...    unit=mg
    ...    frequency=daily
    ...    physician=testLP1, testFP1 testMP1. testSP1
    Set Test Variable    &{medication_details}

Create New Cases With Different Priority
    Set Case Details    ${cases_list}[0]
    Set To Dictionary    ${case_details}    care_team=f15cu01 care team    case_priority=High
    Open A New Case    ${ssn_list}[0]    save
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Return To Patients
    Set Case Details    ${cases_list}[1]
    Set To Dictionary    ${case_details}    care_team=f15cu01 care team
    Open A New Case    ${ssn_list}[1]    save
    Try To Click Banner Message
    Return To Patients
    Set Case Details    ${cases_list}[2]
    Set To Dictionary    ${case_details}    care_team=f15cu01 care team    case_priority=Low
    Open A New Case    ${ssn_list}[2]    save
    Try To Click Banner Message
    Return To Patients

Previous Work Queue Selection Is Remembered
    Navigate To Patient Page
    Go To Work Queue
    Patients Are Displayed In List View
    Case Statuses Order In List View Is Correct    @{expected_statuses}

Columns Can Be Sorted
    Sleep    1
    @{status_list}    Create List    LOW    MEDIUM    HIGH
    Open Cases Tab Column Is Sorted Correctly    Priority    @{status_list}
    Sleep    1
    @{status_list}    Create List    Callback Requested    Callback Requested    Callback Requested
    Open Cases Tab Column Is Sorted Correctly    Status    @{status_list}
    # TODO: Sort case opened column
    Sleep    1
    @{status_list}    Create List    User Clinic    User Clinic    User Clinic
    Open Cases Tab Column Is Sorted Correctly    Assignee    @{status_list}

Set Variable And Close Cases
    @{expected_statuses}    Create List    HIGH    MEDIUM    LOW
    @{ssn_list}    Create List    ${f15cu01_ssn1}[ssn]    ${f15cu01_ssn2}[ssn]    ${f15cu01_ssn3}[ssn]
    @{cases_list}    Create List    Chemo / Tx Questions    Lab Questions / Test Results    Paperwork
    Set Test Variable    @{expected_statuses}
    Set Test Variable    @{ssn_list}
    Set Test Variable    @{cases_list}
    FOR    ${INDEX}    IN RANGE    0    3
        Close Open Cases    ${ssn_list}[${INDEX}]
        Return To Patients
    END

Compare Lists And Cases Count Is Correct
    Get Open Cases Tab Column List    Case type
    @{case}    Create List    Paperwork
    Lists Should Be Equal    ${actual_column_values_list}    ${case}
    Open Cases Count In Tab Is Correct    1    3
