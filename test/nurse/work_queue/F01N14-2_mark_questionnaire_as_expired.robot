*** Settings ***
Documentation       F01N14-2 Clinic user can mark questionnaire as expired
...                 Preconditions:
...                 - Questionnaire has been sent to the patient.
...                 - Patient has not replied to the questionnaire.
...                 - Clinic user can process reports sent by patient (F01N14 Clinic user can process reports sent by patient)
...                 NOTE: Extension B is dependent on Extension A
...                     # TODO: Set existing sent questionnaires to expired for clean testing

Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase_f01n14_2    clinic-web    work-queue


*** Variables ***
${aeq_questionnaire}        aeq
${qol_questionnaire_2}      qol
${expired}                  Expired
${sent}                     SENT


*** Test Cases ***
Main success scenario – Mark a questionnaire as expired
    [Tags]    nms9-ver-88
    Add An Activated Patient Under Default Clinic    f01n14-2_main    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N14}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    ${STATUS_CHECK}
    Questionnaire Status Is Correct    ${STATUS_CHECK}    ${sent}    questionnaire_type=${aeq_questionnaire}
    Mark Questionnaire As Expired    ${aeq_questionnaire}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Questionnaire Status Is Correct    ${STATUS_CHECK}    EXPIRED by Clinic User on ${current_date}
    ...    questionnaire_type=aeq
    Try To Click Element    ${back_to_button}
    Go To Patient Reports
    Wait For Element To Be Present    ${questionnaire_type_filter}
    Wait Until Page Does Not Contain    ${first_name}${SPACE}${family_name}
    Select Today Tab
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n14_care_team]
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A – Mark Distress Questionnaire or Quality of Life Questionnaire as expired
    [Documentation]    Also includes: Extension B – patient sees that the questionnaire has been marked as expired
    [Tags]    nms9-ver-89    nms9-ver-90
    [Setup]    Add An Activated Patient Under Default Clinic    f01n14-2_exta    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N14}
    # extension A
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Questionnaire Status Is Correct    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    ${sent}
    Mark Questionnaire As Expired    ${qol_questionnaire_2}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Questionnaire Status Is Correct    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    EXPIRED by Clinic User on ${current_date}
    Go To Work Queue
    Go To Patient Reports
    Wait For Element To Be Present    ${questionnaire_type_filter}
    Wait Until Page Does Not Contain    ${first_name}${SPACE}${family_name}
    Select Today Tab
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n14_care_team]
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    # extension B
    Login As Patient    email=${patient_email}
    Latest Questionnaire's Status Is Correct    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    ${expired}
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

