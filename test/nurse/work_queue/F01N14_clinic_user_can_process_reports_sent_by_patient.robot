*** Settings ***
Documentation       F01N14 Clinic user can process reports sent by patient
...                 Preconditions:
...                 - Clinic user has scheduled adverse effect questionnaire to a patient.
...                 - Patient has replied to questionnaire.

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n14    clinic-web    work-queue


*** Variables ***
${copy_to_clipboard_button}                         copy
${date_format_API}                                  %Y-%m-%d
${f01n14_nurse_1_fullname}                          f01n14 nurse1
${f01n14_consultant_fullname}                       f01n14 consultant
${patient_case_consultation_note}                   Test case consultation another care team, another clinic user
${patient_case_consultation_note_reply}             Patient case has been consulted. Case can be closed.
${automated_test_clinic_default_user_initials}      CU


*** Test Cases ***
Main success scenario - processing a symptom questionnaire - Part 1
    [Documentation]    Verify Patient Reports-tab's filter changes, browser remembers last selected filter.
    [Tags]    nms9-ver-3-1    nms9-ver-3
    [Setup]    Main success scenario - Part 1 - Pre-conditions
    Login As Nurse    ${automated_tests_clinic}[f01n14_nurse_1]
    Go To Patient Reports
    Wait Until Noona Loader Is Not Visible
    Unhandled Tab Is Selected By Default
    Select Care Team Filter In Work Queue    f01n14 care team
    Patient Cards At Least One Is Visible For F01N14 Care Team
    Clinic User Can Filter The Reports By Status Non-Completed
    Go To Open Cases Tab
    Go To Patient Reports
    Non Completed Tab Is Selected
    Patient Reports Can Be Filtered By Date    Today
    Go To Closed Cases Tab
    Go To Patient Reports
    Today Tab Is Selected
    Patient Reports Can Be Filtered By Date    Tomorrow
    Navigate To Patient Page
    Go To Work Queue
    Go To Patient Reports
    Tomorrow Tab Is Selected
    Patient Reports Can Be Filtered By Another Date In 7 Days
    Other Date Tab Is Selected
    [Teardown]    Close Browser

Main success scenario - processing a symptom questionnaire - Part 2
    [Documentation]    Verify symptom report case proccessing, expand symptom with photo, reply to patient with instruction, case status handled.
    [Tags]    nms9-ver-3-2    nms9-ver-3
    [Setup]    Main success scenario - Part 2 - Pre-conditions
    Login As Nurse    ${automated_tests_clinic}[f01n14_nurse_1]
    Go To Patient Reports
    Wait Until Noona Loader Is Not Visible
    Unselect My Patients In Filter In Work Queue
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    f01n14 care team
    Select Questionnaire Type In Work Queue    Symptom questionnaire
    Set Test Variable    ${patient_name}    ${first_name}${SPACE}${family_name}
    Patient Reports Can Be Filtered By Date    Today
    Select Patient Card    ${patient_name}
    Case Is Assigned To Correct User    ${BASELINE_QUESTIONNAIRE}    ${f01n14_nurse_1_fullname}
    Clinic User Can Display The Symptom Report
    Clinic User Can Click To Expand Reported Symptom One By One
    Symptom Row Is Expandable And Collapsable
    Expand A Single Symptom From A Symptom Report    symptom-other-symptom    yes
    Attached Photo From A Reported Symptom Can Be Viewed In Fullscreen And Closed
    Create Case Note    Test F01N014 case note    F01N14 patient case is being handled.
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Contact Patient Per Case    Instructions    none
    F01P14 Patient Receives Email About Clinic Reply To Their Questionnaire
    Case Status Is Correct    ${BASELINE_QUESTIONNAIRE}    Closed
    Return To Work Queue
    F01N14 Patient Report Case Is No Longer Visible In Unhandled Filter Work Queue
    F01N14 Patient Report Case Was Handled And Visible On Close Cases Tab
    F01P14 Patient Receives Email After The Clinic Has Handled Their Submitted Questionnaire
    [Teardown]    Main success scenario - Part 2 - Test Teardown

Main success scenario - processing a symptom questionnaire - Part 3
    [Documentation]    Verify symptom report case proccessing, send case for consultation from another care team or from another clinic user from a different care team.
    [Tags]    nms9-ver-3-3    nms9-ver-3
    [Setup]    Main success scenario - Part 3 - Test Setup
    Login As Nurse    ${automated_tests_clinic}[f01n14_nurse_1]
    Go To Patient Reports
    Wait Until Noona Loader Is Not Visible
    Unselect My Patients In Filter In Work Queue
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    f01n14 care team
    Select Questionnaire Type In Work Queue    Symptom questionnaire
    Set Test Variable    ${patient_name}    ${first_name}${SPACE}${family_name}
    Patient Reports Can Be Filtered By Date    Today
    Select Patient Card    ${patient_name}
    Case Is Assigned To Correct User    ${BASELINE_QUESTIONNAIRE}    ${f01n14_nurse_1_fullname}
    # Consult a case from another care team != f01n14 care team
    F01P14 Clinic User Sends Patient Report For Consultation From Another Clinic User In Another Care Team
    The Case In Consultation Is Visible In Another Care Team Work Queue
    [Teardown]    Main success scenario - Part 3 - Test Teardown

#    NMS9-VER-68 covers: Assign/unassign the case report to him/herself (refer to test case NMS9-VER-68)
#    NM9-VER-2 covers these following steps
#    Clinic user navigates back to work queue
#    Unreviewed filter: the handled patient is not anymore listed in the page
#    Non completed: List of all questionnaires which has not been answered and the questionnaire is not expired
#    Day specific filter is selected: the patient is marked as handled. If a handled patient is opened from this work queue filtered page, the patient is not assigned again automatically
#    Clinic user navigates to Patient reports work queue
#    Unhandled filter is selected as default
#    Clinic user can filter the reports by status (unhandled,non completed) date (today, tomorrow, other date), Questionnaire type, or Care teams
#    If a filter is changed, the browser remembers the selection when navigating back to the page.

Extension A - Emergency or semi-emergency symptom
    [Documentation]    [file:F01N10.robot|F01N10 Nurse can list patients whose symptom report has triggered an alert or a self care rule and validate the triggered rule]
    [Tags]    nms9-ver-85
    [Setup]    Add An Activated Patient Under Default Clinic    f01p12-exta    module=${BONE_RADIOTHERAPY}    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N10}
    Close Open Cases Per Care Team    f01n10 care team
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    ${BASELINE_QUESTIONNAIRE}
    Close Browser
    Login As Patient    email=${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Answer Baseline Questionnaire With Severe Symptom
    Emergency Priority Symptom Is Displayed
    Close Browser
    Login As Nurse
    Go To Patient Reports
    Select Questionnaire Type In Work Queue    Select all
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    f01n10 care team
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Wait Until Page Contains    ${case_type_filters}[symptom_report]
    Close Case
    Sleep    1
    Case Status Is Correct    ${BASELINE_QUESTIONNAIRE}    Closed
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Exporting A Questionnaire
    # Note: Patient needs to be answered baseline questionnaire
    [Tags]    nms9-ver-86
    Search Patient By Identity Code    ssn=${f01n14_patient_extB}[ssn]
    Go To Patient Cases Tab
    Select Case From Patient Cases Tab    ${BASELINE_QUESTIONNAIRE}
    Open Copy To Clipboard
    Check Clipboard Content
    Click Copy To Clipboard Button
    Close Browser

Extension C - Tennesee Oncology Distress Screening questionnaire
    [Tags]    nms9-ver-87
    Add An Activated Patient Under Default Clinic    f01n14-c
    Verify If Distress Score Is Correctly Displayed    medium
    Return To Patients
    Verify If Distress Score Is Correctly Displayed    high
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Distress Is More Than 4/6
    Sleep    1
    questionnaires.Check And Select Radio Buttons
    questionnaires.Select Specific Answer To Question
    ...    1. Little interest or pleasure in doing things
    ...    Nearly Every Day
    ...    tnonc distress
    questionnaires.Select Specific Answer To Question
    ...    2. Feeling down, depressed or hopeless
    ...    More than Half the Days
    ...    tnonc distress
    Rate With Vertical Slider    5
    Set Test Variable    ${expected_score}    5.0/6

Anxiety Is 7 Or Less
    Sleep    1
    questionnaires.Check And Select Radio Buttons
    questionnaires.Select Specific Answer To Question    1. Little interest or pleasure in doing things    Several Days
    ...    tnonc distress
    questionnaires.Select Specific Answer To Question    2. Feeling down, depressed or hopeless    Not At All
    ...    tnonc distress
    Rate With Vertical Slider    7
    Set Test Variable    ${expected_score}    1.0/6

Verify If Distress Score Is Correctly Displayed
    [Arguments]    ${expected_priority}
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    Tennessee Oncology Distress Screening
    Click Complete Button Per Questionnaire    Tennessee Oncology Distress Screening
    IF    '${expected_priority}'=='high'
        Distress Is More Than 4/6
    ELSE
        Anxiety Is 7 Or Less
    END
    Click Questionnaire Next Button
    questionnaires.Check Summary
    Save Questionnaire Answered For Patient
    Try To Click Element    ${ok_button}
    Return To Patients
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Patient Cases Tab
    IF    '${expected_priority}'=='medium'
        Case Status Is Correct    Tennessee Oncology Distress Screening    Closed
    END
    Select Case From Patient Cases Tab    Tennessee Oncology Distress Screening
    Tnonc Distress Score Is Displayed Correctly    ${expected_score}
    IF    '${expected_priority}'=='high'
        Case Priority Is Displayed Correctly    High
    ELSE IF    '${expected_priority}'=='medium'
        Case Priority Is Displayed Correctly    Medium
    END
    IF    '${expected_priority}'=='medium'
        Archived To EMR Internal Message Is Displayed
    END

Go To Patient Cases Tab
    Try To Click Element    ${patient_cases_tab}

Open Copy To Clipboard
    Try To Click Element    ${clipboard_icon}

Check Clipboard Content
    Wait Until Page Contains Element    ${clipboard_content}
    ${page_content}    Get Value    ${clipboard_content}
    IF    '${ENVIRONMENT}' == 'test'
        Get Test File
    ELSE IF    '${ENVIRONMENT}' == 'staging'
        Get Staging File
    END
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        Should Contain    ${page_content}    ${line}
    END

Get Test File
    ${content}    Get File    ${EXECDIR}${/}resources${/}nurse${/}clipboard_patient_content_test.txt
    Set Test Variable    ${content}

Get Staging File
    ${content}    Get File    ${EXECDIR}${/}resources${/}nurse${/}clipboard_patient_content_staging.txt
    Set Test Variable    ${content}

Click Copy To Clipboard Button
    Try To Click Element    ${copy_to_clipboard_button}
    Wait Until Page Contains    Report copied to clipboard

Answer Baseline Questionnaire With Severe Symptom
    Select Answer Questionnaire
    Select Yes For Symptoms    Other symptom
    Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=new_entry
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Select Specific Answer To Question    How would you rate the severity of your symptom?    Severe
    ...    questionnaire=tnonc distress
    questionnaires.Select Specific Answer To Question    Have you used any medication to alleviate your symptoms?    No
    ...    questionnaire=tnonc distress
    Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic

Main success scenario - Part 1 - Pre-conditions
    Set Questionnaires Dates    ${date_format_API}    ${date_format_API}
    # Questionnaire scheduled but not yet answer, should be visible on "Unhandled tab"
    Send Questionnaire To The F01N14-3 Patient    ${current_date}    ${current_date_scheduled}    baseline
    Login As Patient    ${usecase_f01n14_3}[email]
    Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Mild
    Close Browser
    # Questionnaire scheduled and answered by patient, should be visible on "Other date"-tab, 7 days from current date
    Send Questionnaire To The F01N14-3 Patient    ${after_7_days}    ${after_7_days_scheduled}    treatmentVisit

Main success scenario - Part 2 - Pre-conditions
    Set Test Variable    @{mailosaur_keys}    ${f01n14_email_keys}[0]    ${f01n14_email_keys}[1]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Add An Activated Patient Under Default Clinic
    ...    f01n14
    ...    ${mailosaur_keys}[0]
    ...    ${AUTOMATED_TESTS_SUB_ID_F01N14}
    ...    female
    ...    ${CHEMO_18_SYMPTOMS}
    Send Symptom Questionnaire Via Api To Patient    baseline
    Login As Patient    ${patient_email}
    Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Mild    yes    yes
    Wait Until Page Contains    ${thank_you_for_your_answers_text}
    Try To Click Element    ${move_to_home_button}
    Close Browser

Main success scenario - Part 2 - Test Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Close Browser

Main success scenario - Part 3 - Test Setup
    Add An Activated Patient Under Default Clinic
    ...    f01n14
    ...    no
    ...    ${AUTOMATED_TESTS_SUB_ID_F01N14}
    ...    female
    ...    ${CHEMO_18_SYMPTOMS}
    Send Symptom Questionnaire Via Api To Patient    baseline
    Login As Patient    ${patient_email}
    Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Mild    yes    no
    Close Browser

Main success scenario - Part 3 - Test Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Close Browser

Send Questionnaire To The F01N14-3 Patient
    [Documentation]    This keyword is used specifically to clinic "TA clinic Automated_tests" due to this keyword "Send Symptom Questionnaire Via API" is more suitable in case no new patient is created
    [Arguments]    ${sending_date}    ${reviewing_date}    ${questionnaire_type}
    Generate Clinic Token
    ...    ${automated_tests_clinic}[f01n18_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    IF    '${ENVIRONMENT}' == 'test'
        Get Patient Main Treatment Module Id    ${usecase_f01n14_3}[id_test]
        Send Symptom Questionnaire Via API
        ...    ${usecase_f01n14_3}[id_test]
        ...    ${sending_date}
        ...    ${reviewing_date}
        ...    ${questionnaire_type}
    ELSE IF    '${ENVIRONMENT}' == 'staging'
        Get Patient Main Treatment Module Id    ${usecase_f01n14_3}[id_staging]
        Send Symptom Questionnaire Via API
        ...    ${usecase_f01n14_3}[id_staging]
        ...    ${sending_date}
        ...    ${reviewing_date}
        ...    ${questionnaire_type}
    END
    Sleep    90s

Clinic User Can Filter The Reports By Status Non-Completed
    Try To Click Element    ${non_completed_tab}
    Wait Until Element Is Not Visible    ${noona-loader}

Patient Cards At Least One Is Visible For F01N14 Care Team
    Wait Until Page Contains Element    ${patient_cards}
    ${patient_cards_counts}    Get Element Count    ${patient_cards}
    Should Be True    ${patient_cards_counts} > 1

Patient Reports Can Be Filtered By Another Date In ${number_of_days} Days
    [Documentation]    The keyword generate and select a random date in the future. Limitation: selected date should be in 2 months ahead. Please check keyword "Select Date From Other Date Filter" in work queue resource.
    ${date}    Generate A Random Date From Current Date    ${number_of_days} days    %d.%m.%Y
    Patient Reports Can Be Filtered By Date    ${date}

Clinic User Can Display The Symptom Report
    Wait Until Page Contains Element    ${opened_case_container}
    ${report_type_text}    Get Text    ${opened_case_info_header}
    Should Be Equal    ${report_type_text}    Symptom Report
    ${report_careteam}    Get Text    ${opened_case_assigned_careteam}
    ${report_creation_date}    Get Text    ${opened_case_creation_date}
    ${report_updated_date}    Get Text    ${opened_case_case_updated_date}
    ${report_priority}    Get Text    ${opened_case_case_priority}
    ${report_status}    Get Text    ${opened_case_status}
    ${report_assignee}    Get Text    ${opened_case_assignee}
    ${report_origin}    Get Text    ${opened_case_origin}
    &{case_details}    Create Dictionary
    Set Test Variable
    ...    &{case_details}
    ...    case_type=${report_type_text}
    ...    care_team=${report_careteam}
    ...    created=${report_creation_date}
    ...    updated=${report_updated_date}
    ...    priority=${report_priority}
    ...    status=${report_status}
    ...    assignee=${report_assignee}
    ...    origin=${report_origin}

Clinic User Can Click To Expand Reported Symptom One By One
    Wait Until Noona Loader Is Not Visible
    ${list_of_reported_symptoms}    Get Symptoms From Symptom Report Case
    Set Test Variable    ${list_of_reported_symptoms}
    ${reported_symptoms_ids}    Create List
    FOR    ${index}    IN RANGE    1    ${symptom_rows_count}+1
        ${attribute}    Get Element Attribute    (${symptom_report_symptom_row})[${index}]    data-testid
        Append To List    ${reported_symptoms_ids}    ${attribute}
    END
    Log    ${reported_symptoms_ids}
    Set Test Variable    ${reported_symptoms_ids}
    # TODO: Get all symptom details in each row such as: grading, symptoms days, trend

Symptom Row Is Expandable And Collapsable
    FOR    ${item}    IN    @{reported_symptoms_ids}
        Try To Click Element    //*[@data-testid="${item}"]//mat-expansion-panel-header
        Try To Click Element    //*[@data-testid="${item}"]//mat-expansion-panel-header
    END

F01N14 Patient Report Case Is No Longer Visible In Unhandled Filter Work Queue
    Select Patient Report Unhandled Filter
    Wait Until Page Does Not Contain    ${patient_name}

F01N14 Patient Report Case Was Handled And Visible On Close Cases Tab
    Go To Closed Cases Tab
    Select Care Team Filter In Closed Cases    ${f01n14_nurse_1_fullname}

F01P14 Patient Receives Email About Clinic Reply To Their Questionnaire
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${DEFAULT_CLINIC}
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    Set Test Variable    ${new_message_email_id}    ${message_data}[0]
    Delete A Message In Server    ${mailosaur_keys}[1]    ${new_message_email_id}

F01P14 Patient Receives Email After The Clinic Has Handled Their Submitted Questionnaire
    Patient Received An Email About Questionnaire Review    ${patient_email}    ${automated_tests_clinic}[name]

F01P14 Clinic User Sends Patient Report For Consultation From Another Clinic User In Another Care Team
    Consult A Care Team
    ...    Care team 1
    ...    ${automated_tests_clinic}[default_user_name]
    ...    ${patient_case_consultation_note}
    Wait Until Page Contains Element    //h5[text()="Consulted ${automated_tests_clinic}[default_user_name]"]
    Wait Until Page Contains Element    //p[text()="${patient_case_consultation_note}"]
    Return To Work Queue
    Select Care Team Filter In Work Queue    ${f01n14_nurse_1_fullname}
    Wait Until Element Is Visible
    ...    //noona-patient-card//*[contains(@class, "assignee") and text()=" ${automated_test_clinic_default_user_initials} "]
    Close Browser

The Case In Consultation Is Visible In Another Care Team Work Queue
    Login As Nurse    ${automated_tests_clinic}[default_user]
    Go To Patient Reports
    Wait Until Noona Loader Is Not Visible
    Unselect My Patients In Filter In Work Queue
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Care Team 1
    Select Questionnaire Type In Work Queue    Symptom questionnaire
    Patient Card Is Displayed In Current Work Queue    ${patient_name}
    Select Patient Card    ${patient_name}
    Case Is Assigned To Correct User    ${BASELINE_QUESTIONNAIRE}    ${automated_tests_clinic}[default_user_name]
    Close Case
