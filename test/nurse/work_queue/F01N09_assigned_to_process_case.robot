*** Settings ***
Documentation       F01N09 Clinic user can be assigned to process a case
...                 Preconditions:
...                 - Patient has contacted clinic
...                 - A case has been created for the patient

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources/mailosaur.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n09    clinic-web    work-queue


*** Variables ***
${patient-element}                  workqueue-0
${newcase-button}                   //*[@data-testid="new-case-btn"]
${casetype-dropdown-button}         case-type__select
${careteam-dropdown-button}         care-team__select
${case-description-textarea}        textarea-case-description
${newcase-form-dropdown}            //*[contains(@class,'ng-select-searchable')]
${newcase-assignee-field}           assignee__select
${save-exit-newcase-button}         //*[@data-testid='newCase--save__button']
${assigned-to-text}                 Assigned to
${processcase-continue-button}      //*[@data-testid='newCase--continue__button']
${description-textarea}             id:textarea-case-description
${other-text}                       Other
${assigned-user-text}               Clinic User
${open-case-element}                //*[contains(@data-testid, 'case-open')]
${case-initials-element}            //noona-patient-case-item-bar//*[contains(@class, 'assignee')]
${list-view-icon}                   css:.list-view-icon
${sort-assigned-column}             //th[8]
${first-row-element}                //tbody/tr[1]
${assign-to-me-text}                Assign to me
${unassign-me-text}                 Unassign me
${assign-to-me-button}              //*[@data-testid="assignment-button"]
${unassign-me-button}               //*[@data-testid="unassignment-button"]
${case-date-table}                  //tbody/tr[1]/td[5]//span
${case-date-open}                   (//div[contains(@class, 'subtitle')])[1]
${case-assigned-text}               Case assigned successfully
${patients-search}                  id:input-search-ssn-search
${patient_cases_tab}                tab-patient-messaging
${patient_name_element}             id:patient-name
${assign_to_cu}
...                                 //*[contains(@class, 'title') and contains(text(), 'Assigned to') ]/following-sibling::div[contains(text(), 'Clinic User')]
${f01n09_care_team}                 f01n09 care team
${case_created_toast}               Case created successfully
${select_all}                       Select all
${patient_case_list_item}           //noona-patient-case-list-item[1]/div
${referrals}                        Referrals
${save}                             save
${appontment_question}              Appointment Questions
${instructions}                     Instructions
${medication_instructions}          Medication Instructions
${closed}                           Closed
${my_patient}                       My patients
${paperwork}                        Paperwork
${wq_test}                          Work Queue Test
${pre-test_instructions}            Pre-Test Instructions
${question}                         Question
${lab_quest_test_result}            Lab Questions / Test Results
${cu}                               CU
${nurse_case_email}                 <EMAIL>


*** Test Cases ***
Clinic user can be assigned to process a case
    [Tags]    nms9-ver-68
    [Setup]    Close Open Cases    ${f01n09_patient_1}[ssn]
    Go To Work Queue
    Set Case Details    ${referrals}    care_team=${f01n09_care_team}
    Open A New Case    ${f01n09_patient_1}[ssn]    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Try To Click Element    ${back_to_button}
    Go To Work Queue
    Select Case Type Filter In Work Queue    ${select_all}
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Remove Patient Group Filters In Work Queue
    Remove Selected Primary Providers
    Select Patient Card    ${f01n09_patient_1}[name]
    Wait Until Page Contains    ${case-assigned-text}
    Try To Click Banner Message
    Case Assignee Is Correct    ${referrals}    ${assigned-user-text}
    Contact Patient Per Case    ${instructions}    message_template=none
    Wait Until Keyword Succeeds    9s    0.5s    Case Status Is Correct    ${referrals}    ${closed}
    Case Is Unassigned    ${referrals}

Extension A - Case automatic assignment when being created and processed
    [Tags]    nms9-ver-69
    [Setup]    Close Open Cases    ${f01n09-a_patient_1}[ssn]
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Clinic user creates a new case for a patient
    Clinic user completes the case creation form and click on the Save and exit button
    Case is assigned to no one
    Clinic user goes to the patient screen, opens the created case section and clicks on the "Process case" button
    Clinic user clicks on the Continue button
    Clinic user completes the form and clicks on the Save and exit button
    Case is now assigned to Clinic user
    Clinic user initials can be seen on the patient screen case section

Extension B.a - Clinic user assigns himself from workqueue
    [Tags]    nms9-ver-70
    [Setup]    Close Open Cases    ${f01n09-b_patient_1}[ssn]
    Go To Work Queue
    Set Case Details    ${medication_instructions}    care_team=${f01n09_care_team}
    Open A New Case    ${f01n09-b_patient_1}[ssn]    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Try To Click Element    ${back_to_button}
    Sleep    1
    Select Case Type Filter In Work Queue    ${select_all}
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Remove Patient Group Filters In Work Queue
    Remove Selected Primary Providers
    Click List View Button
    Click Assign To Me From List View    ${f01n09-b_patient_1}[ssn]
    Wait Until Page Contains    ${case-assigned-text}
    Case Assignee Is Correct    ${medication_instructions}    ${assigned-user-text}

Extension B.b - Clinic user assigns himself from patient screen
    [Tags]    nms9-ver-71
    [Setup]    Close Open Cases    ${f01n09-b_patient_1}[ssn]
    Go To    ${NURSE_LOGIN_URL}
    Accept All Cookies If Visible For Clinic
    Clinic user navigates to a patient screen, Patient cases tab, but without opening a case at the same time (for example from the patient search box)
    Clinic user opens an open case that is assigned to no one from the patient cases list
    Try To Click Element    ${patient_case_list_item}
    Case details are displayed
    Clinic user clicks on the "Assign to me" button
    A "Case assigned successfully" message is displayed
    Clinic user initials can be seen on the patient screen case section
    Action button in patient page case section changes to "Unassign me"

Extension B.c - Clinic user assigns himself from case email - Critical Case With Active Session
    [Tags]    nms9-ver-422-1    nms9-ver-422
    Delete All Messages In Server    ${f01n09_mailosaur_keys}[0]    ${f01n09_mailosaur_keys}[1]
    Add An Activated Patient Under Default Clinic    f01n09-bc    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09_BC}
    Login As Nurse
    Set Case Details    ${appontment_question}    care_team=${automated_tests_clinic}[f01n09_bc_care_team]
    Open A New Case    ${patient_ssn}    save
    ${link_to_case}    Care Team User Received An Email About A New Case
    ...    ${f01n09_mailosaur_keys}[0]
    ...    ${f01n09_mailosaur_keys}[1]
    ...    ${nurse_case_email}
    ...    priority=${case_priority}[critical]
    Go To    ${link_to_case}
    Accept All Cookies If Visible For Clinic
    Wait Until Page Contains    ${case_assigned_successfully_banner}
    Case Is Expanded    ${case_details}[case_type]
    Case Is Assigned To Correct User    ${case_details}[case_type]    ${automated_tests_clinic}[default_user_name]
    Close All App Instances
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B.c - Clinic user assigns himself from case email - Medium Case Without Active Session
    [Tags]    nms9-ver-422-2    nms9-ver-422
    Delete All Messages In Server    ${f01n09_mailosaur_keys}[0]    ${f01n09_mailosaur_keys}[1]
    Add An Activated Patient Under Default Clinic    f01n09-bc    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09_BC}
    Login As Nurse
    Set Case Details    ${medication_instructions}    care_team=${automated_tests_clinic}[f01n09_bc_care_team]
    Set To Dictionary    ${case_details}    case_priority=${case_priority}[medium]
    Open A New Case    ${patient_ssn}    save
    ${link_to_case}    Care Team User Received An Email About A New Case
    ...    ${f01n09_mailosaur_keys}[0]
    ...    ${f01n09_mailosaur_keys}[1]
    ...    ${nurse_case_email}
    ...    priority=${case_priority}[medium]
    Close Browser
    Open URL In Chrome    ${link_to_case}
    Accept All Cookies If Visible For Clinic
    Clinic User Logins From Link To Case And Keeps Logged In    ${automated_tests_clinic}[default_user]
    Wait Until Page Contains    ${case_assigned_successfully_banner}
    Case Is Expanded    ${case_details}[case_type]
    Case Is Assigned To Correct User    ${case_details}[case_type]    ${automated_tests_clinic}[default_user_name]
    Close All App Instances
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B.c - Clinic user assigns himself from case email - High Case And Assigned To Other User
    [Tags]    nms9-ver-422-3    nms9-ver-422
    Delete All Messages In Server    ${f01n09_mailosaur_keys}[0]    ${f01n09_mailosaur_keys}[1]
    Add An Activated Patient Under Default Clinic    f01n09-bc    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09_BC}
    Login As Nurse
    Set Case Details    ${lab_quest_test_result}    care_team=${automated_tests_clinic}[f01n09_bc_care_team]
    Set To Dictionary
    ...    ${case_details}
    ...    assigned_to=${automated_tests_clinic}[default_user_name]
    ...    case_priority=${case_priority}[high]
    Open A New Case    ${patient_ssn}    save
    ${link_to_case}    Care Team User Received An Email About A New Case
    ...    ${f01n09_mailosaur_keys}[0]
    ...    ${f01n09_mailosaur_keys}[1]
    ...    ${nurse_case_email}
    ...    priority=${case_priority}[high]
    Close Browser
    Open URL In Chrome    ${link_to_case}
    Accept All Cookies If Visible For Clinic
    Clinic User Logins From Link To Case And Keeps Logged In    ${automated_tests_clinic}[default_manager]
    Case Is Expanded    ${case_details}[case_type]
    Wait Until Page Contains    ${patient_is_already_assigned_banner}
    Page Should Not Contain    ${case_assigned_successfully_banner}
    Case Is Assigned To Correct User    ${case_details}[case_type]    ${automated_tests_clinic}[default_user_name]
    Close All App Instances
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension C - Clinic user needs to ask consultation
    [Tags]    nms9-ver-72
    [Setup]    Login As Nurse
    ${status}    Run Keyword And Return Status    Wait Until Page Contains    ${patient-element}
    IF    ${status}    Select Case Type Filter In Work Queue    ${select_all}
    Select My Patients In Work Queue
    Sleep    1s
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Select Case Type Filter In Work Queue    Select all
    Remove Patient Group Filters In Work Queue
    Remove Selected Primary Providers
    Open First Workqueue Item
    Case is now assigned to Clinic user
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Try To Click Element    ${back_to_button}
    Go To Work Queue
    Remove All Care Team Filter
    Select Case Type Filter In Work Queue    ${select_all}
    Select My Patients In Work Queue
    Sleep    1s
    Open First Workqueue Item
    Case is now assigned to Clinic user

Extension D - Clinic user unassigns himself
    [Tags]    nms9-ver-73
    [Setup]    Close Open Cases    ${f01n09-d_patient_1}[ssn]
    Go To Work Queue
    Set Case Details    ${paperwork}    care_team=${f01n09_care_team}
    Open A New Case    ${f01n09-d_patient_1}[ssn]    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Try To Click Element    ${back_to_button}
    Go To Work Queue
    Select Case Type Filter In Work Queue    ${select_all}
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Remove Patient Group Filters In Work Queue
    Remove Selected Primary Providers
    Select Patient Card    ${f01n09-d_patient_1}[name]
    Wait Until Page Contains    ${case-assigned-text}
    Case Assignee Is Correct    ${paperwork}    ${assigned-user-text}
    Unassign Nurse From Case
    Case Is Unassigned    ${paperwork}
    Page Should Contain    ${assign-to-me-text}
    [Teardown]    Close Case

Extension E - Clinic user is unassigned by other Clinic user
    [Tags]    nms9-ver-74
    Add An Activated Patient Under Default Clinic    f01n09-exte    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09}
    Login As Nurse
    Set Case Details    ${pre-test_instructions}    care_team=${f01n09_care_team}
    Open A New Case    ${patient_ssn}    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Assign Nurse To Case
    Close Browser
    Login As Nurse    ${automated_tests_clinic}[wq_user]
    Remove Patient Group Filters In Work Queue
    Unselect My Patients In Filter In Work Queue
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Assign Nurse To Case
    Page Should Contain    ${unassign-me-text}
    Select First Open Case From List
    Case Assignee Is Correct    ${pre-test_instructions}    ${wq_test}
    Select First Open Case From List
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension F - Patient is replied by other Clinic user
    [Tags]    nms9-ver-75
    Add An Activated Patient Under Default Clinic    f01n09-extf    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09}
    Login As Nurse    ${automated_tests_clinic}[wq_user]
    Set Case Details    ${appontment_question}    care_team=${f01n09_care_team}
    Open A New Case    ${patient_ssn}    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Assign Nurse To Case
    Close Browser
    Login As Nurse    ${automated_tests_clinic}[wq_user]
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Remove Patient Group Filters In Work Queue
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Contact Patient Per Case    ${instructions}    message_template=none
    Wait Until Page Contains    Information saved
    Wait Until Page Does Not Contain    Information saved
    Case Status Is Correct    ${appontment_question}    ${closed}
    Case Is Unassigned    ${appontment_question}
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension G - Patient replies to Clinic user
    [Tags]    nms9-ver-76
    Add An Activated Patient Under Default Clinic
    ...    f01n09-extg
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N09}
    Login As Nurse
    Set Case Details    ${lab_quest_test_result}    care_team=${f01n09_care_team}
    Open A New Case     ${patient_ssn}    ${save}
    Wait Until Page Contains    ${case_created_toast}
    Contact Patient Per Case    ${question}    message_template=none
    Close Browser
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Reply To Clinic Message    Test response to clinic for f01n09-G.
    Close Browser
    Login As Nurse
    Select Case Type Filter In Work Queue    ${select_all}
    Select Care Team Filter In Work Queue    ${f01n09_care_team}
    Remove Patient Group Filters In Work Queue
    Remove Selected Primary Providers
    Patient Card Is Displayed In Current Work Queue     ${family_name}
    Nurse Initials Is Displayed In Patient Card         ${family_name}    ${cu}
    Work Queue Nurse Initials Background Color Is Correct    ${patient_ssn}    rgba(0, 0, 0, 0)
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Clinic user creates a new case for a patient
    Search Patient By Identity Code    ${f01n09-a_patient_1}[ssn]
    Wait Until Page Contains Element    ${newcase-button}
    Try To Click Element    ${newcase-button}

Clinic user completes the case creation form and click on the Save and exit button
    Try To Click Element    ${casetype-dropdown-button}
    Try To Click Element
    ...    ${newcase-form-dropdown}//*[contains(@class,'ng-star-inserted')]//*[contains(text(), 'Other')]
    Try To Click Element    ${careteam-dropdown-button}
    Try To Click Element    (${newcase-form-dropdown}//*[contains(@class,'scroll-host')]/div//*)[1]
    Try To Input Text    ${description-textarea}    autotest case description
    Try To Click Element    ${save-exit-newcase-button}
    Try To Click Banner Message

Case is assigned to no one
    Page Should Not Contain    ${assigned-to-text}

Clinic user goes to the patient screen, opens the created case section and clicks on the "Process case" button
    Wait Until Page Contains Element    ${newcase-button}
    Try To Click Element    ${process_case_button}

Clinic user clicks on the Continue button
    Try To Click Element    ${processcase-continue-button}

Clinic user completes the form and clicks on the Save and exit button
    Try To Click Element    ${save-exit-newcase-button}

Case is now assigned to Clinic user
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${assign_to_cu}
    IF    ${status} != True
        Wait Until Element Is Visible    ${open-case-element}
        Wait Until Page Contains    ${assigned-to-text}
        Page Should Contain    ${assigned-user-text}
    END

Clinic user initials can be seen on the patient screen case section
    Wait Until Element Contains    (${case-initials-element})[1]    ${assigned-user-text}

Clinic user navigates to a patient screen, Patient cases tab, but without opening a case at the same time (for example from the patient search box)
    Search Patient By Identity Code    ${f01n09-b_patient_1}[ssn]
    Wait Until Location Contains    handle-patient
    Wait Until Page Contains    ${f01n09-b_patient_1}[ssn]
    Wait Until Element Contains    ${patient_name_element}    ${f01n09-b_patient_1}[name]
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Try To Click Element    ${patient_cases_tab}

Clinic user opens an open case that is assigned to no one from the patient cases list
    Try To Click Element    ${newcase-button}
    Clinic user completes the case creation form and click on the Save and exit button
    Case is assigned to no one

Case details are displayed
    Wait Until Element Is Visible    //*[contains(@class, 'case-information-section')]

Clinic user clicks on the "Assign to me" button
    Wait Until Keyword Succeeds    9    1    Click Button    ${assign-to-me-button}

A "Case assigned successfully" message is displayed
    Wait Until Element Is Visible    css:.toast-message
    Page Should Contain    ${case-assigned-text}

Action button in patient page case section changes to "Unassign me"
    Wait Until Keyword Succeeds
    ...    9
    ...    1
    ...    Element Should Contain
    ...    ${unassign-me-button}
    ...    ${unassign-me-text}
    ...    ignore_case=True

Set Case Details
    [Arguments]    ${case_type}    ${care_team}=Care Team 1
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${care_team}
    ...    case_description=Test case description    case_priority=Critical
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}

Clinic User Logins From Link To Case And Keeps Logged In
    [Arguments]    ${clinic_user_email}
    Login As Nurse From Landing Page    ${clinic_user_email}
    Wait until keyword succeeds    3x    1s    Keep Me Logged In    Yes
