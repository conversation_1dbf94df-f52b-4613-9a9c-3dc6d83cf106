*** Settings ***
Documentation       F10N01 Nurse can view a questionnaire answer
...                 Preconditions:
...                 - <PERSON><PERSON> has scheduled a questionnaire and questionnaire is sent to patient and patient has answered and saved his/her answer.
...                 - Questionnaire content is found in Questionnaires and related rules.
...                 NOTE:
...                 - Extension A is currently blocked by NOONA-10252

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10n01    clinic-web


*** Test Cases ***
Nurse can view a questionnaire answer
    [Tags]    nms9-ver-263
    Add An Activated Patient Under Default Clinic
    ...    f10n01
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    Quality of life (15D)
    Close Browser
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Complete QOL Questionnaire
    questionnaires.Save Questionnaire    Quality of life (15D)
    Close Browser
    Login As Nurse
    Search Patient By Identity Code      ${patient_ssn}
    Navigate To Patient Cases Tab
    Select Case From Patient Cases Tab    QUALITY OF LIFE QUESTIONNAIRE (15D©)
    QOL Summary Is Displayed In Patient Case
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Questionnaire answer with message goes in work queue
    [Tags]    nms9-ver-264
    Add An Activated Patient Under Default Clinic    f10n01-exta    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F10N01}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Close Browser
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Navigate To Clinic
    Select Latest Clinic Message
    ${date}    Get Current Date    result_format=%d.%m.%Y
    Reply To Clinic Message    Sending message about QOL at ${date}    new_message=yes
    Close Browser
    Login As Nurse
    Sleep    1
    Go To Patient Reports
    Unselect My Patients In Filter In Work Queue
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f10n01_care_team]
    Select Questionnaire Type In Work Queue    QUALITY OF LIFE QUESTIONNAIRE (15D©)
    ${name_of_patient}    Set Variable    ${first_name}${SPACE}${family_name}
    Patient Card Is Displayed In Current Work Queue    ${name_of_patient}
    Patient Card's Color Is Correct    medium    questionnaire=yes
    ##teardown
    Select Patient Card    ${name_of_patient}
    Close Case
    Wait Until Page Contains    Closed
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extention B - Nurse can view an answered questionnaire after the appointment is deleted
    [Tags]    nms9-ver-400
    # setup start
    Login As Nurse    ${appointment_clinic}[user_email]
    Close Cases With Outcome Per Care Team    open cases    ${appointment_clinic}[f10n01_care_team]
    Close Cases With Outcome Per Care Team    patient reports    ${appointment_clinic}[f10n01_care_team]
    Close All Browsers
    # setup end
    Create New Patient And Send Appointment With Symptom Questionnaire
    Answer Symptom Questionnaire From Appointment As Patient
    Login As Nurse    ${appointment_clinic}[user_email]
    Go To Open Cases Tab And Verify Case In Work Queue
    Go To Patient Reports Tab And Veify Case In Work Queue
    Close All Browsers
    Appointment Teardown    ${appointment_id}    ${patient_mrn}
    Login As Nurse    ${appointment_clinic}[user_email]
    Go To Open Cases Tab And Verify Case In Work Queue
    Go To Patient Reports Tab And Veify Case In Work Queue
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}


*** Keywords ***
Create New Patient And Send Appointment With Symptom Questionnaire
    Add An Activated Patient Under Appointment Clinic
    ...    f10n01-b
    ...    subscriber_id=${APPOINTMENT_CLINIC_SUB_ID_F10N01}
    ...    module=${CHEMO_11}
    Set Appointment Date In The Future    2 days
    Send Appointments Via FHIR API    777    test2    ${patient_id}

Answer Symptom Questionnaire From Appointment As Patient
    Login As Patient    ${patient_email}
    Wait Until Page Contains    ${upcoming_events_header_text}
    Reload And Select Clinic Message When It Appears
    Select Answer Questionnaire Button
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Complete Other Symptom Questionnaire With Severe Symptom
    Emergency Priority Symptom Is Displayed
    Select Latest Clinic Message
    Wait Until Page Contains    ${severe_symptom_inbox_text}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}

Go To Open Cases Tab And Verify Case In Work Queue
    # list view
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${appointment_clinic}[f10n01_care_team]
    Select Case Type Filter In Work Queue    ${case_type_filters}[symptom_from_questionnaire]
    Remove Patient Group Filters In Current Work Queue
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Patient Card's Color Is Correct    critical
    Click List View Button
    Case Type Is Displayed in List View    ${patient_mrn}    ${case_type_filters}[symptom_from_questionnaire]
    # card view
    Click Card View Button
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Case Type Header Is Displayed Correctly    ${case_type_filters}[symptom_from_questionnaire]
    Get Case Date
    Should Contain    ${case_date}    ${current_date}
    Return To Patients

Go To Patient Reports Tab And Veify Case In Work Queue
    # list view
    Go To Patient Reports
    Select Patient Report Unhandled Filter
    Click List View Button
    Remove All Care Team Filter
    Remove Patient Group Filters In Current Work Queue
    Select Care Team Filter In Work Queue    ${appointment_clinic}[f10n01_care_team]
    Select Questionnaire Type In Work Queue    ${questionnaire_type_filters}[symptom_questionnaire]
    Questionnaire Type Is Displayed In List View    ${patient_mrn}    ${questionnaire_type_filters}[symptom_report]
    Click Card View Button
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Patient Card's Color Is Correct    critical
    # card view
    Click Card View Button
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Case Type Header Is Displayed Correctly    ${questionnaire_type_filters}[symptom_report]
    Get Case Date
    Should Contain    ${case_date}    ${current_date}
