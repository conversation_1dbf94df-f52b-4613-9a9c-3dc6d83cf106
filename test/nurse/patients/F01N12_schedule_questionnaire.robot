*** Settings ***
Documentation       [F01N12] Nurse can schedule a questionnaire or message to be sent to a patient
...                 Test:
...                 - Adds a new scheduled questionnaire for a random patient
...                 - Patient completes and checks the summary (text and number inputs) of questionnaire
...                 - Saves and checks the status of questionnaire
...                 - Checks saved summary (text and number inputs)

Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n12    clinic-web


*** Variables ***
${last_scheduled_content}       (//*[starts-with(@data-testid, 'symptom-inquiry_')])[last()]
${template}                     Auto test template
${template_questionnaire}       Baseline questionnaire


*** Test Cases ***
Create And Complete IPQ Questionnaire
    [Tags]    nms9-ver-78
    Search Patient By Identity Code    ${f01n12_questionnaire_patient}[ssn]
    Add Questionnaire To Schedule    IPQ
    Login As Patient    email=${f01n12_questionnaire_patient}[email]
    questionnaires.Complete Questionnaire    IPQ
    questionnaires.Save Questionnaire    IPQ
    questionnaires.Check Saved Questionnaire    IPQ

Extension A - Remove Scheduled Questionnaire
    [Tags]    nms9-ver-82
    Schedule Questionnaire
    Questionnaire Status Is Correct    IPQ    SCHEDULED
    Remove Questionnaire
    Scheduled Questionnaire Is Removed    IPQ

Extension B - Schedule More Content Using A Schedule Template
    [Tags]    nms9-ver-83
    Search Patient By Identity Code    ${f01n12_ext_b_patient}[ssn]
    Edit Module    Acute Leukemia and MDS
    Use Template And Apply
    Template Added To Scheduled Content
    Save Module
    Remove Questionnaire


*** Keywords ***
Schedule Questionnaire
    Random Patient From The List
    Search Patient By Identity Code    ${f01n12_questionnaire_patient}[ssn]
    Schedule Questionnaire For Tomorrow    IPQ
    Try To Click Banner Message

Use Template And Apply
    Use Template For Scheduled Content    ${template}
    ${date}    Get Current Date    result_format=%d.%m.%Y
    Select Current Date For Scheduled Template    ${date}
    Try To Click Element    ${scheduled_template_apply_button}

Template Added To Scheduled Content
    Wait Until Page Contains Element    ${last_scheduled_content}
    Element Should Contain    ${last_scheduled_content}    ${template_questionnaire}
    Page Should Contain Element    ${last_scheduled_content}/following-sibling::div/div[text()=' Scheduled ']

Save Module
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Try To Click Banner Message
