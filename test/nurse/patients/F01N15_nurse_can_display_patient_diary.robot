*** Settings ***
Documentation       F01N15 Nurse can display patient diary
...                 Preconditions:
...                 - Patient has made entries to diary.
...                 Note: To test the other features, comment lines 35 and 36 - it's where the bug is

Resource            ${EXECDIR}${/}resources/patient/diary_add_menu/shared_add_menu.resource
Resource            ${EXECDIR}${/}resources/patient/clinic.resource
Resource            ${EXECDIR}${/}resources/patient/login.resource
Resource            ${EXECDIR}${/}resources/nurse/login.resource
Resource            ${EXECDIR}${/}resources/nurse/timeline.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_management.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_cases.resource
Resource            ${EXECDIR}${/}resources/patient/upload_or_remove_photo.resource

Suite Setup         Set Libraries Order
Test Setup          Set Dates
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n15    clinic-web


*** Test Cases ***
Nurse can display patient diary
    [Tags]    nms9-ver-91-1    nms9-ver-91    photo-upload
    Patient Has Made An Entry To Diary
    Login As Nurse
    Search Patient By Identity Code    ${f01n15_patient}[ssn]
    Wait Until Element Is Visible    ${patient_cases_list}
    Navigate To Timeline Tab
    Correct Timeline Month Is Displayed    ${current_month_year}
    Symptom Is Visible Under Summary Tab    ${OTHER_SYMPTOM}
    Symptom Entry Is Listed Below Symptom Type    ${OTHER_SYMPTOM}
    Symptom Details Is Displayed Under Symptom Entry    photo_uploaded=yes
    Symptom Tooltip Is Displayed    ${OTHER_SYMPTOM}
    Individual Information Is Listed For Multiple Entries

No Entries Are Made Label Is Displayed
    [Tags]    nms9-ver-91-2    nms9-ver-91
    Login As Nurse
    Search Patient By Identity Code    ${f01n15_patient_2}[ssn]
    Navigate To Timeline Tab
    Go To Timeline Previous Month
    Correct Timeline Month Is Displayed    ${previous_month_year}
    ${status}    Run Keyword And Return Status    Timeline Entry Container Is Visible
    IF    ${status}==${False}
        Wait Until Page Contains    ${no_reported_symptoms_text}
    END


*** Keywords ***
Patient Has Made An Entry To Diary
    Login As Patient    email=${f01n15_patient}[email]
    Navigate to Clinic
    Ask About Other Symptom    file_example_JPG_100kB.jpg

Set Dates
    ${date}    Get Current Date
    ${current_month_year}    Convert Date    ${date}    result_format=%B %Y
    ${symptom_circle_id_date}    Convert Date    ${date}    result_format=%Y-%m-%d
    ${current_year}    Convert Date    ${date}    result_format=%Y
    ${current_month}    Convert Date    ${date}    result_format=%-m
    ${previous_month}    Evaluate    ${current_month}-1
    IF    ${previous_month}==0
        ${previous_year}    Evaluate    ${current_year}-1
        ${previous_month_year}    Convert Date    12.${previous_year}    date_format=%m.%Y    result_format=%B %Y
    ELSE
        ${previous_month_year}    Convert Date
        ...    ${previous_month}.${current_year}
        ...    date_format=%m.%Y
        ...    result_format=%B %Y
    END
    Set Test Variable    ${previous_month_year}
    Set Test Variable    ${current_month_year}
    Set Test Variable    ${symptom_circle_id_date}
