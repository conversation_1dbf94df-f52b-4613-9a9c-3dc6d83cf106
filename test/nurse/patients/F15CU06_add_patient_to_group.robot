*** Settings ***
Documentation       F15CU06 Clinic user can add patient to groups
...                 Test clinic: TA Clinic Automated_tests_4

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_group.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources/nurse/case_management.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu06    clinic-web


*** Variables ***
${noona_work_queue}             //*[@class='noona-work-queue']
${id_column_elements}           ${noona_work_queue}/descendant::tbody/tr/td[3]
${patient_group_sort_column}    ${noona_work_queue}//span[text()="Patient Group"]
${first_patient_group_colum}    ${noona_work_queue}//table//tbody//tr[1]/td[4]


*** Test Cases ***
Add Patient To Groups
    [Documentation]    Patient should have one open case to be searchable in wq and one existing patient group
    ...    Also includes: Extension A - Filter And Sort Work Queue Based On Patient Groups
    [Tags]    nms9-ver-323    nms9-ver-324
    [Setup]    Remove All Patient Groups
    # nms9-ver-323
    Add New Patient Category And Two Groups
    Search Patient By Identity Code    ${f15cu06_no_mc_patient}[ssn]
    Add Patient To Groups
    Patient Is Added To Groups
    # nms9-ver-324
    Navigate To Work Queue
    Remove Patient Group Filters In Work Queue
    Select Case Type Filter In Work Queue    Select all
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Care Team 1
    Sort By Patient Group
    Click Card View Button
    Select Patient Group Filter In Work Queue    ${random_group_name}
    Filtered Patient Is Shown And Nothing Else
    # nms9-ver-323
    Search Patient By Identity Code    ${f15cu06_no_mc_patient}[ssn]
    Remove Groups From Patient
    Patient Is Removed From The Group


*** Keywords ***
Add New Patient Category And Two Groups
    Add New Category
    Set Category Color
    Add Group To Category
    Add New Group
    patient_group.Save Changes

Find Patient
    Try To Click Element    ${patients_menu}
    Search Patient By Identity Code    ${f15cu06_no_mc_patient}[ssn]

Filtered Patient Is Shown And Nothing Else
    Click List View Button
    Wait Until Page Contains Element    ${id_column_elements}
    Sleep    1
    @{id_webelements}    Get WebElements    ${id_column_elements}
    FOR    ${id_element}    IN    @{id_webelements}
        Element Should Contain    ${id_element}    ${f15cu06_no_mc_patient}[ssn]
    END

Sort By Patient Group
    Click List View Button
    Wait Until Page Contains Element    ${patient_group_sort_column}
    Try To Click Element    ${patient_group_sort_column}
    Wait Until Element Contains    ${first_patient_group_colum}    ${random_group_name}
    Try To Click Element    ${patient_group_sort_column}
    Wait Until Element Does Not Contain    ${first_patient_group_colum}    ${random_group_name}

Remove All Patient Groups
    Login As Nurse    email=${automated_tests_4_clinic}[manager_email]    clinic=${automated_tests_4_clinic}[name]
    Navigate To Patient Groups
    ${remove_button_visible}    Run Keyword And Return Status    Wait Until Element Is Visible    ${remove_button}
    WHILE    ${remove_button_visible}==${TRUE}    limit=5
        Remove Category
        ${remove_button_visible}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${remove_button}
        ...    timeout=3s
    END
    patient_group.Save Changes
