*** Settings ***
Documentation       F01N18 Clinic user can reassign a case
...                 TA clinic Automated_tests
...                 Clinic users are in care team: F01N18
...                 <EMAIL> (default)/ <EMAIL>/ <EMAIL>
...                 Work queue is always all set to diplay cases of all care teams and clinic users

Resource            ${EXECDIR}${/}resources${/}nurse${/}list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_messaging_page.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          clinic-web    usecase-f01n18


*** Variables ***
${workqueue_container}                      //*[contains(@class, "workqueue_contents")]
${workqueue_table}                          //table[contains(@class, "noona-table")]
${first_case_in_workqueue}                  ${workqueue_table}//tbody//tr[1]
${opened_case_container}                    //*[contains(@data-testid, "case-open")]//section[contains(@class, "patient-case-list-item")]
${opened_case_creation_date}                ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[1]//div[@class="subtitle"]
${opened_case_assignee}                     ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[6]//div[@class="subtitle"]
${opened_case_assigned_careteam}            ${opened_case_container}//div[contains(@class, "case-information-section")]//mat-grid-list[1]//mat-grid-tile[5]//div[@class="subtitle"]
${reassign_case_button}                     //*[@data-testid="re-assign-case-button"]
${reassign_case_modal}                      //*[@data-testid="reassign-case-modal"]
${select_assignee_input}                    //*[@data-testid="assignee"]
${reassign_case_modal_header_text}          Reassign case
${reassign_case_modal_subheader_text}       Select a new assignee or transfer this to a different care team
${reassign_case_modal_header}               //h4[text()="${reassign_case_modal_header_text}"]
${reassign_case_modal_subheader}            //p[@class="explanation"]
${reassign_case_assignee_selection_list}    //*[@aria-label="Options list"]
${reassign_case_assignee_option_element}    //*[contains(@data-testid, "option-")]//div
${reassign_case_textarea}                   //textarea[@data-testid="note"]
${reassign_case_save_button}                //button[@data-test-id="reassign-save"]
${reassign_case_cancel_button}              //button[@data-test-id="reassign-cancel"]
${current_care_team}                        F01N18
${reassigned_care_team}                     Sample Care


*** Test Cases ***
Main success scenario - Open cases - Reassign case to another clinic user
    [Documentation]    The test covers happy path: clinic user successfully reassign a case to another clinic user.
    [Tags]    nms9-ver-446-1    nms9-ver-446
    [Setup]    Main success scenario - Pre-conditions
    Clinic User Selects First Open Case From Work Queue Of Their Current Care Team
    Patient Case Page Is Opened
    Clinic User Name And Timestamp Are Display In Case
    Clinic User Presses Reassign Case
    # Reassign a case to a clinic user
    Clinic User Reassigns Case To Another Clinic User
    # Type notes
    Clinic User Types Reassign Case Note To A Clinic User
    Clinic User Saves Reassign Case Modal
    Case Has New Assignee And Is Seen In Workqueue
    [Teardown]    Run Keywords    Main success scenario - Test Teardown    AND    Close All Browsers

Main success scenario - Open cases - Reassign case to another care team
    [Documentation]    The test covers 2 flows: clinic user cancels reassignment and clinic user assigns case to another care team.
    [Tags]    nms9-ver-446-2    nms9-ver-446
    [Setup]    Main success scenario - Pre-conditions
    # Reassign again but cancel the modal
    Clinic User Selects First Open Case From Work Queue Of Their Current Care Team
    Patient Case Page Is Opened
    Clinic User Presses Reassign Case
    Clinic User Reassigns Case To Another Care Team
    Clinic User Types Reassign Case Note To A Care Team
    Clinic User Cancels Reassign Case Modal
    Clinic User Sees The Change Is Not Saved And Reassign Modal Is Closed
    # Reassign to another care team
    Clinic User Returns To Workqueue    Open cases
    Select First Case From Workqueue
    Patient Case Page Is Opened
    Clinic User Name And Timestamp Are Display In Case
    Clinic User Presses Reassign Case
    Clinic User Reassigns Case To Another Care Team
    Clinic User Types Reassign Case Note To A Care Team
    Clinic User Saves Reassign Case Modal
    Case Is Assigned To A New Care Team
    Clinic User Returns To Workqueue    Open cases
    [Teardown]    Close All Browsers
    # Test can stop when user returns to the workqueue, new care team assignment verification was done in the patient case view.
    # See keyword "Case Is Assigned to A New Care Team" in the Keywords-section

Main success scenario - Reassign Patient Reports
    [Documentation]    This test covers test flow where clinic users opens a case from patient reported questionnaire and reassign the case
    ...    to another clinic user in the same care team. Reassigment to another care team is covered in nms9-ver-446-2
    [Tags]    nms9-ver-446-3    nms9-ver-446
    [Setup]    Reassign Patient Reports - Pre-conditions
    Clinic User Selects First Patient Report From Work Queue Of Their Current Care Team
    Patient Case Page Is Opened
    Clinic User Name And Timestamp Are Display In Case
    Clinic User Presses Reassign Case
    # Reassign a case to a clinic user
    Clinic User Reassigns Case To Another Clinic User
    # Type notes
    Clinic User Types Reassign Case Note To A Clinic User
    Clinic User Saves Reassign Case Modal
    Patient Report Has New Assignee And Is Seen In Workqueue
    [Teardown]    Run Keywords    Main success scenario - Test Teardown    AND    Close All Browsers


*** Keywords ***
Main success scenario - Pre-conditions
    Login As Patient    ${f01n18_patient}[email]
    Navigate To Clinic
    Ask About Other Symptom
    Close Browser

Reassign Patient Reports - Pre-conditions
    Send A Quality Of Life (QOL) QL15 Questionnaire To The Patient
    The Patient Completes Quality Of Life Questionnaire QL15
    Close Browser

Clinic User Selects First ${case_type} From Work Queue Of Their Current Care Team
    Login As Nurse    ${automated_tests_clinic}[f01n18_user]
    Wait Until Patient Sees The Workqueue Is Fully Loaded
    # Remove all default filter before selecting care team and case type filter in order to avoid overlapped filter from other test cases
    Unselect My Patients In Filter In Work Queue
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Remove Case Type Filters In Work Queue
    Select Care Team Filter In Work Queue    ${current_care_team}
    IF    '${case_type}' == 'Open Case'
        Select Case Type Filter In Work Queue    Message about a symptom
    ELSE IF    '${case_type}' == 'Patient Report'
        Go To Patient Reports
        Select Patient Report Unhandled Filter
        Select Questionnaire Type In Work Queue    QUALITY OF LIFE QUESTIONNAIRE (15D©)
    END
    Toggle Work Queue List View
    IF    '${case_type}' == 'Open Case'
        Get Open Case Information    1
    ELSE IF    '${case_type}' == 'Patient Report'
        Get Patient Report Information    1
    END
    Select First Case From Workqueue

Main success scenario - Test Teardown
    Go To    ${case_location}
    Patient Case Page Is Opened
    Close Case

Select First Case From Workqueue
    Try To Click Element    ${first_case_in_workqueue}

Toggle Work Queue List View
    ${workqueue_table_visible}    Run Keyword And Return Status    Element Should Be Visible    ${workqueue_table}
    IF    '${workqueue_table_visible}' == 'False'
        Try To Click Element    ${list_view_button}
    END
    Wait Until Element Is Visible    ${workqueue_table}
    Wait Until Noona Loader Is Not Visible

Get Open Case Information
    [Arguments]    ${index}
    ${case_patient_name}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[2]//p
    ${case_opened_date}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[6]//p
    ${case_care_team}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[8]//p
    ${case_assignee}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[10]//p
    @{assignee_names}    Split String    ${case_assignee}    ${SPACE}
    ${formatted_assignee_name}    Format String    ${assignee_names}[1] ${assignee_names}[0]
    Log    ${assignee_names}
    @{patient_case_information}    Create List
    ...    ${case_patient_name}
    ...    ${case_opened_date}
    ...    ${case_care_team}
    ...    ${formatted_assignee_name}
    Set Test Variable    ${case_patient_name}
    Set Test Variable    ${case_opened_date}
    Set Test Variable    ${patient_case_information}

Get Patient Report Information
    [Arguments]    ${index}
    Wait Until Element Is Visible    ${workqueue_table}//tbody//tr[${index}]//td[2]//p
    Wait Until Element Is Enabled    ${workqueue_table}//tbody//tr[${index}]//td[2]//p
    ${case_patient_name}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[2]//p
    ${report_date}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[6]//p
    ${report_care_team}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[8]//p
    ${report_assignee}    Get Text    ${workqueue_table}//tbody//tr[${index}]//td[10]//p
    @{assignee_names}    Split String    ${report_assignee}    ${SPACE}
    ${formatted_assignee_name}    Format String    ${assignee_names}[1] ${assignee_names}[0]
    Log    ${assignee_names}
    @{patient_report_case_information}    Create List
    ...    ${case_patient_name}
    ...    ${report_date}
    ...    ${report_care_team}
    ...    ${formatted_assignee_name}
    Set Test Variable    ${case_patient_name}
    Set Test Variable    ${report_date}
    Set Test Variable    ${patient_report_case_information}

Patient Case Page Is Opened
    Wait Until Location Contains    ${PATIENT_CASE_PATH}
    Location Should Contain    ${PATIENT_CASE_PATH}
    ${case_location}    Get Location
    Log    ${case_location}
    Set Test Variable    ${case_location}
    Wait Until Element Is Visible    ${opened_case_container}
    Wait Until Page Loader Is Not Visible

Wait Until Patient Sees The Workqueue Is Fully Loaded
    Wait Until Page Loader Is Not Visible
    Wait Until Element Is Visible    ${workqueue_container}

Clinic User Name And Timestamp Are Display In Case
    Wait Until Element Is Visible    ${opened_case_container}
    ${case_timestamp}    Get Text    ${opened_case_creation_date}
    ${case_current_assignee}    Get Text    ${opened_case_assignee}

Clinic User Presses Reassign Case
    Wait Until Element Is Visible    ${reassign_case_button}
    Try To Click Element    ${reassign_case_button}
    Wait Until Element Is Visible    ${reassign_case_modal}
    Element Text Should Be    ${reassign_case_modal_header}    ${reassign_case_modal_header_text}
    Element Text Should Be    ${reassign_case_modal_subheader}    ${reassign_case_modal_subheader_text}

Clinic User Reassigns Case To Another Clinic User
    Try To Click Element    ${select_assignee_input}
    Get List Of Clinic Users And Care Teams From Reassign Modal
    Select A Random Assignee From List

Clinic User Reassigns Case To Another Care Team
    Try To Click Element    ${select_assignee_input}
    Try To Click Element    //*[@title="${reassigned_care_team}"]

Get List Of Clinic Users And Care Teams From Reassign Modal
    Wait Until Page Contains Element    ${reassign_case_assignee_selection_list}
    ${number_of_options}    Get Element Count    ${reassign_case_assignee_option_element}
    @{list_options}    Create List
    FOR    ${index}    IN RANGE    0    ${number_of_options}
        ${option_text}    Get Text    //*[contains(@data-testid, "option-${index}")]//div
        Append To List    ${list_options}    ${option_text}
    END
    ${careteam_option_index}    Get Index From List    ${list_options}    Care teams    0
    ${list_of_clinic_users}    Get Slice From List    ${list_options}    0    ${careteam_option_index}
    # Remove "Care teams" and "Clinic Users" from list since they are not selectable
    Remove Values From List    ${list_of_clinic_users}    Clinic users
    Set Test Variable    ${list_of_clinic_users}

Select A Random Assignee From List
    ${random_assignee}    Evaluate    random.choice(${list_of_clinic_users})    random
    Set Test Variable    ${random_assignee}
    Try To Click Element    //*[@title="${random_assignee}"]

Clinic User Types Reassign Case Note To ${type_of_assignee}
    IF    '${type_of_assignee}' == 'A Clinic User'
        Input Text    ${reassign_case_textarea}    This case is reassigned to ${random_assignee}.
    ELSE IF    '${type_of_assignee}' == 'A Care Team'
        Input Text    ${reassign_case_textarea}    This case is reassigned to ${reassigned_care_team}.
    END

Clinic User Saves Reassign Case Modal
    Try To Click Element    ${reassign_case_save_button}

Clinic User Cancels Reassign Case Modal
    Try To Click Element    ${reassign_case_cancel_button}

Clinic User Returns To Workqueue
    [Arguments]    ${tab}
    Try To Click Element    ${return_to_work_queue_button}
    IF    '${tab}' == 'Open cases'
        Wait Until Location Contains    ${WORKQUEUE_ENDPOINT}
    ELSE IF    '${tab}' == 'Patient reports'
        Wait Until Location Contains    ${PATIENT_REPORTS_ENDPOINT}
    END

Case Has New Assignee And Is Seen In Workqueue
    Clinic User Returns To Workqueue    Open cases
    Wait Until Patient Sees The Workqueue Is Fully Loaded
    Get Open Case Information    1
    List Should Contain Value    ${patient_case_information}    ${random_assignee}

Patient Report Has New Assignee And Is Seen In Workqueue
    Clinic User Returns To Workqueue    Patient Reports
    Wait Until Patient Sees The Workqueue Is Fully Loaded
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${random_assignee}
    Get Patient Report Information    1
    List Should Contain Value    ${patient_report_case_information}    ${random_assignee}

Case Is Assigned To A New Care Team
    Wait Until Banner Message And Reassignment Modal Are Dismissed
    Element Text Should Be    ${opened_case_assigned_careteam}    ${reassigned_care_team}

Clinic User Sees The Change Is Not Saved And Reassign Modal Is Closed
    Element Text Should Be    ${opened_case_assigned_careteam}    ${current_care_team}

Wait Until Page Loader Is Not Visible
    Wait Until Element Is Not Visible    ${noona-loader}

Wait Until Banner Message And Reassignment Modal Are Dismissed
    Try To Click Banner Message
    Wait Until Page Does Not Contain    ${case_assigned_successfully_banner}
    Wait Until Element Is Not Visible    ${reassign_case_modal}

Send A Quality Of Life (QOL) QL15 Questionnaire To The Patient
    Generate Clinic Token
    ...    ${automated_tests_clinic}[f01n18_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ${todays_date}    Get Current Date    result_format=%Y-%m-%d
    Set Test Variable    ${todays_date}
    IF    '${ENVIRONMENT}' == 'test'
        Get Patient Main Treatment Module Id    ${f01n18_patient}[id_test]
        Send QOL 15D Questionnaire Via API    ${f01n18_patient}[id_test]    ${todays_date}    ${todays_date}
    ELSE IF    '${ENVIRONMENT}' == 'staging'
        Get Patient Main Treatment Module Id    ${f01n18_patient}[id_staging]
        Send QOL 15D Questionnaire Via API    ${f01n18_patient}[id_staging]    ${todays_date}    ${todays_date}
    END
    Sleep    90s

The Patient Completes Quality Of Life Questionnaire QL15
    Login As Patient    ${f01n18_patient}[email]
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    questionnaires.Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
