*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings - Main success scenario (Case Management and SMS settings)
...                 Settings below are tested in detail in other test cases and therefore not included in the tests below:
...                 Case priority levels
...                 Case escalation for patients
...                 Reason for delay
...                 Case outcome
...                 Email notification for new work queue cases
...                 SMS notifications
...                 Patient invitations

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}sms_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource

Suite Setup         Set Libraries Order
Suite Teardown      Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web    noona_admin


*** Test Cases ***
Main Success Scenario - Case Management
    [Tags]    nms9-ver-450
    Login As Admin To Clinic And Go To Basic Settings
    Wait Until Noona Loader Is Not Visible
    Select Case management tab
    Which Additional Patient Account Information Are Enabled And / Or Mandatory
    Which Case Types Are Enabled, With Which Default Priority Level?
    Is Case Escalation Enabled?
    Is Case Delay Reason Enabled?
    Which Case Statuses Are Enabled?
    Is Case Outcome Enabled?
    Email Notification For New Work Queue Cases
    Select SMS Settings Tab And Modify Patient Recruitment (SMS) Settings
    Select Patient Invitation And Modify Patient Account Invitation Related Content


*** Keywords ***
Login As Admin To Clinic And Go To Basic Settings
    Login As Nurse    clinic=${test_clinic_setting_4}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default

Which Additional Patient Account Information Are Enabled And / Or Mandatory
    Enable Additional Patient Account Information
    Select And Deselect Mandatory Checkboxes
    Disable Additional Patient Account Information

Enable Additional Patient Account Information
    ${additional_information}    Create List
    ...    Medical record number
    ...    HIPAA code
    ...    Phone number 2
    ...    Phone number 3
    ...    Address
    ...    Zip / Postal code
    ...    City
    ...    State / Province
    ...    Primary provider
    ...    Site
    FOR    ${information}    IN    @{additional_information}
        Select Clinic Settings Checkbox    ${information}
    END
    ${status}    Run Keyword And Return Status    Element Should Be Enabled    ${save_button}
    IF    ${status} == ${TRUE}    Save Settings
    Reload Page
    Wait Until Page Contains    Medical record number

Disable Additional Patient Account Information
    ${additional_information}    Create List
    ...    Medical record number
    ...    HIPAA code
    ...    Phone number 2
    ...    Phone number 3
    ...    Address
    ...    Zip / Postal code
    ...    City
    ...    State / Province
    ...    Primary provider
    ...    Site
    FOR    ${information}    IN    @{additional_information}
        Unselect Clinic Settings Checkbox    ${information}
    END
    Save Settings

Select And Deselect Mandatory Checkboxes
    [Documentation]    Selects checkboxes from index 1 to 10 if not selected and deselects from index 1 to 10 after selection
    FOR    ${index}    IN RANGE    1    11
        ${mandatory_checkbox}    Set Variable    (//*[@data-testid="mandatory"])[${index}]
        Click Element    ${mandatory_checkbox}
    END
    Save Settings
    Reload Page
    Wait Until Page Contains    Medical record number
    FOR    ${index}    IN RANGE    1    11
        ${mandatory_checkbox}    Set Variable    (//*[@data-testid="mandatory"])[${index}]
        Click Element    ${mandatory_checkbox}
    END
    Save Settings
    Reload Page
    Wait Until Page Contains    Medical record number

Which Case Types Are Enabled, With Which Default Priority Level?
    Enable Clinic Case Types And Base Priorities
    Disable Clinic Case Types And Base Priorities

Enable Clinic Case Types And Base Priorities
    ${case_types}    Create List
    ...    Symptom Management
    ...    Chemo / Tx Questions
    ...    Medication Instructions
    ...    Lab Questions / Test Results
    ...    Appointment Questions
    ...    Prior Authorization / Insurance Questions
    ...    Medication Refill
    ...    Paperwork
    ...    Pre-Test Instructions
    ...    Referrals
    ...    Practical Problems
    ...    Medical records
    ...    Treatment Scheduling
    ...    Interpersonal Safety
    ...    Other
    FOR    ${type}    IN    @{case_types}
        Select Clinic Settings Checkbox    ${type}
    END
    ${status}    Run Keyword And Return Status    Element Should Be Enabled    ${save_button}
    IF    ${status}    Save Settings

Disable Clinic Case Types And Base Priorities
    Wait Until Page Contains    Practical Problems
    Unselect Clinic Settings Checkbox    Practical Problems
    Unselect Clinic Settings Checkbox    Interpersonal Safety
    Save Settings
    Reload Page

Is Case Escalation Enabled?
    Wait Until Page Contains    Case escalation enabled
    Noona Administrator Edits Case Escalation

Is Case Delay Reason Enabled?
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    Noona Administrator Edits Case Delay Reason

Which Case Statuses Are Enabled?
    Wait Until Page Contains    Awaiting Callback
    Modify Option - Enable Case Statuses
    Reload Page

Modify Option - Enable Case Statuses
    [Documentation]    to check that options can be adjusted and saved by disabling and then enabling options
    ${case_statuses}    Create List
    ...    Closed
    ...    New
    ...    Awaiting Callback
    ...    Awaiting Physician Response
    ...    Awaiting Triage Follow-up Call
    ...    Sent to Voicemail
    ...    Live Transfer
    ...    Callback Requested
    ...    Park Pharmacy
    ...    GYN
    ...    Other
    ...    Sent to Clinic
    FOR    ${c_stat}    IN    @{case_statuses}
        Unselect Clinic Settings Checkbox    ${c_stat}
    END
    ${status}    Run Keyword And Return Status    Element Should Be Enabled    ${save_button}
    IF    ${status} == ${TRUE}    Save Settings
    Reload Page
    Wait Until Page Contains    Medical record number
    FOR    ${c_stat}    IN    @{case_statuses}
        Select Clinic Settings Checkbox    ${c_stat}
    END
    ${status}    Run Keyword And Return Status    Element Should Be Enabled    ${save_button}
    IF    ${status} == ${TRUE}    Save Settings

Is Case Outcome Enabled?
    Wait Until Page Contains Element    ${case_outcome_enabled_yes}
    Noona Administrator Edits Case Outcome

Email Notification For New Work Queue Cases
    Wait Until Page Contains Element    ${add_email_notification_yes}
    Noona Administrator Edits Email Notification For New Work Queue Cases

Select SMS Settings Tab And Modify Patient Recruitment (SMS) Settings
    Noona Administrator Navigates To The SMS Settings Tab
    Noona Administrator Edits SMS Notifications
    Reload Page

Select Patient Invitation And Modify Patient Account Invitation Related Content
    Wait Until Element Is Visible    ${sms_yes_radio_button}
    Select Patient Invitation Tab
    Update Invitation Message SMS And Email Content

Select Patient Invitation Tab
    Click Element    ${patient_invitation_tab}
    Wait Until Element Is Visible    ${patient_invitation_textbox1}

Update Invitation Message SMS And Email Content
    Update Login Information Sent SMS Content And Reset To Default
    Update Login Reminder SMS Content And Reset To Default
    Update 'Content Of The Patient Account Invitation Email' And Reset To Default
    Update 'Content Of The Patient Account Activation Reminder Email' And Reset To Default
    Update Patient Invitation Page Content And Reset To Default

Update Login Information Sent SMS Content And Reset To Default
    ${file_content}    Get File    ${EXECDIR}${/}resources${/}clinic_settings${/}login-info-sent-content.txt
    Set Variable    ${file_content}
    Clear Element Text    ${patient_invitation_textbox1}
    ${random_text1}    Generate Random String    25
    ${full_content}    Set Variable    ${file_content}${random_text1}
    Try To Input Text    ${patient_invitation_textbox1}    ${full_content}
    Log    ${full_content}
    Save Settings
    Wait Until Element Is Visible    ${patient_invitation_textbox1}
    ${verify_content}    Get Value    ${patient_invitation_textbox1}
    Should Be Equal    ${verify_content}    ${full_content}
    Clear Element Text    ${patient_invitation_textbox1}
    Try To Input Text    ${patient_invitation_textbox1}    ${file_content}
    Save Settings

Update Login Reminder SMS Content And Reset To Default
    Wait Until Element Is Visible    ${patient_invitation_textbox2}
    ${file_content2}    Get File    ${EXECDIR}${/}resources${/}clinic_settings${/}login-reminder-sms-content.txt
    Set Variable    ${file_content2}
    Clear Element Text    ${patient_invitation_textbox2}
    ${random_text2}    Generate Random String    25
    ${full_content2}    Set Variable    ${file_content2}${random_text2}
    Try To Input Text    ${patient_invitation_textbox2}    ${full_content2}
    Log    ${full_content2}
    Save Settings
    Wait Until Element Is Visible    ${patient_invitation_textbox2}
    ${verify_content2}    Get Value    ${patient_invitation_textbox2}
    Should Be Equal    ${verify_content2}    ${full_content2}
    Clear Element Text    ${patient_invitation_textbox2}
    Try To Input Text    ${patient_invitation_textbox2}    ${file_content2}
    Save Settings

Update 'Content Of The Patient Account Invitation Email' And Reset To Default
    Wait Until Element Is Visible    ${patient_invitation_textbox3}
    ${file_content3}    Get File
    ...    ${EXECDIR}${/}resources${/}clinic_settings${/}content-patient-acct-invitation-email.txt
    Set Variable    ${file_content3}
    Clear Element Text    ${patient_invitation_textbox3}
    ${random_text3}    Generate Random String    25
    ${full_content3}    Set Variable    ${file_content3}${random_text3}
    Try To Input Text    ${patient_invitation_textbox3}    ${full_content3}
    Log    ${full_content3}
    Save Settings
    Wait Until Element Is Visible    ${patient_invitation_textbox3}
    ${verify_content3}    Get Value    ${patient_invitation_textbox3}
    Should Be Equal    ${verify_content3}    ${full_content3}
    Clear Element Text    ${patient_invitation_textbox3}
    Try To Input Text    ${patient_invitation_textbox3}    ${file_content3}
    Save Settings

Update 'Content Of The Patient Account Activation Reminder Email' And Reset To Default
    [Documentation]    Default text content is the same as we have in 'content-patient-acct-invitation-email.txt' file
    Wait Until Element Is Visible    ${patient_invitation_textbox4}
    ${file_content4}    Get File
    ...    ${EXECDIR}${/}resources${/}clinic_settings${/}content-patient-acct-invitation-email.txt
    Set Variable    ${file_content4}
    Clear Element Text    ${patient_invitation_textbox4}
    ${random_text4}    Generate Random String    25
    ${full_content4}    Set Variable    ${file_content4}${random_text4}
    Try To Input Text    ${patient_invitation_textbox4}    ${full_content4}
    Log    ${full_content4}
    Save Settings
    Wait Until Element Is Visible    ${patient_invitation_textbox4}
    ${verify_content4}    Get Value    ${patient_invitation_textbox4}
    Should Be Equal    ${verify_content4}    ${full_content4}
    Clear Element Text    ${patient_invitation_textbox4}
    Try To Input Text    ${patient_invitation_textbox4}    ${file_content4}
    Save Settings

Update Patient Invitation Page Content And Reset To Default
    Wait Until Element Is Visible    ${patient_invitation_textbox5}
    ${file_content5}    Get File    ${EXECDIR}${/}resources${/}clinic_settings${/}patient-invitation-page-content.txt
    Set Variable    ${file_content5}
    Clear Element Text    ${patient_invitation_textbox5}
    ${random_text5}    Generate Random String    25
    ${full_content5}    Set Variable    ${file_content5}${random_text5}
    Try To Input Text    ${patient_invitation_textbox5}    ${full_content5}
    Log    ${full_content5}
    Save Settings
    Wait Until Element Is Visible    ${patient_invitation_textbox5}
    ${verify_content5}    Get Value    ${patient_invitation_textbox5}
    Should Be Equal    ${verify_content5}    ${full_content5}
    Clear Element Text    ${patient_invitation_textbox5}
    Try To Input Text    ${patient_invitation_textbox5}    ${file_content5}
    Save Settings
