*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings -> Basic Settings
...                 Settings below are tested in detail in other test cases and therefore not included in the tests below:
...                 Clinic experience for patients
...                 Asking for patient consent
...                 Smart symptom monitoring enabled
...                 F03NA01 Noona administrator can edit clinic settings -> Extension G
...                 Clinic Setting To Manage If Patient Can Capture Symptom Using Diary

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_management.resource
Resource            ${EXECDIR}${/}resources/nurse/case_management.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_education.resource
Resource            ${EXECDIR}${/}resources/nurse/nurse_profile.resource
Resource            ${EXECDIR}${/}resources/nurse/templates.resource
Resource            ${EXECDIR}${/}resources/nurse/clinic_common.resource
Resource            ${EXECDIR}${/}resources/nurse/questionnaires.resource
Resource            ${EXECDIR}${/}resources/clinic_settings/clinic_information.resource
Resource            ${EXECDIR}${/}resources/nurse/list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}case_management.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web    noona_admin


*** Variables ***
${case_mgt_tab}                     //*[@data-testid="case-management"]
${candidates_care_team_dropdown}    (//*[@id='candidates__careTeam--filter']//span[contains(@class, 'arrow')])[1]
${invitation}                       (//td[@data-testid='invite-patient-action'])[1]
${cancel_invite}                    //*[@data-testid="cancel-patient-invitation"]


*** Test Cases ***
Main success scenario - Basic Settings - Update Patient Message Settings - 1
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-1    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${complete_questionnaire_on_behalf_of_patient}    No
    Select Clinic Settings Radio Button    ${patient_can_ask_about_symptoms}    No
    Select Clinic Settings Radio Button    ${patient_can_capture_symptom_using_diary}    No
    Select Clinic Settings Radio Button    ${patient_can_ask_other_issues}    No
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    No
    Select Clinic Settings Radio Button    ${nurse_can_schedule_questionnaire_for_patient}    Yes
    Reset Patient Settings
    Save Settings
    Close Browser
    Verify If Patient Message Settings Are Applied - 1
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_2_EHR_TOKEN}

Main success scenario - Basic Settings - Update Patient Education Settings - 2
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-2    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${patient_can_ask_other_issues}    Yes
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    Yes
    Select Clinic Settings Radio Button    ${nurse_can_schedule_questionnaire_for_patient}    No
    Select Clinic Settings Radio Button    ${patient_access_link_from_message}    No
    Select Clinic Settings Radio Button    ${patient_can_rate_instructions}    No
    Select Clinic Settings Radio Button    ${enable_file_attachment_message}    No
    Reset Patient Settings
    Save Settings
    Close Browser
    Verify If Patient Message Settings Are Applied - 2
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_2_EHR_TOKEN}

Main success scenario - Basic Settings - Update Patient Education Settings - 1
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-3    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    Yes
    Select Clinic Settings Radio Button    ${clinic_can_send_pe_message_to_patients}    Yes
    Select Clinic Settings Radio Button    ${patient_can_respond_to_patient_education_messages_label}    No
    Select Clinic Settings Checkbox    Oncolink
    Unselect Clinic Settings Checkbox    Clinic
    Select Radio Button In Group    Oncolink
    Reset Patient Settings
    Save Settings
    Close Browser
    Verify If Patient Education Settings Are Applied - 1 Source
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Checkbox    Clinic
    Save Settings
    Close Browser
    Verify If Patient Education Settings Are Applied - 2 Sources
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_2_EHR_TOKEN}

Main success scenario - Basic Settings - Update Patient Education Settings - Cannot Send PE message To Patient
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-4    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    Yes
    Select Clinic Settings Radio Button    ${clinic_can_send_pe_message_to_patients}    No
    Reset Patient Settings
    Save Settings
    Close Browser
    Verify If Patient Education Settings Are Applied - Cannot Send PE message To Patient
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_2_EHR_TOKEN}

Main success scenario - Basic Settings - Update Language and Unit System
    [Documentation]    Todo
    [Tags]    nms9-ver-14    manual
    Login As Admin To Clinic And Go To Basic Settings
    Update Clinic Language Settings    random
    Update Measurement Unit System    Imperial
    Save Settings
    Close Browser

Main success scenario - Basic Settings - Date Format
    [Tags]    nms9-ver-14-5    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    No    # to reset settings
    Select Clinic Settings Radio Button    ${nurse_can_contact_patient}    Yes    # to reset settings
    Update Date Formatting    random
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Navigate To Patient Page
    Go To List Of Patients Tab
    Try To Click Element    ${first_patient}
    Wait Until Element Is Visible    ${patient_dob_header}
    ${text}    Get Text    ${patient_dob_header}
    Verify If Patient's DOB Match Date Format    ${text}    ${date_format}
    Close Browser

Main success scenario - Basic Settings - Analytics
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-6    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Update Analytics Settings    No
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Try To Click Element    ${clinic_menu}
    Wait Until Page Contains Element    ${clinic_information_menu_link}
    Element Should Not Be Visible    ${patient_status_analytics}
    Close Browser
    Login As Nurse    clinic=${test_clinic_setting_2}[name]    user_type=${USER_TYPE}[noona_admin]
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Wait Until Page Contains Element    ${clinic_information_menu_link}
    Element Should Not Be Visible    ${patient_status_analytics}
    Close Browser

Main success scenario - Basic Settings - In-app Analytics - Enabled, Reporting - Enabled
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-7    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Update In-app Analytics Settings    Yes
    Update Reporting Setting    Yes
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Visible    ${provider_analytics_menu}
    Try To Click Element    ${provider_analytics_menu}
    Wait Until Element Is Visible    ${operational_analytics_menu}
    Element Should Be Visible    ${reporting_menu_link}
    Close Browser

Main success scenario - Basic Settings - In-app Analytics - Enabled, Reporting - Disabled
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-8    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Update In-app Analytics Settings    Yes
    Update Reporting Setting    No
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Wait Until Element Is Visible    ${clinic_menu}
    Element Should Be Visible    ${provider_analytics_menu}
    Try To Click Element    ${provider_analytics_menu}
    Wait Until Element Is Visible    ${operational_analytics_menu}
    Element Should Not Be Visible    ${reporting_menu_link}
    Close Browser

Main success scenario - Basic Settings - In-app Analytics - Disabled
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-9    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Update In-app Analytics Settings    No
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Wait Until Element Is Visible    ${clinic_menu}
    Element Should Not Be Visible    ${provider_analytics_menu}

Main success scenario - Basic Settings - Identity Code Validation
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-10    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${finnish_identidy_code_selected}
    ...    timeout=5s
    IF    ${status}!=${TRUE}
        Update Identity Code Validation Setting    Finnish identity code
        Save Settings
    END
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Try To Click Element    ${patients_menu}
    Try To Click Element    ${create_patient_tab}
    Set New Patient Data With Random Data
    Try To Input Text    ${identity_code_field}    ${patient_dict}[social security number]
    Wait Until Page Contains    Invalid identity code    timeout=3s
    Close Browser

Main success scenario - Basic Settings - Patient Authentication - 1
    [Documentation]    Testing only the options that are not yet tested in other cases
    [Tags]    nms9-ver-14-11    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${patient_access_link_from_message}    No    # to reset the settings
    Select Clinic Settings Radio Button    ${patient_access_link_from_message}    Yes
    Update Date Formatting    DD.MM.YYYY    #to make sure that date format is correct for other tcs under this usecase
    Update Identity Code Validation Setting    No validation
    Update Authentication Settings - Patient    Yes    Yes
    Save Settings
    Close Browser
    Patient Requests Password Reset    ${f03na01_patient}[email]
    Close Browser
    Set Email And Delete Previous Messages    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${f03na01_patient}[email]
    ...    ${PATIENT_URL_IN_MESSAGE}/s/
    Open URL In Chrome    ${message_data}[1]
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${verification_code_login_header}
    Location Should Contain    /patient/#/login/sms
    Close Browser

Main success scenario - Basic Settings - Patient Authentication - 2
    [Tags]    nms9-ver-14-12    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Update Identity Code Validation Setting    No validation
    VAR    ${message}    Test with problems logging in
    Update Authentication Settings - Patient    No    No    message=${message}
    Save Settings
    Close Browser
    Patient Requests Password Reset    ${f03na01_patient}[email]
    Close Browser
    Set Email And Delete Previous Messages    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    Patient Received An Email About A New Message
    ...    ${f03na01_patient}[email]
    ...    Instructions to log into Noona
    ...    ${message}

Main success scenario - Basic Settings - Nurse Authentication
    [Documentation]    Putting back to manual for now as it has risk of making other tcs to fail if the 2fa is not disabled successfully
    [Tags]    nms9-ver-14    manual
    Login As Admin To Clinic And Go To Basic Settings
    Update Authentication Settings - Nurse    Password and SMS verification code    40    40
    Save Settings
    Close Browser
    Open URL In Chrome    ${NURSE_LOGIN_URL}
    Login As Nurse From Landing Page    ${test_clinic_setting_2}[user_email]
    Wait Until Page Contains    Verification code
    Wait Until Page Contains    Please enter the verification code sent to your mobile phone.
    Element Should Be Visible    ${nurse_2fa_input_field}
    Wait Until Page Contains    Send a new verification code
    Close Browser

Main success scenario - Basic Settings - Legal texts
    [Tags]    nms9-ver-14-13    nms9-ver-14
    Login As Admin To Clinic And Go To Basic Settings
    Select Clinic Settings Radio Button    ${show_terms_of_use_to_patients_label}    No
    Select Clinic Settings Radio Button    ${clinician_code_required_label}    Yes
    # External clinic user id required - not in use
    Select Clinic Settings Checkbox    ${ask_patient_to_accept_terms_checkbox_label}    in_group=No
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${terms}    Convert Text File To String    ${terms_text_location}
    Set Test Variable    ${terms}    ${now}-${terms}
    Try To Input Text    ${terms_of_use_text_area}    ${terms}
    Select New Consent Required From Patients For Terms Of Use
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Go To Nurse Profile
    Identity Code Field Is Visible
    Close Browser
    Login As Patient    ${f03na01_patient}[email]
    Wait Until Page Contains Element    ${privacy_statement_button}
    Element Should Not Be Visible    ${terms_of_use_link}
    Approve Terms And Click Next
    Patient Is In The Right Page    Diary
    [Teardown]    F03NA01 - Test Teardown - Basic Settings - Legal Texts

Clinic Setting To Manage If Patient Can Capture A symptom Using Diary (Ext G)
    [Documentation]    tc is on this file because its setting is under 'basic settings' tab
    ...    tc uses ' TA clinic Test_Clinic_Setting_4 ' to avoid failures on other tcs
    ...    tc will also check what happens if option is set to 'No'
    ...    nurse for this tc is ' <EMAIL> '
    [Tags]    nms9-ver-426
    Login As Nurse    clinic=${test_clinic_setting_4}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default
    Find 'Patient Can Capture A Symptom Using Diary' Option And Select 'Yes'

Main success scenario: Patient Identifier - INS Settings (Ext H)
    [Documentation]    Checks functionality of our INS setting for French speaking customers
    ...    tc runs on TA Clinic Test_Clinic_Settings_3
    ...    nurse is ' <EMAIL> '
    ...    TODO : Check identifier in PDF
    [Tags]    nms9-ver-479
    [Setup]    Patient Identifier - INS Settings (Ext H) - Test Setup
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Symptom
    Close Browser
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Check Identifier On Work Queue    ${id_ssn_column_header}
    Check SSN Identifier For All Tabs On Patients Page
    Close Browser
    Login As Nurse
    ...    clinic=${test_clinic_setting_3}[name]
    ...    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default
    Update Patient Identifier - INS    Yes
    Verify INS Setting On Case Management Tab
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Check Identifier On Work Queue    ${ins_column_header}
    Check INS Identifier For All Tabs On Patients Page
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_3}[manager_email]
    ...    ${TEST_CLINIC_SETTING_3_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_3_EHR_TOKEN}


*** Keywords ***
Patient Identifier - INS Settings (Ext H) - Test Setup
    Login As Nurse
    ...    clinic=${test_clinic_setting_3}[name]
    ...    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default
    Update Patient Identifier - INS    No
    Verify SSN Setting On Case Management Tab
    Close Browser
    Add An Activated Patient Under Clinic Settings 3        f03na01_ins

Update Patient Identifier - INS
    [Arguments]    ${option}
    Wait Until Page Contains Element    ${patient_identifier_ins_yes}
    IF    '${option}'=='Yes'
        Click Element    ${patient_identifier_ins_no}//input
        Click Element    ${patient_identifier_ins_yes}//input
    ELSE
        Click Element    ${patient_identifier_ins_yes}//input
        Click Element    ${patient_identifier_ins_no}//input
    END
    Save Settings
    Reload Page
    Wait Until Page Contains Element    ${patient_identifier_ins_yes}
    IF    '${option}'=='Yes'
        ${attr_yes}    Get Element Attribute    ${patient_identifier_ins_yes}    class
        Should Contain    ${attr_yes}    mat-mdc-radio-checked
    ELSE
        ${attr_no}    Get Element Attribute    ${patient_identifier_ins_no}    class
        Should Contain    ${attr_no}    mat-mdc-radio-checked
    END

Update Clinic Language Settings
    [Arguments]    ${language}
    IF    '${language}'=='random'
        ${random_lang}    Get Random Language
        VAR    ${language}    ${random_lang}    scope=TEST
    ELSE
        VAR    ${language}    scope=TEST
    END
    Select Clinic Settings Checkbox    ${language}

Update Measurement Unit System
    [Documentation]    Metric or Imperial
    [Arguments]    ${unit}
    Select Clinic Settings Radio Button    ${measurement_unit_system_label}    ${unit}

Update Date Formatting
    [Arguments]    ${format}
    Click Settings Dropdown    ${date_formatting_text}
    ${random_date_format}    Get Random Date Formatting
    IF    '${format}'=='random'
        VAR    ${date_format}    ${random_date_format}    scope=TEST
        Log    ${date_format}
    ELSE
        VAR    ${date_format}    ${format}    scope=TEST
    END
    Select Dropdown Option    ${date_format}
    Set Current Date According To Format    ${random_date_format}

Set Current Date According To Format
    [Arguments]    ${random_date_format}
    ${new_date_format}    Set Variable If
    ...    '${random_date_format}'=='DD/MM/YYYY with 24 hour clock'    %d/%m/%Y
    ...    '${random_date_format}'=='DD.MM.YYYY with 24 hour clock'    %d.%m.%Y
    ...    '${random_date_format}'=='MM/DD/YYYY with AM/PM clock'    %m/%d/%Y
    ...    '${random_date_format}'=='YYYY-MM-DD with 24 hour clock'    %Y-%m-%d
    Set Test Variable    ${new_date_format}

Update Analytics Settings
    [Arguments]    ${option}
    Select Clinic Settings Radio Button    ${clinic_medical_records_visible}    ${option}
    Select Clinic Settings Radio Button    ${adimn_medical_records_visible}    ${option}

Update In-app Analytics Settings
    [Arguments]    ${option}
    Select Clinic Settings Radio Button    ${clinic_inapp_analytics_visible}    ${option}

Update Reporting Setting
    [Arguments]    ${option}
    Select Clinic Settings Radio Button    ${reporting_is_visible_text}    ${option}

Update Identity Code Validation Setting
    [Arguments]    ${option}
    Select From Settings Dropdown    ${id_code_validation}    ${option}

Update Authentication Settings - Patient
    [Arguments]    ${can_reset_pwd}    ${2fa_login}    ${message}=none
    Select Clinic Settings Radio Button    ${patient_can_reset_password_label}    ${can_reset_pwd}
    Select Clinic Settings Radio Button    ${patients_use_2fa_when_logging_in_via_link}    ${2fa_login}
    IF    '${can_reset_pwd}'=='No'
        Wait Until Element Is Visible    ${message_for_patient_problems_logging_in}
        Input Text    ${message_for_patient_problems_logging_in}    ${message}
    END

Update Authentication Settings - Nurse
    [Arguments]    ${clinic_auth}    ${clinic_session_lifetime}    ${clinic_remember_lifetime}
    Select From Settings Dropdown    ${clinic_user_auth_type_dropdown_title}    ${clinic_auth}
    Input Text    ${clinic_user_login_session_lifetime}    ${clinic_session_lifetime}
    Input Text    ${clinic_user_remember_login_lifetime}    ${clinic_remember_lifetime}

Verify If Patient Message Settings Are Applied - 1
    Set Test Variable    @{mailosaur_keys}    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    Add An Activated Patient Under All Settings Clinic    basic.settings    mailosaur=${settings_email_keys}[0]
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Send QOL 15D Questionnaire Via API To Patient For Today
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    # The care person can complete questionnaires on behalf of the patient - No
    Verify Complete Questionnaire Button Visibility    No    ${BASELINE_QUESTIONNAIRE}
    Verify Complete Questionnaire Button Visibility    No    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    # The nurse can contact the patient - No
    Navigate To Patient Cases Tab
    Verify Contact Patient Button Visibility    No
    Close Browser
    Login As Patient    ${patient_email}
    # Patient can ask about symptoms - No
    Navigate To Clinic
    Verify Ask About Symptom Button Visibility    No
    # The patient can ask about other issues - No
    Verify Ask About Other Issues Button Visibility    no
    # Patient can capture a symptom using diary - No
    Verifiy Add A Symptom Button Visibility    No
    Close Browser

Verify If Patient Message Settings Are Applied - 2
    Set Test Variable    @{mailosaur_keys}    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    Add An Activated Patient Under All Settings Clinic    basic.settings    mailosaur=${settings_email_keys}[0]
    Patient Received Invitation To Use Noona    ${patient_email}    ${test_clinic_setting_2}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    # Patient can access to Noona content via direct links from notification messages - No
    Verify If Patient Can See Questionnaire Form From Link    No    ${patient_email}    ${test_clinic_setting_2}[name]
    Send Contact Patient Request    11
    Close Browser
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    # A nurse can schedule symptom questionnaires for patients - No
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Verify Questionnaire Option If Disabled    ${BASELINE_QUESTIONNAIRE}
    Close Browser
    # Enable file attachments in patient messages - No
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask about other issues
    Select topic
    Verify Attach Files Link Visibility    No
    Cancel Open Question Sending
    # Patients can rate received instructions
    Select Latest Clinic Message
    Verify If Patient Can Rate Instructions    No
    Close Browser

Verify If Patient Education Settings Are Applied - 1 Source
    Create A Patient And Contact With Attachment
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Try To Click Element    ${top_contact_patient_button}
    Verify PE Documents 1 Source
    Return To Patients
    Navigate to Message templates
    Try To Click Element    ${new_template_button}
    Verify PE Documents 1 Source
    Close Browser
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Verify If Patient Can Respond To PE Message    No
    Close Browser

Verify If Patient Education Settings Are Applied - 2 Sources
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Try To Click Element    ${top_contact_patient_button}
    Verify PE Documents 2 Sources
    Return To Patients
    Navigate to Message templates
    Try To Click Element    ${new_template_button}
    Verify PE Documents 2 Sources
    Close Browser

Verify PE Documents 2 Sources
    Try To Click Element    ${add_documents}
    Wait Until Element Is Visible    ${search_field}
    Try To Click Element    ${search_document_dropdown_arrow}
    Wait Until Element Is Visible    ${patient_education_search_option_oncolink}
    Wait Until Element Is Visible    (//*[@title='${test_clinic_setting_2}[name]'])[last()]
    Try To Click Element    ${cancel_schedule_template_modal}

Verify PE Documents 1 Source
    Try To Click Element    ${add_documents}
    Wait Until Element Is Visible    ${search_field}
    Element Should Not Be Visible    ${search_document_dropdown_arrow}
    Try To Click Element    ${cancel_schedule_template_modal}

Verify If Patient Education Settings Are Applied - Cannot Send PE message To Patient
    Set Test Variable    @{mailosaur_keys}    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    Add An Activated Patient Under All Settings Clinic    basic.settings    mailosaur=${settings_email_keys}[0]
    Login As Nurse    ${test_clinic_setting_2}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Try To Click Element    ${top_contact_patient_button}
    Try To Click Element    ${contact_type_list}
    Wait Until Element Is Visible    ${question_contact_option}
    Element Should Not Be Visible    ${patient_education_contact_option}
    Close Browser

Login As Admin To Clinic And Go To Basic Settings
    Login As Nurse    clinic=${test_clinic_setting_2}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default

Create A Patient And Contact With Attachment
    Set Test Variable    @{mailosaur_keys}    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    Add An Activated Patient Under All Settings Clinic    basic.settings    mailosaur=${settings_email_keys}[0]
    Generate Clinic Token
    ...    ${test_clinic_setting_2}[user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${TEST_CLINIC_SETTING_2_CLINIC_ID}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    Set Test Variable    ${now}
    Send Contact Patient Request With Attachment
    ...    basic settings test ${now}
    ...    ${attachment_name_brain_tumors}
    ...    ${attachment_id_brain_tumors}

Reset Patient Settings
    [Documentation]    Reset settings to make sure patient can be created and login successfully with usual keywords
    Update Identity Code Validation Setting    No validation
    Update Authentication Settings - Patient    Yes    No
    Update Date Formatting    DD.MM.YYYY with 24 hour clock
    Unselect Clinic Settings Checkbox    ${ask_patient_to_accept_terms_checkbox_label}    in_group=No
    ${now}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S
    # update to make sure save button is enabled
    Try To Input Text
    ...    ${privacy_statement_textarea}
    ...    Privacy Statement Updated today ${now}

Verify If Patient's DOB Match Date Format
    [Arguments]    ${dob}    ${date_format}
    ${regex}    Set Variable If
    ...    '${date_format}'=='DD/MM/YYYY with 24 hour clock'    (^0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(\\d{4}$)
    ...    '${date_format}'=='DD.MM.YYYY with 24 hour clock'    (^0[1-9]|[12][0-9]|3[01]).(0[1-9]|1[0-2]).(\\d{4}$)
    ...    '${date_format}'=='MM/DD/YYYY with AM/PM clock'    (0[1-9]|1[0-2])\/(^0[1-9]|[12][0-9]|3[01])\/(\\d{4}$)
    ...    '${date_format}'=='YYYY-MM-DD with 24 hour clock'    (|\d{4}$)-(0[1-9]|1[0-2])-(^0[1-9]|[12][0-9]|3[01])
    Should Match Regexp    ${dob}    ${regex}

Find 'Patient Can Capture A Symptom Using Diary' Option And Select 'Yes'
    [Documentation]    tc first checks what happens when option is set to 'no'
    Noona Admin Turns OFF Setting For Patient To Capture Symptom Using Diary
    Patient Cannot See Symptom-Option From Add Menu
    Noona Admin Turns ON Setting For Patient To Capture Symptom Using Diary
    Patient Can See Symptom-Option From Add Menu

Noona Admin Turns OFF Setting For Patient To Capture Symptom Using Diary
    Wait Until Element Is Visible    ${capture_symptom_using_diary}
    Scroll Element Into View    ${capture_symptom_using_diary}
    Click Element    ${capture_symptom_using_diary_yes}//input
    Click Element    ${capture_symptom_using_diary_no}//input
    Save Settings
    Reload Page    # needed to avoid failures caused by element being absent from page
    Wait Until Element Is Visible    ${capture_symptom_using_diary}
    ${attr_yes}    Get Element Attribute    ${capture_symptom_using_diary_yes}    class
    Should Not Contain    ${attr_yes}    mat-mdc-radio-checked
    Close Browser

Patient Cannot See Symptom-Option From Add Menu
    Login As Patient    ${f03na01_capture_symptom}[email]
    Approve Terms And Consent Only If Visible
    Click Add Menu Button
    Page Should Not Contain Element    ${symptom_entry}
    Close Browser

Noona Admin Turns ON Setting For Patient To Capture Symptom Using Diary
    Login As Nurse    clinic=${test_clinic_setting_4}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default
    Wait Until Element Is Visible    ${capture_symptom_using_diary}
    Scroll Element Into View    ${capture_symptom_using_diary}
    Click Element    ${capture_symptom_using_diary_no}//input
    Click Element    ${capture_symptom_using_diary_yes}//input
    Save Settings
    Reload Page    # needed to avoid failures caused by element being absent from page
    Wait Until Element Is Visible    ${capture_symptom_using_diary}
    ${attr_yes}    Get Element Attribute    ${capture_symptom_using_diary_yes}    class
    Should Contain    ${attr_yes}    mat-mdc-radio-checked
    Close Browser

Patient Can See Symptom-Option From Add Menu
    Login As Patient    ${f03na01_capture_symptom}[email]
    Click Add Menu Button
    Page Should Contain Element    ${symptom_entry}
    Close Browser

Verify SSN Setting On Case Management Tab
    Select Case Management Tab
    Check Case Management Tab Patient Primary Identifier Option    ${ssn_radio_button}
    Close browser

Verify INS Setting On Case Management Tab
    Select Case Management Tab
    Check Case Management Tab Patient Primary Identifier Option    ${ins_radio_button}
    Close browser

Select Case Management Tab
    Try To Click Element    ${case_mgt_tab}
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Page Contains Element    ${mrn_case_mgt_tab}

Check Case Management Tab Patient Primary Identifier Option
    [Arguments]    ${ins_or_ssn_radio_button}
    Scroll Element Into View    ${mrn_case_mgt_tab}
    Page Should Contain Element    ${ins_or_ssn_radio_button}

Check Identifier On Work Queue
    [Arguments]    ${ins_or_ssn_column}
    Click List View Button
    Wait Until Page Does Not Contain Element    ${loader}
    Page Should Contain Element    ${ins_or_ssn_column}
    Go To Patient Reports
    Wait Until Page Does Not Contain Element    ${loader}
    Select Care Team Filter In Work Queue    Care Team 1
    Wait Until Page Does Not Contain Element    ${loader}
    Page Should Contain Element    ${ins_or_ssn_column}
    Go To Closed Cases Tab
    Wait Until Page Does Not Contain Element    ${loader}
    ${status}    Run Keyword And Return Status    Page Should Contain Element    ${ins_or_ssn_column}
    IF    ${status} == ${FALSE}
        Search Patient By Identity Code    ${patient_ssn}
        Open Other Symptom Case
        Close Case
        Return To Work Queue
        Wait Until Element Is Visible    ${ins_or_ssn_column}
    END

Check SSN Identifier For All Tabs On Patients Page
    Navigate To Patient Page
    Wait Until Page Does Not Contain Element    ${loader}
    Create Patient Page Has Correct Header Name    ${ssn_header_create_patient}
    List Of Patients Page Has Correct Header
    ...    ${ssn_header_1_list_of_patients_page}
    ...    ${ssn_header_2_list_of_patients_page}
    SMS-Invitations Page Has Correct Header    ${ssn_header_other_patient_tabs}
    Candidates Page Has Correct Header    ${ssn_header_other_patient_tabs}
    Invitation Modal Has Correct Patient Identifier    ${ssn_header_gen_info_tab}
    Check Identifier On Patient Profile    ${ssn_header_patient_profile}    ${ssn_header_gen_info_tab}

Check INS Identifier For All Tabs On Patients Page
    Navigate To Patient Page
    Wait Until Page Does Not Contain Element    ${loader}
    Create Patient Page Has Correct Header Name    ${ins_header_create_patient}
    List Of Patients Page Has Correct Header
    ...    ${ins_header_1_list_of_patients_page}
    ...    ${ins_header_2_list_of_patients_page}
    SMS-Invitations Page Has Correct Header    ${ins_header_other_patient_tabs}
    Candidates Page Has Correct Header    ${ins_header_other_patient_tabs}
    Invitation Modal Has Correct Patient Identifier    ${ins_header_gen_info_tab}
    Check Identifier On Patient Profile    ${ins_header_patient_profile}    ${ins_header_gen_info_tab}

Create Patient Page Has Correct Header Name
    [Arguments]    ${header}
    Wait Until Page Contains Element    ${header}

List Of Patients Page Has Correct Header
    [Arguments]    ${header1}    ${header2}
    Go To List Of Patients Tab
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Page Contains Element    ${header1}
    ${other_header_visible}    Run Keyword And Return Status    Wait Until Page Contains Element    ${header2}
    IF    ${other_header_visible} == True
        Log
        ...    There 2 tables on "Patient list"-page if there have been patient removal requests coming for the clinic manager.
    END

SMS-Invitations Page Has Correct Header
    [Arguments]    ${header}
    Navigate To SMS-Invitations Tab
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Page Contains Element    ${header}

Candidates Page Has Correct Header
    [Arguments]    ${header}
    Try To Click Element    ${candidates_tab}
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Page Contains Element    ${header}

Invitation Modal Has Correct Patient Identifier
    [Arguments]    ${header}
    Wait Until Element Is Visible    ${candidates_care_team_dropdown}
    Wait Until Element Is Enabled    ${candidates_care_team_dropdown}
    Try To Click Element    ${candidates_care_team_dropdown}
    Try To Click Element    ${remove_all_selections_option}
    Try To Click Element    ${candidates_care_team_dropdown}
    Wait Until Page Contains    All patients
    ${status}    Run Keyword And Return Status    Page Should Contain Element    ${invitation}
    IF    ${status} == ${FALSE}
        Send Candidate Request    ${TEST_CLINIC_SETTING_3_EHR_TOKEN}    222
        Sleep    2s
        Reload Page
        Wait Until Page Contains Element    ${invitation}
    END
    Try To Click Element    ${invitation}
    Wait Until Page Contains Element    ${header}
    Try To Click Element    ${cancel_invite}
    Wait Until Page Contains Element    ${candidates_tab}

Check Identifier On Patient Profile
    [Arguments]    ${header1}    ${header2}
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Element Is Visible    ${header1}
    Try To Click Element    ${general_information_tab}
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Element Is Visible    ${header2}

F03NA01 - Test Teardown - Basic Settings - Legal Texts
    Login As Admin To Clinic And Go To Basic Settings
    Unselect Clinic Settings Checkbox    ${ask_patient_to_accept_terms_checkbox_label}    in_group=No
    Save Settings
    Close Browser
