*** Settings ***
Documentation       F13CU01 Clinic user can export patient data - not yet automated
...                 F13NA01 Noona administrator can see clinic data export requests - Main success scenario
...                 F13NA02 Noona administrator can approve a clinic data export request - Main success scenario

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}announcements.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_data_export.resource
Resource            ${EXECDIR}${/}resources${/}management${/}noona_management_site.resource
Resource            ${EXECDIR}${/}resources/management/exports.resource
Resource            ${EXECDIR}${/}resources/management/login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13cu02    nms9-ver-f13na01    nms9-ver-f13na02    clinic-web


*** Test Cases ***
Main success scenario
    [Tags]    nms9-ver-50    nms9-ver-51    sms
    [Setup]    Approve Existing Pending Requests
    Login As Nurse With 2FA    email=${automated_tests_clinic}[f13cu02_manager]
    Navigate To Clinic Data Export
    Download Approved Data Export Request If There's Any
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    Set Test Variable    ${data_export_description}    Data export test - ${now}
    @{data_types}    Create List    PATIENTS    SYMPTOMS    QUESTIONNAIRES    MESSAGES    DIARYENTRIES    TIMELINEEVENTS    CASES
    ${random_int}    Random Int    min=0    max=6
    Send Data Export Request
    ...    CUSTOM
    ...    Care Team 5
    ...    ${ABDOMINAL_RADIOTHERAPY}[name]
    ...    C00& Malignant neoplasm of lip
    ...    ${data_types}[${random_int}]
    ...    Data export test - ${now}
    Wait Until Page Contains    ${saving_requests_banner_message}
    Try To Click Banner Message
    Page Should Contain    ${data_export_request_being_processed_text}
    Reload Page
    Wait Until Page Contains    ${data_export_request_being_processed_text}
    Element Should Not Be Visible    ${data_export_request_send_button}
    Close Browser
    # nms9-ver-50
    Login As Admin With 2FA
    Admin User Navigates To Exports Tab
    Wait Until Data Export Request Page Is Visible
    Admin Opens And Sees Details Of The Data Export Request    Pending    ${data_export_description}
    Admin User Closes The Data Export Request Modal
    Select Data Export Request From List    Pending    ${data_export_description}
#    # nms9-ver-51
    Admin Can Confirm The Approval Of The Data Export Request
    Admin Can See Approval Confirmation And Click Ok
    Admin Opens And Sees Details Of The Data Export Request    Approved    ${data_export_description}
    Admin User Closes The Data Export Request Modal
    Wait Until Data Export Request Page Is Visible
    Close Browser
    # nms9-ver-45 - not working atm, approved data export request is not visible, clarification in progress
#    Login As Nurse With 2FA    email=${automated_tests_clinic}[f13cu02_manager]
#    Navigate To Clinic Data Export
#    Wait Until Element Is Visible    ${data_export_download_link}
#    Try To Click Element    ${data_export_download_link}
#    Wait Until Page Contains    ${clinic_data_export_page_info_text}
#    Delete All Messages In Server    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}
#    ${message}    User Received SMS
#    Should Contain    ${message}    is the password for your Noona clinic data export file.

Extension A - User does not have 2-factor-authentication enabled
    [Tags]    nms9-ver-283
    Login As Nurse
    Navigate To Clinic Data Export
    Wait Until Page Contains
    ...    Please enable two factor authentication from your profile settings to create a data export request.


*** Keywords ***
Wait Until Data Export Request Page Is Visible
    Wait Until Element Is Visible    ${data_export_requests_header_text}
    Wait Until Element Is Visible    ${data_export_requests_table}
    Verify Data Export Request Table Header Cells

Select Data Export Request From List
    [Arguments]    ${status}    ${other_detail}
    Try To Click Element
    ...    ${data_export_requests_table}//table//tbody//td[text()='${status}']/following-sibling::td[text()='${other_detail}']

Admin Opens And Sees Details Of The Data Export Request
    [Arguments]    ${status}    ${description}
    Select Data Export Request From List    ${status}    ${description}
    Wait Until Element Is Visible    ${data_export_approval_modal}
    Page Should Contain Element    ${data_export_detail_title_status}
    Page Should Contain Element    ${data_export_detail_title_date_and_time}
    Page Should Contain Element    ${data_export_detail_title_clinic}
    Page Should Contain Element    ${data_export_detail_title_requested_by}
    Page Should Contain Element    ${data_export_detail_title_description}
    ${detail_info1_status}    Get Text    ${data_export_approval_detail_data}[1]/span
    Should Be Equal    ${detail_info1_status}    ${status}
    ${detail_info2_datetime}    Get Text    //*[@class="request-detail"][2]/span
    ${today}    Get Current Date    result_format=%m/%d/%Y
    Should Contain    ${detail_info2_datetime}    ${today}
    ${detail_info3_clinic}    Get Text    //*[@class="request-detail"][3]/span
    Should Be Equal    ${detail_info3_clinic}    ${automated_tests_clinic}[name]
    ${detail_info4_requester}    Get Text    //*[@class="request-detail"][4]/span
    Should Be Equal    ${detail_info4_requester}    f13cu02 manager
    ${detail_info5_description}    Get Text    //*[@class="request-detail"][5]/span
    Should Be Equal    ${detail_info5_description}    ${data_export_description}
    Wait Until Element Is Visible    ${data_export_approval_options}/ul
    IF    '${detail_info1_status}' == 'Pending'
        Page Should Contain    ${data_export_approval_modal_info_text1}
        Page Should Contain    ${data_export_approval_modal_info_text2}
    END

Admin User Closes The Data Export Request Modal
    Try To Click Element    ${data_export_approval_modal_close_button}

Verify Data Export Request Table Header Cells
    Element Text Should Be    ${data_export_request_table_header_row}//th[1]    Status
    Element Text Should Be    ${data_export_request_table_header_row}//th[2]    Date and time
    Element Text Should Be    ${data_export_request_table_header_row}//th[3]    Clinic
    Element Text Should Be    ${data_export_request_table_header_row}//th[4]    Requested by
    Element Text Should Be    ${data_export_request_table_header_row}//th[5]    Description
    Element Text Should Be    ${data_export_request_table_header_row}//th[6]    Approval

Admin Can Confirm The Approval Of The Data Export Request
    Try To Click Element    ${approve_data_export_request_button}
    Wait Until Element Is Visible    ${data_export_confirmation_header_text}
    Element Should Be Visible    ${data_export_confirmation_texts}
    Element Should Be Visible    ${data_export_request_cancel_button}
    Try To Click Element    ${data_export_request_confirm_button}

Admin Can See Approval Confirmation And Click Ok
    Wait Until Element Is Visible    ${data_export_request_confirmation_modal_header}
    Element Should Be Visible    ${data_export_request_confirmation_modal_text}
    Try To Click Element    ${data_export_request_confirmation_ok_button}

Approve Existing Pending Requests
    Login As Admin With 2FA
    Admin User Navigates To Exports Tab
    ${pending_request_element}    Format String    ${pending_data_export_request}    f13cu02 manager
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    (${pending_request_element})[last()]    timeout=3s
    IF    ${status}
        ${count}    Get Element Count    ${pending_request_element}
        IF    ${count} >= 1
            FOR    ${INDEX}    IN RANGE    1    ${count}+1
                Try To Click Element    (${pending_request_element})[${INDEX}]
                Admin Can Confirm The Approval Of The Data Export Request
                Admin Can See Approval Confirmation And Click Ok
            END
        END
    END
    Close Browser

Download Approved Data Export Request If There's Any
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${data_export_download_link}    timeout=3s
    IF    ${status}
        Try To Click Element    ${data_export_download_link}
        Wait Until Page Contains    ${clinic_data_export_page_info_text}
    END