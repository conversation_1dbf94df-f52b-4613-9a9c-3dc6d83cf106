*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings -> Integration

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}medication_list.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web    noona_admin


*** Test Cases ***
Main success scenario - Integration - FHIR Integration Settings
    [Tags]    nms9-ver-449-1    nms9-ver-449    manual
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of FHIR Integration Setting
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable FHIR Integration Settings
        Save Settings
        Enable FHIR Integration And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable FHIR Integration And Save The Settings
        ${current_status}    Run Keyword    Check Status Of FHIR Integration Setting    # check if update is successful
        Should Be Equal    ${current_status}    enabled
    END

Main Success Scenario - Integration - Clinic Integration Setting And Sub-Settings
    [Tags]    nms9-ver-449-1    nms9-ver-449    manual
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Integration For Clinic Setting
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    Yes
        Input Required Fields Value For Patient Status Change Integration Settings
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    Yes
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    Medical record number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    Yes
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    Yes
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    Yes
        Save Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    No
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    No
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    SSN / Identity Code / INS number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    No
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    No
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    No
        Save Settings
        Disable Integration For Clinic Settings
        Save Settings
        Enable Integration For Clinic And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Integration For Clinic And Save The Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    Yes
        Input Required Fields Value For Patient Status Change Integration Settings
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    Yes
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    Medical record number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    Yes
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    Yes
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    Yes
        Save Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    No
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    No
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    SSN / Identity Code / INS number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    No
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    No
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    No
        Save Settings
        Disable Integration For Clinic Settings
        Save Settings
    END

Main Success Scenario - Integration - Is Data Lake Integration Enabled
    [Tags]    nms9-ver-449-1    nms9-ver-449    manual
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Data Lake Integration
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Input Data Lake Account Name
        Input Data Lake Container Name
        Input Data Lake Encryption Key URL
        Input Data Lake Public Key
        Input Data Lake Shared Access Signature Token
        Save Settings
        Disable Data Lake Integration And Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Data Lake Integration And Save Settings
        Input Data Lake Account Name
        Input Data Lake Container Name
        Input Data Lake Encryption Key URL
        Input Data Lake Public Key
        Input Data Lake Shared Access Signature Token
        Save Settings
        Remove Values From Data Lake Setting Fields
        Disable Data Lake Integration And Save Settings
    END

Extension F - Clinic setting to manage Medication feature
    [Tags]    nms9-ver-404
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Medication List Settings
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable Medication List Settings
        Save Settings
        Enable Medication List And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Medication List And Save The Settings
        ${current_status}    Run Keyword    Check Status Of Medication List Settings    # check if update is successful
        Should Be Equal    ${current_status}    enabled
    END
    Login As Patient    ${f03na01_patient2}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Logout As Patient
    Close Browser
    [Teardown]    Extension F - Test teardown

Extension I - Enable Common FHIR Integration
    [Tags]    nms9-ver-480
    Extension I - Test Setup
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    Yes
    Wait Until Page Contains Element    ${customer_tenant_id}
    Input Text    ${customer_tenant_id}    test${now}
    Enable Data Integrations    DiagnosticReports
    Enable Data Integrations    DocumentReferences
    Enable Data Integrations    MedicationStatements
    Enable Data Integrations    Quality of life questionnaires
    Enable Data Integrations    Symptoms
    Enable Data Integrations    Translations
    Save Settings
    Reload Page
    Verify If The Common FHIR Integration Are Saved
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    No
    Save Settings
    Wait Until Page Contains    ${fhir_integration_settings_enabled_text}
    Page Should Not Contain     ${customer_tenant_id}
    Close Browser


*** Keywords ***
Enable Medication List And Save The Settings
    Enable Medication List Settings
    Save Settings

Extension F - Test teardown
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Disable Medication List Settings
    Save Settings
    ${current_status}    Run Keyword    Check Status Of Medication List Settings    # check if update is successful
    Should Be Equal    ${current_status}    disabled
    Close All Browsers

Extension I - Test Setup
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    Yes
    Wait Until Element Is Visible    ${customer_tenant_id}
    Clear Element Text    ${customer_tenant_id}
    Disable Data Integrations    DiagnosticReports
    Disable Data Integrations    DocumentReferences
    Disable Data Integrations    MedicationStatements
    Disable Data Integrations    Quality of life questionnaires
    Disable Data Integrations    Symptoms
    Disable Data Integrations    Translations
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    No
    Save Settings
    ${now}    Get Current Date    result_format=%d%Y%m
    Set Test Variable    ${now}

Verify If The Common FHIR Integration Are Saved
    Wait Until Element Is Visible    ${customer_tenant_id}
    ${tenant_id}    Get Value    ${customer_tenant_id}
    Should Be Equal    ${tenant_id}    test${now}
    Verify Data Integration Setting Status    DiagnosticReports    enabled
    Verify Data Integration Setting Status    DocumentReferences    enabled
    Verify Data Integration Setting Status    MedicationStatements    enabled
    Verify Data Integration Setting Status    Quality of life questionnaires    enabled
    Verify Data Integration Setting Status    Symptoms    enabled
    Verify Data Integration Setting Status    Translations    enabled
