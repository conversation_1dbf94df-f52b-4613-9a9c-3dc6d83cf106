*** Settings ***
Documentation       Example accessibility tests using axe-core
...                 This demonstrates how to integrate accessibility testing
...                 into your existing Robot Framework test suite

Resource            ${EXECDIR}${/}resources${/}accessibility.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource

Suite Setup         Set Libraries Order
Suite Teardown      Accessibility Test Teardown
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    accessibility-wcag-aa    example

*** Variables ***
${ACCESSIBILITY_ENABLED}    True

*** Test Cases ***
Patient Login Page Accessibility
    [Documentation]    Test accessibility of patient login page
    [Tags]    patient-web    login    wcag-aa
    
    # Navigate to page
    Open Patient Login Page
    
    # Run accessibility tests
    Run Accessibility Test Suite    patient_login_page    wcag_level=AA
    
    # Specific checks for login forms
    Check Form Accessibility
    Check Keyboard Navigation
    
    [Teardown]    Close Browser

Form Accessibility Test
    [Documentation]    Test form accessibility specifically
    [Tags]    forms    wcag-aa
    
    # Navigate to a form page (example: patient registration)
    Open Patient Login Page
    
    # Test form-specific accessibility
    Check Form Accessibility
    Check Image Accessibility
    
    # Test keyboard navigation through form
    Check Keyboard Navigation
    
    [Teardown]    Close Browser

Color Contrast Compliance
    [Documentation]    Test color contrast compliance across pages
    [Tags]    color-contrast    wcag-aa
    
    # Test multiple pages for color contrast
    @{pages}=    Create List    patient_login    nurse_login
    
    FOR    ${page}    IN    @{pages}
        IF    '${page}' == 'patient_login'
            Open Patient Login Page
        ELSE IF    '${page}' == 'nurse_login'
            Open Nurse Login Page
        END
        
        ${violations}=    Check Color Contrast    fail_on_violations=False
        ${violation_count}=    Get Length    ${violations}
        Log    ${page}: Found ${violation_count} color contrast violations
        
        Close Browser
    END

Keyboard Navigation Test
    [Documentation]    Test keyboard navigation accessibility
    [Tags]    keyboard    navigation    wcag-aa
    
    Open Patient Login Page
    
    # Test tab order and keyboard accessibility
    Check Keyboard Navigation
    
    # Test specific keyboard interactions
    Press Keys    None    TAB
    ${focused_element}=    Get WebElement    css:*:focus
    Element Should Be Visible    ${focused_element}
    
    # Test Enter key on buttons/links
    ${tag_name}=    Get Element Attribute    ${focused_element}    tagName
    IF    '${tag_name.lower()}' in ['button', 'a']
        Press Keys    None    RETURN
        Sleep    1s
    END
    
    [Teardown]    Close Browser

Screen Reader Compatibility
    [Documentation]    Test screen reader compatibility
    [Tags]    screen-reader    aria    wcag-aa
    
    Open Patient Login Page
    
    # Check ARIA attributes and labels
    ${aria_rules}=    Create List    
    ...    aria-valid-attr
    ...    aria-valid-attr-value
    ...    aria-required-attr
    ...    aria-required-children
    ...    aria-required-parent
    
    ${violations}=    Check Specific Rules    ${aria_rules}
    
    # Check for proper labeling
    ${label_rules}=    Create List    label    aria-label    aria-labelledby
    ${label_violations}=    Check Specific Rules    ${label_rules}    fail_on_violations=False
    
    [Teardown]    Close Browser

*** Keywords ***
Open Patient Login Page
    [Documentation]    Navigate to patient login page
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Wait Until Page Contains Element    css:input[type="email"]    timeout=10s

Run Conditional Accessibility Test
    [Documentation]    Run accessibility test only if enabled
    [Arguments]    ${test_name}    ${test_keyword}
    
    IF    ${ACCESSIBILITY_ENABLED}
        Log    Running accessibility test: ${test_name}
        Run Keyword    ${test_keyword}
    ELSE
        Log    Accessibility testing disabled, skipping: ${test_name}
    END
