*** Settings ***
Documentation       Example accessibility tests using axe-core
...                 This demonstrates how to integrate accessibility testing
...                 into your existing Robot Framework test suite

Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}cookies.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}accessibility${/}missing_accessible_names_checker.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    accessibility-wcag-aa    example

*** Variables ***
${ACCESSIBILITY_ENABLED}    True

*** Test Cases ***
Patient Login Page Cookie Consent Accessibility
    [Documentation]    Test accessibility of patient login page with cookie consent modal
    [Tags]    patient-web    login    cookies    wcag-aa

    # Navigate to patient login page
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}

    # Wait for cookie consent modal to appear
    Wait Until Element Is Visible    ${cookies_image}    timeout=30s

    # Check accessibility with cookie consent modal open
    Log    Checking accessibility with cookie consent modal open
    ${issues_with_modal}=    Run Custom Accessibility Check    fail_on_issues=False
    Log    Found ${issues_with_modal.__len__()} accessibility issues with cookie modal open

    # Verify cookie settings are displayed properly
    Check That Cookie Settings Are Displayed

    # Accept all cookies
    Select All Cookies

    # Wait for modal to disappear
    Wait Until Element Is Not Visible    ${cookies_image}    timeout=10s

    # Check accessibility after accepting cookies
    Log    Checking accessibility after accepting cookies
    ${issues_after_accept}=    Run Custom Accessibility Check    fail_on_issues=False
    Log    Found ${issues_after_accept.__len__()} accessibility issues after accepting cookies

    # Compare results
    ${modal_count}=    Get Length    ${issues_with_modal}
    ${after_count}=    Get Length    ${issues_after_accept}
    Log    Accessibility issues: ${modal_count} with modal, ${after_count} after accepting

    [Teardown]    Close Browser

Cookie Modal Keyboard Navigation Test
    [Documentation]    Test keyboard navigation through cookie consent modal
    [Tags]    keyboard    navigation    cookies    wcag-aa

    # Navigate to patient login page
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}

    # Wait for cookie consent modal
    Wait Until Element Is Visible    ${cookies_image}    timeout=30s

    # Test keyboard navigation through cookie modal
    Press Keys    None    TAB
    ${focused_element}=    Get WebElement    css:*:focus
    Element Should Be Visible    ${focused_element}

    # Navigate to necessary cookies button
    Press Keys    None    TAB
    Press Keys    None    TAB

    # Test that we can activate buttons with keyboard
    Press Keys    None    RETURN

    # Verify modal is dismissed
    Wait Until Element Is Not Visible    ${cookies_image}    timeout=10s

    Log    Cookie modal keyboard navigation test completed

    [Teardown]    Close Browser

*** Keywords ***
Wait For Cookie Modal
    [Documentation]    Wait for cookie consent modal to appear
    [Arguments]    ${modal_type}=patient

    IF    '${modal_type}' == 'patient'
        Wait Until Element Is Visible    ${cookies_image}    timeout=30s
    ELSE IF    '${modal_type}' == 'clinic'
        Wait Until Element Is Visible    ${clinic_cookies_image}    timeout=30s
    END

Check Accessibility With Custom Checker
    [Documentation]    Run the custom accessibility checker and return results
    [Arguments]    ${container_id}=    ${EMPTY}    ${fail_on_issues}=False

    ${issues}=    Run Custom Accessibility Check
    ...    container_id=${container_id}
    ...    fail_on_issues=${fail_on_issues}

    ${issue_count}=    Get Length    ${issues}
    Log    Found ${issue_count} accessibility issues

    RETURN    ${issues}

Log Accessibility Summary
    [Documentation]    Log a summary of accessibility test results
    [Arguments]    ${test_name}    ${issues}

    ${issue_count}=    Get Length    ${issues}
    Log    ${test_name}: Found ${issue_count} accessibility issues

    IF    ${issue_count} > 0
        Log    Issues found in ${test_name}    WARN
        FOR    ${issue}    IN    @{issues}
            Log    - ${issue}[message]    WARN
        END
    ELSE
        Log    No accessibility issues found in ${test_name}
    END
