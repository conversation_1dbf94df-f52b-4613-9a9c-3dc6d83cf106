*** Settings ***
Documentation       Comprehensive accessibility tests for patient-facing pages
...                 Tests WCAG 2.1 AA compliance across patient workflows

Resource            ${EXECDIR}${/}resources${/}accessibility.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Setup Accessibility Testing
Suite Teardown      Accessibility Test Teardown
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    patient-web    wcag-aa

*** Test Cases ***
Patient Login Accessibility
    [Documentation]    Test accessibility of patient login workflow
    [Tags]    login    wcag-aa
    
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    
    # Run comprehensive accessibility tests
    Run Accessibility Test Suite    patient_login    wcag_level=AA
    
    [Teardown]    Close Browser
