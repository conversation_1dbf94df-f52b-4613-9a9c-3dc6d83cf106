*** Settings ***
Documentation       Comprehensive accessibility tests for patient-facing pages
...                 Tests WCAG 2.1 AA compliance across patient workflows

Resource            ${EXECDIR}${/}resources${/}accessibility.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Setup Accessibility Testing
Suite Teardown      Accessibility Test Teardown
Test Timeout        ${TEST_TIMEOUT}

Force Tags          accessibility    patient-web    wcag-aa

*** Test Cases ***
Patient Login Accessibility
    [Documentation]    Test accessibility of patient login workflow
    [Tags]    login    wcag-aa
    
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    
    # Run comprehensive accessibility tests
    Run Accessibility Test Suite    patient_login    wcag_level=AA
    
    # Test login form specifically
    Check Form Accessibility
    Check Keyboard Navigation
    
    [Teardown]    Close Browser

Patient Dashboard Accessibility
    [Documentation]    Test accessibility of patient dashboard after login
    [Tags]    dashboard    wcag-aa
    
    Login As Patient    ${CLINIC_PATIENT}[email]
    
    # Test dashboard accessibility
    Run Accessibility Test Suite    patient_dashboard    wcag_level=AA
    
    # Check specific dashboard elements
    Check Heading Structure
    Check Color Contrast
    
    [Teardown]    Close Browser

Patient Diary Accessibility
    [Documentation]    Test accessibility of patient diary functionality
    [Tags]    diary    forms    wcag-aa
    
    Login As Patient    ${CLINIC_PATIENT}[email]
    Go To Diary
    
    # Test diary page accessibility
    Run Accessibility Test Suite    patient_diary    wcag_level=AA
    
    # Test form interactions
    Check Form Accessibility
    Check Image Accessibility
    
    [Teardown]    Close Browser

Patient Message Center Accessibility
    [Documentation]    Test accessibility of patient message center
    [Tags]    messages    wcag-aa
    
    Login As Patient    ${CLINIC_PATIENT}[email]
    # Navigate to message center (adjust based on your app structure)
    
    # Test message center accessibility
    Run Accessibility Test Suite    patient_messages    wcag_level=AA
    
    # Test specific message functionality
    Check Keyboard Navigation
    Check Focus Management
    
    [Teardown]    Close Browser

Patient Mobile Accessibility
    [Documentation]    Test accessibility on mobile viewport
    [Tags]    mobile    responsive    wcag-aa
    
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    375    667    # iPhone SE size
    
    # Test mobile accessibility
    Run Accessibility Test Suite    patient_mobile    wcag_level=AA
    
    # Test mobile-specific interactions
    Check Keyboard Navigation
    Check Color Contrast
    
    [Teardown]    Close Browser

*** Keywords ***
Login As Patient
    [Documentation]    Login as patient and verify successful login
    [Arguments]    ${email}
    
    Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    
    Input Text    css:input[type="email"]    ${email}
    Input Text    css:input[type="password"]    ${DEFAULT_PASSWORD}
    Click Button    css:button[type="submit"]
    
    # Wait for successful login
    Wait Until Page Contains Element    css:.dashboard    timeout=10s

Go To Diary
    [Documentation]    Navigate to patient diary
    
    # Adjust selector based on your app structure
    Click Element    css:a[href*="diary"]
    Wait Until Page Contains Element    css:.diary-container    timeout=10s
