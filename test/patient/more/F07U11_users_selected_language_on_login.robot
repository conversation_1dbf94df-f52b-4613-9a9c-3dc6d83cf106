*** Settings ***
Documentation       F07U11 User's selected language on login
...                 Pre-conditions:
...                 1. User has an active account
...                 2. Clinic languages available EN, FI, and FR default is FI for TA clinic Language Testing1
...                 3. Clinic languages available EN, FI default is FI for TA clinic Language Testing2
...                 Test clinic: TA clinic Language Testing1
...                 TA clinic Language Testing2

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u11    patient-web


*** Variables ***
${language_option_label}                                        //div[@class="form-group"]//descendant::div
${landing_page_title_web}                                       //h1[@class='login-title']
${landing_page_title_native_app}                                //h2[contains(@class, "text")]
${landing_page_title_ENG}                                       Log in to Noona
${landing_page_title_ENG_app}                                   Connect to your clinic using Noona
${landing_page_title_FIN}                                       Kirjaudu Noona-palveluun
${landing_page_title_FIN_app}                                   Muodosta yhteys klinikkaasi Noonan avulla
${landing_page_title_DE}                                        Anmelden bei Noona
${landing_page_title_DE_app}                                    Mit Noona eine Verbindung zu Ihrer Klinik herstellen
${language_label_placeholder}                                   //label[contains(text(), "{}")]
${language_label_ENG}                                           //*[contains(text(), "English")]
${language_label_FIN}                                           //*[contains(text(), "Suomi")]
${clinic_preferences_page_title}                                //*[contains(@class, "section-title")]
${clinic_preferences_page_title_text_FI}                        Klinikka-asetukset
${clinic_preferences_page_title_text_ENG}                       Clinic preferences
${library_page_header}                                          //*[@id="header-page-title"]
${library_page_header_android}                                  //*[contains(@resource-id, 'header-page-title')]
${library_page_header_text_FIN}                                 Kirjasto
${library_page_header_text_ENG}                                 Library
${library_delegate_user_logout_button}                          //*[@data-testid="library-delegate-logout-button"]
${library_delegate_user_logout_button_text_FIN}                 KIRJAUDU ULOS
${library_delegate_user_logout_button_text_ENG}                 LOG OUT
${library_delegate_user_policy_link}                            //*[@data-testid="library-privacy-policy-link"]
${library_delegate_user_policy_link_text_FIN}                   TIETOSUOJAKÄYTÄNTÖ
${library_delegate_user_policy_link_text_ENG}                   PRIVACY STATEMENT
${library_patient_education_card_header}                        //*[@data-testid="library-patient-education"]/div/h3/span
${library_patient_education_card_header_text_ENG}               Education documents
${library_patient_education_card_header_text_FIN}               Ohjaavat asiakirjat
${library_delegate_user_patient_education_card_introduction}    //*[@data-testid="library-patient-education"]/div/p
${library_patient_education_card_introduction_text_ENG}         All the useful links and documents your clinic has sent to you.
${library_patient_education_card_introduction_text_FIN}         Kaikki hyödylliset linkit ja asiakirjat, jotka klinikkasi on lähettänyt.
${clinic_preferences_page_title_text_FRA}                       Préférences de la clinique
${language_label_FRA}                                           //*[contains(text(), "Français")]


*** Test Cases ***
User's selected language on login - Main success scenario
    [Tags]    nms9-ver-430    native-web
    [Setup]    Setup App Environment
    User Selects Preferred Language On OIDC Login Page    Suomi
    Login As User With Selected Language    patient    ${f07u11_finnish_patient1}[email]    Suomi
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    Suomi
    Logout As Patient
    Select Language After Logout
    Login As User With Selected Language    patient    ${f07u11_finnish_patient1}[email]    English
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    English
    [Teardown]    Close All App Instances

Extension A - User logs in with a language that is not available on the clinic
    [Tags]    nms9-ver-431    patient-web
    [Setup]    Setup App Environment
    Patient Set Finnish As Browser Default Language
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    Suomi
    Logout As Patient
    Select Language After Logout    Deutsch
    User Sees Noona Landing Page In Deutsch
    Login As User With Selected Language    patient    ${f07u11_finnish_patient2}[email]    Deutsch
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    Suomi
    Clinic Preferences Page Displays English And Suomi As Available Languages
    Clinic Preferences Page Does Not Display Unavailable Language    Deutsch
    Logout As Patient
    Select Language After Logout    English
    User Sees Noona Landing Page In English
    Login As User With Selected Language    patient    ${f07u11_finnish_patient2}[email]    English
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    English
    Logout As Patient
    Select Language After Logout    Deutsch
    User Sees Noona Landing Page In Deutsch
    Login As User With Selected Language    patient    ${f07u11_finnish_patient2}[email]    Deutsch
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    English
    Clinic Preferences Page Displays English And Suomi As Available Languages
    Clinic Preferences Page Does Not Display Unavailable Language    Deutsch
    [Teardown]    Close All App Instances

Extension B.1 - Delegate user Login with default language
    [Documentation]    Delegate user can see "Education documents" and "Medical Records" set by TA Clinic Language Testing1's clinic settings
    [Tags]    nms9-ver-432    native-web
    [Setup]    Setup App Environment
    User Selects Preferred Language On OIDC Login Page    Suomi
    User Sees Noona Landing Page In Suomi
    Login As User With Selected Language    delegate    ${f07u11_finnish_patient1}[delegate]    Suomi
    Patient Delegate Page Is Displayed In Selected Language    Suomi
    [Teardown]    Close All App Instances

Extension B.2 - Delegate user Login with other than default clinic language
    [Documentation]    Delegate user can see "Education documents" and "Medical Records" set by TA Clinic Language Testing1's clinic settings
    [Tags]    nms9-ver-433    native-web
    [Setup]    Setup App Environment
    User Selects Preferred Language On OIDC Login Page    Suomi
    User Sees Noona Landing Page In Suomi
    User Switches From Suomi To English
    Login As User With Selected Language    delegate    ${f07u11_finnish_patient2}[delegate]    English
    Patient Delegate Page Is Displayed In Selected Language    English
    [Teardown]    Close All App Instances

Extension B.3 - Delegate user Login with non Clinic supported language
    [Documentation]    Delegate user can see "Education documents" and "Medical Records" set by TA Clinic Language Testing1's clinic settings
    [Tags]    nms9-ver-434    native-web
    [Setup]    Setup App Environment
    User Selects Preferred Language On OIDC Login Page    Suomi
    User Sees Noona Landing Page In Suomi
    User Switches From Suomi To Norsk
    Login As User With Selected Language    delegate    ${f07u11_finnish_patient2}[delegate]    Norsk
    Patient Delegate Page Is Displayed In Selected Language    Suomi
    [Teardown]    Close All App Instances

Extension D.1 Single Patient, Multi Clinic (same languages available)
    [Documentation]    Test patient was registered first in TA Clinic Language Testing 2. Hence, this clinic
    ...    is the first valid clinic upon new login session.
    [Tags]    nms9-ver-438    patient-web
    [Setup]    Setup App Environment
    Extension D.1 Precondition - Patient Has Logged In App In Finnish
    User Sees Noona Landing Page In Suomi
    User Switches From Suomi To English
    Login As User With Selected Language    patient    ${f07u11_finnish_spmc1}[email]    English
    Verify Visited Clinic    ${language_testing_secondary_clinic}[name]
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    English
    Clinic Preferences Page Displays English And Suomi As Available Languages
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_clinic}[name]
    Verify Visited Clinic    ${language_testing_clinic}[name]
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    English
    Clinic Preferences Page Displays English And Suomi As Available Languages
    [Teardown]    Run Keywords    Extension D.1 - Test Teardown    AND    Close Browser

Extension D.2 Single Patient, Multi Clinic (Different languages available)
    [Tags]    nms9-ver-439    patient-web
    [Setup]    Setup App Environment
    Extension D.2 Precondition - Patient Has Logged In App In Finnish
    User Sees Noona Landing Page In Suomi
    User Switches From Suomi To Français
    Login As User With Selected Language    patient    ${f07u11_finnish_spmc1}[email]    Français
    Verify Visited Clinic    ${language_testing_clinic}[name]
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    Français
    Clinic Preferences Page Displays French Too As Available Languages With English and Suomi
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_secondary_clinic}[name]
    Verify Visited Clinic    ${language_testing_secondary_clinic}[name]
    Go To Clinic Preferences
    Clinic Preferences Page Is Visible In Selected Available Clinic Languages    Suomi
    Clinic Preferences Page Displays English And Suomi As Available Languages
    [Teardown]    Run Keywords    Extension D.2 - Test Teardown    AND    Close Browser
    # Todo: few test cases are failing for native with this MR change so that need to be fixed in next MR


*** Keywords ***
Clinic Preferences Page Is Visible In Selected Available Clinic Languages
    [Documentation]    TA clinic Language Testing1 has only 3 language options Suomi (default), English & French. This keyword can be extended for clinics which have other available languages.
    [Arguments]    ${selected_language}
    IF    '${selected_language}' == 'Suomi'
        Element Text Should Be    ${clinic_preferences_page_title}    ${clinic_preferences_page_title_text_FI}
    ELSE IF    '${selected_language}' == 'English'
        Element Text Should Be    ${clinic_preferences_page_title}    ${clinic_preferences_page_title_text_ENG}
    ELSE IF    '${selected_language}' == 'Français'
        Element Text Should Be    ${clinic_preferences_page_title}    ${clinic_preferences_page_title_text_FRA}
    END

User Selects Preferred Language On OIDC Login Page
    [Arguments]    ${prefered_ui_language}
    IF    'native' in '${ENVIRONMENT}'
        app_patient_login.Select Native App Language    ${prefered_ui_language}
    ELSE
        Open Page    ${PATIENT_LOGIN_URL}
        Accept All Cookies If Visible
        Set Test Variable    ${LANGUAGE_LABEL}    ${prefered_ui_language}
        shared_login.Select Language    ${language_value}
    END
    Sleep    3s

Login As User With Selected Language
    [Arguments]    ${user}    ${user_email}    ${selected_language}
    IF    'native' in '${ENVIRONMENT}'
        Login To Native App From Landing Page With Selected Language    ${user_email}    ${selected_language}
        IF    '${user}' == 'patient'
            Allow Notifications In Selected Language    ${selected_language}
        END
    ELSE
        Login As Patient From OIDC Login Page With Selected Language    ${user_email}    ${selected_language}
    END

Select Language After Logout
    [Arguments]    ${language}=English
    # wait for page to load aftering logging out
    Sleep    3s
    IF    'native' in '${ENVIRONMENT}'
        app_patient_login.Select Native App Language    ${language}
    ELSE
        Set Test Variable    ${LANGUAGE_LABEL}    ${language}
        shared_login.Select Language    ${language_value}
    END
    # wait for language change kicks in
    Sleep    1s

User Sees Noona Landing Page In ${language}
    [Documentation]    This keyword compares the landing page title text in different selected language
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Contains Element    ${landing_page_title_native_app}    ${SYS_VAR_PAGE_TIMEOUT}
        Wait Until Element Is Visible    ${landing_page_title_native_app}
        ${landing_page_title_text}    Get Text    ${landing_page_title_native_app}
    ELSE
        Wait Until Page Contains Element    ${landing_page_title_web}    ${SYS_VAR_PAGE_TIMEOUT}
        Wait Until Element Is Visible    ${landing_page_title_web}
        ${landing_page_title_text}    Get Text    ${landing_page_title_web}
    END
    # The native app landing page was not fully redesigned for INS login, just button name was changed.
    IF    'native' not in '${ENVIRONMENT}'
        IF    '${language}' == 'English'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_ENG}
        ELSE IF    '${language}' == 'Suomi'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_FIN}
        ELSE IF    '${language}' == 'Deutsch'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_DE}
        END
    ELSE
        IF    '${language}' == 'English'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_ENG_app}
        ELSE IF    '${language}' == 'Suomi'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_FIN_app}
        ELSE IF    '${language}' == 'Deutsch'
            Should Be Equal    ${landing_page_title_text}    ${landing_page_title_DE_app}
        END
    END
    Set Test Variable    ${landing_page_language}    ${language}
    Accept All Cookies If Visible

Clinic Preferences Page Displays English And Suomi As Available Languages
    Page Should Contain Element    ${language_label_ENG}    limit=1
    Page Should Contain Element    ${language_label_FIN}    limit=1

Patient Delegate Page Is Displayed In Selected Language
    [Arguments]    ${selected_language}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        IF    '${PLATFORM_NAME}' == 'ios'
            Switch To Context    ${contexts}[1]
        ELSE IF    '${PLATFORM_NAME}' == 'android'
            Switch To Context    NATIVE_APP
        END
    END
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}' == 'android'
            Wait Until Page Contains Element       ${library_page_header_android}
        END
    ELSE
        Wait Until Page Contains Element    ${library_page_header}
    END
    IF    '${selected_language}' == 'English'
        IF    'native' in '${ENVIRONMENT}'
            IF    '${PLATFORM_NAME}' == 'android'
                Element Text Should Be          ${library_page_header_android}     ${library_page_header_text_ENG}
            END
        ELSE
            Element Text Should Be      ${library_page_header}    ${library_page_header_text_ENG}
            Element Text Should Be
            ...    ${library_delegate_user_logout_button}
            ...    ${library_delegate_user_logout_button_text_ENG}
            Element Text Should Be    ${library_delegate_user_policy_link}    ${library_delegate_user_policy_link_text_ENG}
            Element Text Should Be
            ...    ${library_patient_education_card_header}
            ...    ${library_patient_education_card_header_text_ENG}
            Element Text Should Be
            ...    ${library_delegate_user_patient_education_card_introduction}
            ...    ${library_patient_education_card_introduction_text_ENG}
        END
    ELSE IF    '${selected_language}' == 'Suomi'
        IF    'native' in '${ENVIRONMENT}'
            IF    '${PLATFORM_NAME}' == 'android'
                Element Text Should Be           ${library_page_header_android}      ${library_page_header_text_FIN}
                Text Should Be In The Page       ${library_delegate_user_logout_button_text_FIN}
                Text Should Be In The Page       ${library_delegate_user_policy_link_text_FIN}
                Text Should Be In The Page       ${library_patient_education_card_header_text_FIN}
            END
        ELSE
            Element Text Should Be    ${library_page_header}    ${library_page_header_text_FIN}
            Element Text Should Be
            ...    ${library_delegate_user_logout_button}
            ...    ${library_delegate_user_logout_button_text_FIN}
            Element Text Should Be    ${library_delegate_user_policy_link}    ${library_delegate_user_policy_link_text_FIN}
            Element Text Should Be
            ...    ${library_patient_education_card_header}
            ...    ${library_patient_education_card_header_text_FIN}
        END
    END

Clinic Preferences Page Does Not Display Unavailable Language
    [Arguments]    ${unavailable_language}
    Page Should Not Contain Element    //*[contains(text(), "${unavailable_language}")]

Patient Set Finnish As Browser Default Language
    User Selects Preferred Language On OIDC Login Page    Suomi
    Login As User With Selected Language    patient    ${f07u11_finnish_patient2}[email]    Suomi
    Go To Clinic Preferences

User Switches From ${language_one} To ${language_two}
    IF    '${landing_page_language}' == '${language_one}'
        IF    'native' in '${ENVIRONMENT}'
            Select Native App Language    ${language_two}
        ELSE
            Select Language After Logout    ${language_two}
        END
    END

Extension D.1 Precondition - Patient Has Logged In App In Finnish
    User Selects Preferred Language On OIDC Login Page    Suomi
    Login As User With Selected Language    patient    ${f07u11_finnish_spmc1}[email]    Suomi
    Logout As Patient

Extension D.1 - Test Teardown
    [Documentation]    Switch back to the clinic when patient first login
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_secondary_clinic}[name]
    Verify Visited Clinic    ${language_testing_secondary_clinic}[name]
    Logout As Patient

Extension D.2 Precondition - Patient Has Logged In App In Finnish
    User Selects Preferred Language On OIDC Login Page    Suomi
    Login As User With Selected Language    patient    ${f07u11_finnish_spmc1}[email]    Suomi
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_secondary_clinic}[name]
    Verify Visited Clinic    ${language_testing_secondary_clinic}[name]
    Go To Clinic Preferences
    Verify Which Language Is Selected In Clinic Preferences And Change To Expected Language    Suomi
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_clinic}[name]
    Verify Visited Clinic    ${language_testing_clinic}[name]
    Logout As Patient

Extension D.2 - Test Teardown
    [Documentation]    Switch back to the clinic when patient first login
    Go To Diary
    Switch Clinic From Clinic Header    ${language_testing_clinic}[name]
    Verify Visited Clinic    ${language_testing_clinic}[name]

Clinic Preferences Page Displays French Too As Available Languages With English and Suomi
    Page Should Contain Element    ${language_label_ENG}    limit=1
    Page Should Contain Element    ${language_label_FIN}    limit=1
    Page Should Contain Element    ${language_label_FRA}    limit=1
