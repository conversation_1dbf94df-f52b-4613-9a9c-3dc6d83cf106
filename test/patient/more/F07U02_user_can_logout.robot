*** Settings ***
Documentation       F07U02 User can logout

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u02


*** Variables ***
${mgmt_landing_page_login_button}       //*[@id="open-login"]


*** Test Cases ***
Clinic User Can Log Out
    [Tags]    nms9-ver-247    clinic-web
    Login as Nurse    email=${automated_tests_clinic}[default_user]    clinic=${automated_tests_clinic}[name]
    Clinic User Clicks Log Out
    Clinic User Is Logged Out
    Close All Browsers

Clinic Admin Can Log Out
    [Documentation]    Clinic Admin Is Logging In To Clinic
    [Tags]    nms9-ver-247    clinic-web
    Login As Nurse    email=${automated_tests_clinic}[clinic_admin]    clinic=${automated_tests_clinic}[name]
    Clinic User Clicks Log Out
    Clinic User Is Logged Out
    Close All Browsers

Clinic Patient Can Log Out
    [Tags]    nms9-ver-248    patient-web
    Login As Patient
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    Close All Browsers

Extension A - With two-factor authentication - Nurse (web / app)
    [Tags]    nms9-ver-249-1    nurse-2fa    sms
    Login As Nurse With 2FA    ${mailosaur_sms}[f07u02_user_email]
    Clinic User Clicks Log Out
    Clinic User Is Logged Out
    Close Browser

Extension A - With two-factor authentication - Patient (web / app)
    [Tags]    nms9-ver-249-2    native-web    patient-2fa
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Login As Patient With 2FA    ${f07u02_2fa_patient}[email]
    ELSE
        Login To Native App With 2FA In Browserstack
        ...    email=${f07u02app_patient_extD}[email]
        ...    autho=yes
    END
    Logout As Patient
    [Teardown]    Close All App Instances
