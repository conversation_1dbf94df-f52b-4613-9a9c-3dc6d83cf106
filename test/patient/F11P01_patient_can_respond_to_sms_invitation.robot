*** Settings ***
Documentation       F11P01 Patient can respond to SMS invitation
...                 Preconditions:
...                 - Nurse has send SMS-invitation to this patient.
...
...                 [file:./docs/img/F11P01_sms_flow_chart.png|SMS flow chart]
...
...                 | =Name= | =Description= | =Validation Rule= | =(EN) Default= | =Translation key= | =Insert point= | =Min Length= | =Max Length= |
...                 | The patients will get an SMS notification of the messages sent by the clinic* | | Yes/No | Yes | - |
...                 | The clinic can send invitations to patients* | | Yes/No | No | - |
...                 | Invitation valid since creation of invitation (days) | Number of days after which the invitation expires | Accept numbers | 14 | - | | 1 | 4 |
...                 | Contents of the invitation message | Invited patients get SMS specified here | Accept chars | Hi, {clinic} would like to invite you to use Noona, an online service that allows you to communicate directly with your nurse and doctor. {link} You can find out more about the service and activate your account by following the link above. | sms.general.invitation | {link} = Account activation link {clinic} = Clinic name set in clinic information | 1 | 1600 |
...                 | Invitation delay since creation of invitation (days) | Number of days after which the SMSs are sent | Accept numbers | 0 | - | | 1 | 4 |
...                 | Contents of the reminder message (invitation expiry date = {invitationExpiryDate}, clinic name = {clinic}, link to start activation = {link}, required)* | Reminder message if the patient has not activated the account from SMS invitation. | Accept chars | Hi, please remember to activate your account in the Noona online service provided by {clinic}. Your invitation expires at {invitationExpiryDate}. {link} | sms.general.invitationReminder | {link} = Account activation link; {clinic} = Clinic name set in clinic information; {invitationExpiryDate} = Date when the login link expires | 1 | 1600 |
...                 | SMS reminder delay since creation of invitation (days)* | Number of days after which the reminder is sent Accept numbers | 3 | | | 1 | 4 |
...                 | Login information sent SMS content, password expiration date = {passwordExpirationDate}* | Message sent to patient when patient is sent a login link when nurse creates an account | Accept chars | Hi, {clinic} would like to invite you to use Noona, an online service that allows you to communicate directly with your nurse and doctor. {link} You can find out more about the service and activate your account by following the link above. The link has also been sent to your email. | sms.general.loginInformationSent | {link} = Account activation link; {clinic} = Clinic name set in clinic information | 1 | 1600 |
...                 | Login reminder SMS content, expiration date = {invitationExpiryDate}* | Reminder message if the patient has not activated the account from nurse created account. | Accept chars | Hi, please remember to activate your account in the Noona online service provided by {clinic}. Your invitation expires on {invitationExpiryDate}. {link} This reminder has also been sent to your email. | sms.general.loginReminder | {link} = Account activation link; {clinic} = Clinic name set in clinic information; {invitationExpiryDate} = Date when the login link expires | 1 | 1600 |

Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}


Force Tags          usecase-f11p01    patient-web

*** Variables ***
${tunit}                        666
${birth_date}                   1990-10-10

*** Test Cases ***
Main success scenario (web)
    [Tags]    nms9-ver-36    sms
    [Documentation]    Test case is only checking flow with the email added in the SMS invitation; additional TC needed if ticket NOONA-24744 will be fixed.
    Delete All Messages In Server    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}
    Invite Patient Via SMS Invitation
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${tunit}
    ...    ${birth_date}
    ...    phone_number=${mailosaur_number}
    @{sms_message_data}    Patient Received SMS Invitation To Use Noona    ${appointment_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Activate Noona Account As Patient    ${link}    ${patient_email}    terms_enabled=false    with_2fa=no
    Wait Until Location Contains    patient/#/diary-timeline
    Patient Is Asked Wellbeing In Diary
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[user_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}


Extension B - SMS invitation link is expired (web)
    [Tags]    manual    nms9-ver-37
    Patient receives SMS-invitation
    Patient clicks the link, but since the link is expired, patient is directed to invitation link expired page
    Patient is sent new invitation as SMS

Extension C - Email invitation link is expired (web)
    [Tags]    manual    nms9-ver-38
    Patient receives email-invitation
    Patient clicks the link, but since the link is expired, patient is directed to invitation link expired page
    Patient is sent new invitation as email
