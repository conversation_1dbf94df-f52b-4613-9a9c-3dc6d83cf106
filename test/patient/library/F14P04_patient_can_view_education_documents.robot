*** Settings ***
Documentation       F14P04 Patient can view education documents
...
...                 Preconditions:
...                 1. Patient is logged in to Noona.
...                 2. Clinic has Patient Education feature enabled.
...
...                 Main success scenario
...                 1. Patient has received at least one Patient Education message containing at least one attachment.
...                     # TODO: Opening Eduction Documents From Library
...                     # 2. <PERSON>ient navigates to Library.
...                     # 3. <PERSON><PERSON> clicks on the Education documents item.
...                 4. Education documents page is displayed.
...                 - a. Navigation at the bottom is displayed.
...                 - b. Patient can go back to the Library page (previous page) by clicking the back icon on the top left corner.
...                 - c. All the documents sent to <PERSON><PERSON> in a Patient Education message are displayed in the list.
...                 5. Patient clicks on a document in the list.
...                 6. Document is opened and displayed externally.
...                 7. Patient goes back to Noona application. The Education documents screen is still displayed.
...
...                 F14P04 Patient can view education documents - Extension A: Empty state
...                 1. Patient has not received any documents in a Patient Education message.
...                 2. Main success scenario from step 2 to 4.
...                 3. No documents are displayed in the Education documents page. Instead, an empty state message is displayed.
...
...                 F14P04 Patient can view education documents - Extension B: The document doesn’t exist anymore.
...                 1. Patient tries opening a document, but the document doesn’t exist anymore in Sharepoint.
...                 2. No new window of tab is opened.
...                 3. In Noona, an error message is displayed as a toaster at the top of the screen.
...                 4. Patient can continue using Noona normally.
...
...                 F14P04 Patient can view education documents - Extension C: Delegate user can view education documents
...                     # TODO: Opening Eduction Documents From Library
...                     # 1. Patient has invited a delegate user who has activated their account.
...                 2. Delegate user is directed to Library directly after logging in.
...                 3. Delegate user sees the same content as Patient (all Patient Education documents).
...                 - a. Delegate user is not able to view the original message
...                 4. Delegate user clicks on a document (the whole list item is clickable)
...                     # - a. if patient had opened a document, but delegate user has not, then delegate user sees the document as unread and patient sees it read
...                     # - b. if delegate user has opened a document, but patient has not, then patient sees the document as unread and delegate user sees it read
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_education.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_education.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f14p04    patient-education    patient-web


*** Test Cases ***
Patient Can View Education Documents
    [Tags]    nms9-ver-300    nms9-ver-304
    Enable Patient Education Settings    ${patient_education_clinic}[name]
    Add An Activated Patient Under Patient Education Clinic    f14p04-main
    Login As Nurse    ${patient_education_clinic}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Select Contact The Patient
    As A Nurse Send Education Message With Attachment
    Verify Patient Education Message As A Patient    ${patient_email}
    Open Education Article From Message    ${attached_education_document}
    Click See All Documents In The Library
    Open Education Document In Library    ${attached_education_document}
    Open Education Document From Action Menu
    Close Browser
    Set Email And Delete Previous Messages    ${f14p04_email_keys}[0]    ${f14p04_email_keys}[1]
    Add Delegate User Via API And Activate    ${patient_email}    ${patient_education_clinic}[name]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}    delegate ${first_name}    delegate ${family_name}
    Verify Delegate User Can Login    ${delegate_email}
    Click Education Documents Intro
    Open Education Document In Library    ${attached_education_document}
    Disable Patient Education Settings    ${patient_education_clinic}[name]
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${patient_education_clinic}[manager_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_EHR_TOKEN}

Extension A: Open document from the Action sheet
    [Tags]    nms9-ver-301
    Enable Patient Education Settings    ${patient_education_clinic}[name]
    Add An Activated Patient Under Patient Education Clinic    f14p04-exta
    Login As Nurse    ${patient_education_clinic}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Go To Patient Cases Tab
    As A Nurse Send Education Message With Attachment
    Verify Patient Education Message As A Patient    ${patient_email}
    Open Education Article From Message    ${attached_education_document}
    Click See All Documents In The Library
    Open Education Document In Library    ${attached_education_document}
    Open Education Document From Action Menu
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${patient_education_clinic}[manager_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_EHR_TOKEN}

Extension B - Empty State
    [Tags]    nms9-ver-302    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f14p04_extb}[email]
    Go To Library
    Go To Education Documents
    Wait Until Page Contains    Education documents
    Text Should Be In The Page    All the useful links and documents your clinic sends you are gathered below.
    Text Should Be In The Page    No documents yet.
    Navigate Back Using Header
    Wait Until Element Is Visible    ${discover_library_header}
    Wait Until Page Contains Element    ${medical_records_banner}
    Wait Until Page Contains Element    ${education_documents_link_in_library}

Extension C - The Document Doesn’t Exist Anymore
    [Documentation]    Add step to remove document from sharepoint (R&D)
    [Tags]    nms9-ver-303    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f14p04_re_doc}[email]
    Select Latest Clinic Message
    Verify Clinic Message Title Is Correct    Removed document
    Open Removed Document
    Click See All Documents In The Library
    Open Removed Document

Extension E - Attachment icon behavior - Info Not Sufficient
    [Tags]    nms9-ver-417-1    nms9-ver-417        native-web
    [Setup]    Setup App Environment
    Create A Patient And Send Patient Education Message
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Verify Attachment Icon For The Message    f14p04-exte ${now}    displayed
    Select Latest Clinic Message
    Verify Clinic Message Title Is Correct    f14p04-exte ${now}
    Verify Message Attachment    ${attachment_name_brain_tumors}
    Open Education Article From Message    ${attachment_name_brain_tumors}
    Close Clinic's Message Dialog
    Reload Clinic Page
    Verify Attachment Icon For The Message    f14p04-exte ${now}    displayed
    Attachment Icon Is Removed When Patient Answers If Info Is Sufficient    not sufficient
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - Attachment icon behavior - Info Is Sufficient
    [Tags]    nms9-ver-417-2    nms9-ver-417        native-web
    [Setup]    Setup App Environment
    Create A Patient And Send Patient Education Message
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Verify Attachment Icon For The Message    f14p04-exte ${now}    displayed
    Select Latest Clinic Message
    Verify Clinic Message Title Is Correct    f14p04-exte ${now}
    Verify Message Attachment    ${attachment_name_brain_tumors}
    Open Education Article From Message    ${attachment_name_brain_tumors}
    Close Clinic's Message Dialog
    Reload Clinic Page
    Verify Attachment Icon For The Message    f14p04-exte ${now}    displayed
    Attachment Icon Is Removed When Patient Answers If Info Is Sufficient    sufficient
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
As A Nurse Send Education Message With Attachment
    Verify Contact Patient Panel
    Contact Patient With Education Articles    PE Message Test    Patient Education    message_template=none
    Search Document Articles    Heart
    Select First Document In List
    Compare Selected Documents From Attached
    View Education Document In Clinic
    Save Education Document Attachment
    Verify Attached Document On Contact Patient Panel
    Send Message To Patient
    Close Browser

Verify Patient Education Message As A Patient
    [Arguments]    ${patient}
    Login As Patient    ${patient}
    Select Latest Clinic Message
    Verify Clinic Message Title Is Correct    PE Message Test
    Verify Clinic Education Message Is Correct    ${random_string_education}
    Verify Message Attachment    ${attached_education_document}

Go To Patient Cases Tab
    Try To Click Element    ${patient_cases_tab}

Attachment Icon Is Removed When Patient Answers If Info Is Sufficient
    [Arguments]    ${option}
    Select Latest Clinic Message
    IF    '${option}'=='not sufficient'
        Respond If The Info Sufficient As Patient    No    Additional question to Instructions-${now}
    ELSE
        Respond If The Info Sufficient As Patient    Yes
    END
    Reload Clinic Page
    Verify Attachment Icon For The Message    ${message_title}    not displayed

Create A Patient And Send Patient Education Message
    Add An Activated Patient Under Default Clinic    f14p04-exte
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    Set Test Variable    ${now}
    Send Contact Patient Request With Attachment
    ...    f14p04-exte ${now}
    ...    ${attachment_name_brain_tumors}
    ...    ${attachment_id_brain_tumors}
