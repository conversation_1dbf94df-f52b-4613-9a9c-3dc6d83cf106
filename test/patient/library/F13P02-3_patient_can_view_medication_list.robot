*** Settings ***
Documentation       F13P02-3 Pat<PERSON> can view medication list

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}medication_list.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p02-3    patient-web


*** Test Cases ***
Main - Patient can view medication list - Setting is disabled
    [Documentation]    Test case checks that medication list is not available in Library page if setting is disabled
    [Tags]    nms9-ver-418-1    nms9-ver-418    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_3_patient_1}[email]
    Go To Library
    Wait Until Element Is Visible    ${discover_library_header}
    Page Should Not Contain Element    ${medication_list_tile}

Main - Patient can view medication list - Setting is enabled
    [Documentation]    Test case checks that active medications are displayed for patient; step 2 in the main usecase is checked in the extension A
    [Tags]    nms9-ver-418-2    nms9-ver-418   native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_3_patient_2}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That All Medication List Page Elements Are Displayed
    Verify Medication List Content
    Verify Scroll Up Button Functionality
    Navigate Back Using Header

Extension A - Patient has no medications yet in ARIA
    [Tags]    nms9-ver-419    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_3_patient_3}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That Medication List Is Displayed With Empty State

Extension B - Error state for medication list
    [Tags]    nms9-ver-420    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_3_patient_4}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That Medication List Is Displayed With Error State
