*** Settings ***
Documentation       F07U05 User can request password reset
...                 Preconditions:
...                 - Patient is in login page.
...                 - Feature available only patients.

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/patient/single_patient_multi_clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u05    patient-web


*** Variables ***
${request_rejected}     The login instructions have already been sent to your email address


*** Test Cases ***
Main Success Scenario - User Can Request Password Reset
    [Tags]    nms9-ver-250-1    nms9-ver-250    native-web    patient-email-link    nms9-ver-549    nms9-ver-549-1
    [Setup]    Set Application On Environment
    Set Email And Delete Previous Messages    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Open Patient Page
    Request Password Reset    ${f07u05_patient}[email]
    Patient Received An Email About Reset Password
    ...    ${f07u05_patient}[email]
    ...    ${PATIENT_URL_IN_MESSAGE}/s/
    [Teardown]    Close All App Instances

Main Success Scenario - Patient Has Problems Logging In - Language Check
    [Tags]    nms9-ver-250-2    nms9-ver-250
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    END
    @{languages}    Create List    English    Suomi    Svenska    Norsk    Deutsch    Français
     ...    Español    Italiano    Português    Nederlands    Türkçe    Polski
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    FOR    ${language}    IN    @{languages}
        Wait Until Element Is Visible    ${landing_page_login_button}
        Select Patient Language In Login    ${language}
        Set Problems Logging In Language Variables    ${language}
        Try To Click Element    ${landing_page_login_button}
        Patient Clicks Problems Logging In Link    language=${language}
        Verify Problems Logging In Language Modal    ${language}
        Wait Until Element Is Visible    ${problems_logging_in_modal_cancel}
        Try To Click Element    ${problems_logging_in_modal_cancel}
        Wait Until Element Is Not Visible    ${problems_logging_in_modal_cancel}
    END
    [Teardown]    Close Browser

Extension A - Patient Has Requested Password Reset Within 15 Minutes
    [Tags]    nms9-ver-251    native-web
    [Setup]    Set Application On Environment
    Open Patient Page
    Request Password Reset    ${f07u05_patient}[email]
    Request Password Reset Rejected    ${f07u05_patient}[email]
    [Teardown]    Close All App Instances

Extension B - Nurse Has Locked Patient's Account
    [Tags]    nms9-ver-252    native-web
    [Setup]    Setup App Environment
    Create New Patient And Lock Account
    Open Patient Page
    Request Password Reset    ${patient_email}
    Patient Is Notified About Locked Account
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[name]
    Close All App Instances

Extension A - Patients is asked 2 factor authentication when they have requested for a new logging link theirselves (web / app)
    [Documentation]    This tc has been set to manual on 25.02.2025 until we have reconciled its associated usecase(s) with jama
    [Tags]    nms9-ver-31    patient-2fa    sms
    Set Test Variable    @{mailosaur_keys}    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Delete All Messages In Server    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Request Password Reset And Check Email    ${f07u05_patient_ext_c}[email]
    Delete All Messages In Server    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}
    Go To    ${link}
    ${sms_code}    User Received SMS
    Wait Until Element Is Visible    ${password_input_container_oidc_false}
    Input 2fa Code During Activation    ${sms_code}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    ${new_pw}    Set Variable    Pw-${now}
    Create New Password To Activate Account    password=${new_pw}
    Accept All Cookies If Visible
    Delete All Messages In Server    ${sms_server_id}    ${sms_api_key}
    Login As Patient From OIDC Login Page    ${f07u05_patient_ext_c}[email]    password=${new_pw}
    Input Patient Verification Code       #this step was added because clinic settings authentication for patient is pwd+2fa - even though patient setting is pwd only
    Patient Is In The Right Page    Diary
    [Teardown]    Close All App Instances

Extension D - Patient has multiple clinics associated with them (web / app)
    [Documentation]    This tc has been set to manual on 26.02.2025 until we have reconciled its associated usecase(s) with jama
    [Tags]    nms9-ver-32     patient-2fa      sms
    Set Test Variable    @{mailosaur_keys}    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Delete All Messages In Server    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Request Password Reset And Check Email
    ...    ${f07uo5_extd_patient}[email]
    ...    ${custom_branding_enabled_clinic}[custom_clinic_name]
    Delete All Messages In Server    ${sms_server_id}    ${sms_api_key}
    Go To    ${link}
    Wait Until Element Is Visible    ${password_input_container_oidc_false}
    Page Has Correct Logo    winter-city-publicdomain.svg
    ${sms_code}    User Received SMS
    Input 2fa Code During Activation    ${sms_code}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    ${new_pw}    Set Variable    Pw-${now}
    Create New Password To Activate Account    password=${new_pw}    check_next_button_color=rgba(81, 106, 150, 1)
    Verify Custom Branding In Landing Page
    Login As Patient From OIDC Login Page    ${f07uo5_extd_patient}[email]    password=${new_pw}
    Patient Is In The Right Page    Diary
    Verify Custom Branding In Diary
    [Teardown]    Close All App Instances

Extension E - Patient Can Reset Password/Unlock Delegate User Account (web / app)
    [Tags]    nms9-ver-478    native-web    patient-email-link
    [Setup]    Set Application On Environment
    Set Email And Delete Previous Messages
    ...    ${f07u05_email_keys}[0]
    ...    ${f07u05_email_keys}[1]
    Open Patient Page
    Trigger Delegate Account Lock
    ...    ${f07u05_ext_e_patient}[delegate]
    Delegate User Account Is Now Locked
    ...    ${f07u05_ext_e_patient}[delegate]
    Close All App Instances
    Setup App Environment
    Patient Navigates To More And Goes To Clinic Preferences
    Reset Delegate User Password    ${f07u05_ext_e_patient}[delegate]
    Pressing Cancel Or X Closes The Modal    ${f07u05_ext_e_patient}[delegate]
    Delegate User Opens Password Reset Link Via Email    ${f07u05_ext_e_patient}[delegate]
    Delegate Has A Newly Reset Password And Is Able To Login    ${f07u05_ext_e_patient}[delegate]
    [Teardown]    Close All App Instances


*** Keywords ***
Open Patient Page
    IF    'native' in '${ENVIRONMENT}'
        Launch Noona
        Try To Click Element    ${landing_page_login_button_app}
    ELSE
        ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
        IF    ${remote_url_exists}
            Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
        ELSE
            Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
        END
        Accept All Cookies If Visible
        Try To Click Element    ${landing_page_login_button}
    END

Create New Patient And Lock Account
    Set Test Variable
    ...    @{mailosaur_keys}
    ...    ${f07u05_email_keys}[0]
    ...    ${f07u05_email_keys}[1]
    Add An Activated Patient Under Default Clinic
    ...    f07nu05
    ...    mailosaur=${mailosaur_keys}[0]
    Get Patient UserID    ${patient_id}
    Lock Patient Via API    ${user_id}
    Delete All Messages In Server
    ...    ${mailosaur_keys}[0]
    ...    ${mailosaur_keys}[1]

Input Email For Password Reset And Submit
    [Arguments]    ${patient_email}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        IF    '${PLATFORM_NAME}'=='android'
            Wait Until Keyword Succeeds    6x    200ms    Hide Keyboard
        END
        Try To Click Native App Element    xpath=${problems_logging_in_app}
        Wait Until Element Is Visible    ${problems_logging_in_title}
        Wait Until Page Contains Element    ${problems_logging_in_instructions_line_1}    timeout=15s
        Wait Until Page Contains Element    ${problems_logging_in_instructions_line_2}    timeout=10s
        IF  '${PLATFORM_NAME}'=='android'
            Try To Input Native App Text     xpath=${problems_logging_in_email_field_android}     ${patient_email}
        ELSE
            Try To Input Native App Text     ${problems_logging_in_email_field}     ${patient_email}
        END
        IF    '${PLATFORM_NAME}'=='ios'
            Hide Mobile Keyboard
            Click Element      ${problems_logging_in_modal_send}
        ELSE
            Click Element    ${problems_logging_in_email_field_android}
            Press Keycode    61    #shows the send button
            Press Keycode    61    #shows the send button
            Try To Click Native App Element    ${problems_logging_in_modal_send}
        END
    ELSE
        Try To Click Element    ${problems_logging_in_oidc}
        Try To Input Text    ${problems_logging_in_email_textfield}    ${patient_email}
        Try To Click Element    ${problems_logging_in_modal_submit}
    END

Request Password Reset
    [Arguments]    ${patient_email}
    Input Email For Password Reset And Submit    ${patient_email}
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'
            @{contexts}    Get Contexts
            Switch To Context    WEBVIEW_chrome
            Check Password Reset Confirmation Modal    ${patient_email}
            Try To Click Element    ${password_reset_modal_close_button}
            Accept All Cookies If Visible
        ELSE
            Check Password Reset Confirmation Modal    ${patient_email}
            Try To Click Native App Element    ${password_reset_modal_close_button_app}
            Accept All Cookies If Visible    accept_cookies_in_nativeapp=yes
        END
    ELSE
        Check Password Reset Confirmation Modal    ${patient_email}
        Try To Click Element    ${password_reset_modal_close_button}
    END

Request Password Reset Rejected
    [Arguments]    ${patient_email}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Try To Click Native App Element    ${native_app_email_and_password_button}
        Input Email For Password Reset And Submit    ${patient_email}
        IF    '${PLATFORM_NAME}'=='android'
            @{contexts}    Get Contexts
            Switch To Context    WEBVIEW_chrome  
            Wait Until Page Contains    ${request_rejected}
            Try To Click Element    ${password_reset_modal_close_button}
        ELSE
            Wait Until Page Contains    ${request_rejected}
            Try To Click Native App Element    ${native_app_password_reset_modal_close_button}
        END
    ELSE
        Try To Click Element    ${landing_page_login_button}
        Input Email For Password Reset And Submit    ${patient_email}
        Wait Until Page Contains    ${request_rejected}
        Try To Click Element    ${password_reset_modal_close_button}
    END

Request Password Reset And Check Email
    [Arguments]    ${email}    ${clinic}=Noona
    Open Patient Page
    Request Password Reset    ${email}
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${email}
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    ...    clinic=${clinic}
    Set Test Variable    ${link}    ${message_data}[1]

Verify Custom Branding In Landing Page
    Wait Until Page Contains    Log in to ${custom_branding_enabled_clinic}[custom_clinic_name]
    Page Has Correct Logo    winter-city-publicdomain.svg
    Verify Css Property Color    ${landing_page_login_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${dont_have_account_link}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${privacy_statement_link}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${about_link_login_page}    rgba(81, 106, 150, 1)    color

Verify Custom Branding In Diary
    Verify Css Property Color    ${clinic_name_header_id}    rgb(81, 106, 150)    background-image
    Verify Css Property Color    ${diary_icon}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${update_symptoms_button_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${welcome_to_noona_ok_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${add_menu_button}    rgba(81, 106, 150, 1)    color

Trigger Delegate Account Lock
    [Arguments]    ${email}    ${password}=${incorrect_password}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        IF    '${PLATFORM_NAME}'=='android'
            ${keyboard_status}    Is Keyboard Shown
            IF    ${keyboard_status} == True
                Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
            END
        END
    END
    FOR    ${index}    IN RANGE    0    6
        IF    'native' not in '${ENVIRONMENT}'
            Try To Input Text    ${oidc_username_input}    ${email}
            Try To Input Text    ${oidc_password_input}    ${password}
            Sleep    2
            Try To Click Element    ${oidc_login_button}
        ELSE
            Input Login Credentials With Error Message    ${email}    ${password}
            Sleep    5s    # Sleep is needed as per NOONA-18026
            Clear Patient Login Details    ${email}=${f07u05_ext_e_patient}[delegate]
        END
    END

Delegate User Account Is Now Locked
    [Arguments]    ${email}
    IF    'native' not in '${ENVIRONMENT}'
        Patient Is Locked    ${email}
    ELSE
        Input Login Credentials With Error Message
        ...    ${email}
        ...    ${DEFAULT_PASSWORD}
    END

Patient Navigates To More And Goes To Clinic Preferences
    Login As Patient    ${f07u05_ext_e_patient}[email]
    Go To Clinic Preferences
    Delegate Users Section Is Displayed In My Profile
