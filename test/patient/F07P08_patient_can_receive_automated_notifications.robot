*** Settings ***
Documentation       F07P08 Patient can receive automated notifications
...                 Preconditions:
...                 - Patient has user account.
...                 - Patient receives AEQ, QoL, scheduled message or message or is notified to follow-up symptom and wellbeing information [file:./test/patient/F01P14.robot|F01P14 Patient is notified to follow-up symptom and wellbeing information].
...                 - TODO: SMS check

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources/clinic_settings/basic_settings.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p08    patient-web    patient-email-link


*** Variables ***
${email_text}
...                 New questionnaire
...                 Your care team at TA clinic Automated_tests has sent you a questionnaire in Noona to keep track of your progress.


*** Test Cases ***
Patient Can Receive Automated Notifications
    [Tags]    nms9-ver-235    native-app-todo
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Login As Nurse And Select Patient    ${f07p08_patient}[ssn]
    Add Questionnaire To Schedule    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    @{message_data}    Patient Received An Email About A New Message
    ...    ${f07p08_patient}[email]
    ...    New Quality of Life Questionnaire from ${automated_tests_clinic}[name]
    ...    Your care team at ${automated_tests_clinic}[name] has sent you a questionnaire in Noona to keep track of your progress.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Set Test Variable    ${link}    ${message_data}[2]
    Go To Link And Check That Page Is Opened
    [Teardown]    Close Browser

Extension A - Patient doesn’t receive notifications from a clinic - Unactivated
    [Tags]    nms9-ver-21-1    nms9-ver-21
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    # The patient belong to only 1 clinic and the status = Activated - already included in other tcs
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    unactivated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If All Email Notifications Are Received    sent

Extension A - Patient doesn’t receive notifications from a clinic - Declined
    [Tags]    nms9-ver-21-2    nms9-ver-21    long-tc    long-tc-1
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    declined    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Activation Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Password Reset Is Sent    not sent
    Check If Email Notification About Locked Account Is Sent    not sent
    # schedule message cannot be sent

Extension A - Patient doesn’t receive notifications from a clinic - Locked By Nurse
    [Tags]    nms9-ver-21-3    nms9-ver-21
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    locked    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Activation Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    # password reset - locked patient can receive temporary locked account email notification when doing password reset, this should be tested in another testcase
    Check If Email Notification About Locked Account Is Sent    not sent
    # schedule message cannot be sent

Extension A - Patient doesn’t receive notifications from a clinic - Proxy
    [Tags]    nms9-ver-21-4    nms9-ver-21    long-tc    long-tc-1
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    proxy    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If All Email Notifications Are Received    not sent

Extension A - Patient doesn’t receive notifications from a clinic - Candidate
    [Tags]    nms9-ver-21-5    nms9-ver-21
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    candidate    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Activation Is Sent    not sent    ${spmc_clinic_a}[name]
    # clinic cannot send questionnaires and messages to candidate
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Password Reset Is Sent    not sent
    Check If Email Notification About Locked Account Is Sent    not sent
    # schedule message cannot be sent

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 activated
    [Tags]    nms9-ver-21-6    nms9-ver-21    long-tc    long-tc-1
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Check If Email Notification About Activation Is Sent - Multi Clinic    sent    sent
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Password Reset Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    # locked account - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # scheduled message
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_b}[name]    ${spmc_clinic_b}[manager_email]    ${SPMC_CLINIC_B_CLINIC_ID}    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 disabled
    [Documentation]    Actions to send notifications to patient cannot be made when clinic is disabled.
    ...    Therefore, only notifications coming from an active clinic is verified
    [Tags]    nms9-ver-21-7    nms9-ver-21    long-tc    long-tc-1
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    ${patient_id}    Set Variable IF    'test' in '${ENVIRONMENT}'    ${f07p08_activated_disabled}[id_test]
    ...    ${f07p08_activated_disabled}[id_staging]
    Generate Clinic Token    ${spmc_clinic_a}[manager_email]    ${DEFAULT_PASSWORD}    ${SPMC_CLINIC_A_CLINIC_ID}
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}    ${f07p08_activated_disabled}[email]
    Set Test Variable    ${login_token}    ${login_token}
    # patient is already created, therefore activation email is not checked
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    wait_more=30s
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Password Reset Is Sent    sent
    Check If Email Notification About Locked Account Is Sent    sent
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 locked
    [Tags]    nms9-ver-21-8     nms9-ver-21   long-tc    long-tc-2
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    locked
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - locked patient can receive temporary locked account email notification when doing password reset, this should be tested in another testcase
    Check If Email Notification About Password Reset Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    # locked account
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message cannot be sent

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 locked, Clinic 2 locked
    [Tags]    nms9-ver-21-9     nms9-ver-21   long-tc    long-tc-2
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    locked    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    locked
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - locked patient can receive temporary locked account email notification when doing password reset, this should be tested in another testcase
    # locked account
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message cannot be sent

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 proxy
    [Tags]    nms9-ver-21-10    nms9-ver-21    long-tc    long-tc-2
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    proxy
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset
    Check If Email Notification About Password Reset Is Sent
    ...    sent
    ...    clinic=${spmc_clinic_a}[name]
    ...    multi=yes
    ...    close_browser=no
    Check If Email Notification About Password Reset Is Sent
    ...    not sent
    ...    clinic=${spmc_clinic_b}[name]
    ...    multi=yes
    ...    open_browser=no
    # locked account
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes
    Check If Email Notification About Scheduled Message Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 proxy, Clinic 2 proxy
    [Tags]    nms9-ver-21-11    nms9-ver-21    long-tc    long-tc-2
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    proxy    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    proxy
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset
    Check If Email Notification About Password Reset Is Sent    not sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Password Reset Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # locked account
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message
    Check If Email Notification About Scheduled Message Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}
    ...    multi=yes
    Check If Email Notification About Scheduled Message Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 candidate
    [Tags]    nms9-ver-21-12    nms9-ver-21    long-tc    long-tc-3
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    candidate
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${SPMC_CLINIC_B_EHR_TOKEN}
    # clinic cannot send questionnaires and messages to candidate
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset
    Check If Email Notification About Password Reset Is Sent
    ...    sent
    ...    clinic=${spmc_clinic_a}[name]
    ...    multi=yes
    ...    close_browser=no
    Check If Email Notification About Password Reset Is Sent
    ...    not sent
    ...    clinic=${spmc_clinic_b}[name]
    ...    multi=yes
    ...    open_browser=no
    # locked account
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message cannot be sent to candidate
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 candidate, Clinic 2 candidate
    [Tags]    nms9-ver-21-13    nms9-ver-21    long-tc    long-tc-3
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    candidate    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Create Patient With Different Statuses
    ...    candidate
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${SPMC_CLINIC_B_EHR_TOKEN}
    # clinic cannot send questionnaires and messages to candidate
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset
    Check If Email Notification About Password Reset Is Sent
    ...    not sent
    ...    clinic=${spmc_clinic_a}[name]
    ...    multi=yes
    ...    close_browser=no
    Check If Email Notification About Password Reset Is Sent
    ...    not sent
    ...    clinic=${spmc_clinic_b}[name]
    ...    multi=yes
    ...    open_browser=no
    # locked account
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # schedule message cannot be sent to candidate

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 unactivated, Clinic 2 unactivated
    [Tags]    nms9-ver-21-14    nms9-ver-21    long-tc    long-tc-3
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    unactivated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    unactivated
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Password Reset Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    # locked account - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # scheduled message
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_b}[name]    ${spmc_clinic_b}[manager_email]    ${SPMC_CLINIC_B_CLINIC_ID}    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 unactivated
    [Tags]    nms9-ver-21-15    nms9-ver-21    long-tc    long-tc-3
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    unactivated
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Password Reset Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    # locked account - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # scheduled message
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_b}[name]    ${spmc_clinic_b}[manager_email]    ${SPMC_CLINIC_B_CLINIC_ID}    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 activated, Clinic 2 declined
    [Tags]    nms9-ver-21-16    nms9-ver-21    long-tc    long-tc-4
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    declined
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset
    Check If Email Notification About Password Reset Is Sent
    ...    sent
    ...    clinic=${spmc_clinic_a}[name]
    ...    multi=yes
    ...    close_browser=no
    Check If Email Notification About Password Reset Is Sent
    ...    not sent
    ...    clinic=${spmc_clinic_b}[name]
    ...    multi=yes
    ...    open_browser=no
    # locked account
    Check If Email Notification About Locked Account Is Sent    sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # scheduled message to declined patient
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]    ${SPMC_CLINIC_A_CLINIC_ID}    multi=yes

Extension A - Patient doesn't receive notifications from multiple clinics - Clinic 1 declined, Clinic 2 declined
    [Tags]    nms9-ver-21-17    nms9-ver-21    long-tc    long-tc-4
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    declined    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    declined
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}
    # questionnaire
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Symptom Questionnaire Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # message
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Message From Clinic Is Sent    not sent    ${spmc_clinic_b}[name]    multi=yes
    # lab reports
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    ...    multi=yes
    Check If Email Notification About Lab Results Is Sent
    ...    not sent
    ...    ${spmc_clinic_b}[name]
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    multi=yes
    # password reset - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Password Reset Is Sent    not sent    clinic=${spmc_clinic_a}[name]    multi=yes
    Check If Email Notification About Password Reset Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # locked account - only 1 clinic can be tested due to limitation in email notification sent within 15mins
    Check If Email Notification About Locked Account Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    Check If Email Notification About Password Reset Is Sent    not sent    clinic=${spmc_clinic_b}[name]    multi=yes
    # scheduled message to declined patient

Extension A - Patient doesn't receive notifications from multiple clinics - PatientAccount is locked
    [Tags]    nms9-ver-21-18    nms9-ver-21    long-tc    long-tc-4
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Activation Is Sent    sent    ${spmc_clinic_a}[name]
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Check If Email Notification About Locked Account Is Sent    sent
    Check If Email Notification About Symptom Questionnaire Is Sent    sent    ${spmc_clinic_a}[name]    wait_more=30s
    Check If Email Notification About Message From Clinic Is Sent    sent    ${spmc_clinic_a}[name]
    Check If Email Notification About Lab Results Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Password Reset Is Sent    sent
    Check If Email Notification About Scheduled Message Is Sent
    ...    sent
    ...    ${spmc_clinic_a}[name]
    ...    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}

Extension B - Scheduled content - Outside Quiet Hours
    [Tags]    nms9-ver-385-1    nms9-ver-385    sms    noona_admin
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Login As Nurse    clinic=${ta_clinic_quiet_hours_set}[name]    user_type=${USER_TYPE}[noona_admin]
    Update Quiet Time    outside
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses
    ...    activated
    ...    ${ta_clinic_quiet_hours_set}[name]
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    ...    ${QUIET_HOURS_SET_SUB_ID_CARE_TEAM_1}
    ...    ${QUIET_HOURS_SET_EHR_TOKEN}
    Verify Scheduled Content Notifications Against Quiet Time    sent
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    ...    ${QUIET_HOURS_SET_EHR_TOKEN}

Extension B - Scheduled content - Inside Quiet Hours
    [Tags]    nms9-ver-385-2    nms9-ver-385    sms    noona_admin
    [Setup]    Set Email And Delete Previous Messages    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    Login As Nurse    clinic=${ta_clinic_quiet_hours_set}[name]    user_type=${USER_TYPE}[noona_admin]
    Update Quiet Time    inside
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses
    ...    activated
    ...    ${ta_clinic_quiet_hours_set}[name]
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    ...    ${QUIET_HOURS_SET_SUB_ID_CARE_TEAM_1}
    ...    ${QUIET_HOURS_SET_EHR_TOKEN}
    Verify Scheduled Content Notifications Against Quiet Time    not sent
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    ...    ${QUIET_HOURS_SET_EHR_TOKEN}

Extension B - Scheduled content - Inside Quiet Hours - Push Notifications
    [Tags]    nms9-ver-465    manual
    # TODO: To test push notifications, follow the steps below:
    # 1. Log in as patient on a mobile phone (<EMAIL>)
    # 2. A different patient and clinic can be used for this test. Update the patient id, nurse email and clinic id if needed
    # 3. Set quiet time to not receive notification
    # 4. Run this test case
    # 5. Verify if the phone receive push notification
    # Note: Appointments are tested under a different test case
    Set Test Variable    ${patient_id}    9f72c5b5-4516-4a4d-9c05-690fcf355eb0
    Generate Clinic Token
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Send Scheduled Message To Patient Via Api    Sample title    Sample content
    Set Test Variable    ${integration_user_token}    ${QUIET_HOURS_SET_EHR_TOKEN}
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}


*** Keywords ***
Login As Nurse And Select Patient
    [Arguments]    ${patient}
    Login As Nurse
    Search Patient By Identity Code    ${patient}
    Navigate To Questionnaires Tab

Go To Link And Check That Page Is Opened
    Go To    ${link}
    Wait Until Page Contains Element    ${questionnaire_next_button}

Set And Delete Emails
    Set Test Variable    @{mailosaur_keys}    ${server_id}    ${api_key}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

Check If All Email Notifications Are Received
    [Arguments]    ${sent_or_not_sent}
    Check If Email Notification About Activation Is Sent    ${sent_or_not_sent}    ${spmc_clinic_a}[name]
    Check If Email Notification About Symptom Questionnaire Is Sent
    ...    ${sent_or_not_sent}
    ...    ${spmc_clinic_a}[name]
    ...    wait_more=30s
    Check If Email Notification About Message From Clinic Is Sent    ${sent_or_not_sent}    ${spmc_clinic_a}[name]
    Check If Email Notification About Lab Results Is Sent
    ...    ${sent_or_not_sent}
    ...    ${spmc_clinic_a}[name]
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Check If Email Notification About Password Reset Is Sent    ${sent_or_not_sent}
    Check If Email Notification About Locked Account Is Sent    ${sent_or_not_sent}
    Check If Email Notification About Scheduled Message Is Sent
    ...    ${sent_or_not_sent}
    ...    ${spmc_clinic_a}[name]
    ...    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}

Email Notification Is Not Received
    ${status}    ${failure_message}    Run Keyword And Ignore Error
    ...    Get Email Message
    ...    ${f07p08_email_keys}[0]
    ...    ${f07p08_email_keys}[1]
    ...    ${patient_email}
    ...    54
    Should Be Equal    ${status}    FAIL
    # to make sure that kw failed because no email is received and not because of something else
    Should Contain
    ...    ${failure_message}
    ...    No matching messages found in time.

Check If Email Notification About Activation Is Sent - Multi Clinic
    [Arguments]    ${sent_or_not_sent1}    ${sent_or_not_sent2}
    Generate Random Patient Data    mailosaur=${f07p08_email_keys}[0]
    Create Patient With Different Statuses    activated    ${spmc_clinic_a}[name]    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}    ${SPMC_CLINIC_A_SUB_ID_F07P08}    ${SPMC_CLINIC_A_EHR_TOKEN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Set Test Variable    ${login_token1}    ${login_token}
    Create Patient With Different Statuses
    ...    unactivated
    ...    ${spmc_clinic_b}[name]
    ...    ${spmc_clinic_b}[manager_email]
    ...    ${SPMC_CLINIC_B_CLINIC_ID}
    ...    ${SPMC_CLINIC_B_SUB_ID_F07P08}
    ...    ${SPMC_CLINIC_B_EHR_TOKEN}
    ...    second_clinic=yes
    IF    '${sent_or_not_sent2}'=='sent'
        Patient Received An Email About A New Message
        ...    ${patient_email}
        ...    ${spmc_clinic_b}[name] invites you to use Noona
        ...    By using Noona to communicate symptoms and potentially serious conditions, your care team at ${spmc_clinic_b}[name] can follow your progress and offer more personalized care.
    ELSE
        Email Notification Is Not Received
    END
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Set Test Variable    ${login_token2}    ${login_token}

Check If Email Notification About Activation Is Sent
    [Arguments]    ${sent}    ${clinic}
    IF    '${sent}'=='sent'
        Patient Received Invitation To Use Noona    ${patient_email}    ${clinic}
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About Symptom Questionnaire Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}    ${multi}=no    ${wait_more}=no
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Sleep    60s    # additional wait for questionnaire to be sent
    IF    '${wait_more}'!='no'    Sleep    ${wait_more}
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received Invitation To Answer AEQ    ${patient_email}    ${clinic}
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About Message From Clinic Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}    ${multi}=no
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    Send Contact Patient Request    11
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received An Email About A New Message
        ...    ${patient_email}
        ...    New message from your care team at ${clinic}
        ...    Please log in to read the message.
        ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About Lab Results Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}    ${ehr_token}    ${multi}=no
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    Set Test Variable    ${integration_user_token}    ${ehr_token}
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received An Email About New Lab Results    ${patient_email}    ${clinic}
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About Password Reset Is Sent
    [Arguments]
    ...    ${sent_or_not_sent}
    ...    ${clinic}=default
    ...    ${multi}=no
    ...    ${open_browser}=yes
    ...    ${close_browser}=yes
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    IF    '${open_browser}'=='yes'    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    Try To Click Element    ${problems_logging_in_oidc}
    Try To Input Text    ${problems_logging_in_email_textfield}    ${patient_email}
    Try To Click Element    ${problems_logging_in_modal_submit}
    Try To Click Element    ${password_reset_modal_close_button}
    IF    '${close_browser}'=='yes'    Close Browser
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received An Email About Reset Password
        ...    ${patient_email}
        ...    ${PATIENT_URL_IN_MESSAGE}/s/
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About Locked Account Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}=default    ${multi}=no
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Sleep    3s
    Type Wrong Password For 6 Times    ${patient_email}
    Patient Is Locked    ${patient_email}
    IF    '${sent_or_not_sent}'=='sent'
        Patient Is Notified About Account Being Locked    ${patient_email}
    ELSE
        Email Notification Is Not Received
    END
    Close Browser

Check If Email Notification About Scheduled Message Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}    ${nurse_email}    ${clinic_id}    ${multi}=no
    Delete All Messages In Server    ${f07p08_email_keys}[0]    ${f07p08_email_keys}[1]
    IF    '${multi}'=='yes'    Set Variables For Multi Clinics    ${clinic}
    Generate Clinic Token    ${nurse_email}    ${DEFAULT_PASSWORD}    ${clinic_id}
    Send Scheduled Message To Patient Via Api    Sample title    Sample content
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received An Email About A New Message
        ...    ${patient_email}
        ...    New message from your care team at ${clinic}
        ...    Please login to read it.
    ELSE
        Email Notification Is Not Received
    END

Check If Email Notification About SMS Invitation And Reminder Is Sent
    [Arguments]    ${sent_or_not_sent}    ${clinic}    ${ehr_token}    ${tunit}
    Delete All Messages In Server    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}
    Format DOB
    Invite Patient Via SMS Invitation    ${ehr_token}    ${tunit}    ${dob}    phone_number=${mailosaur_number}
    IF    '${sent_or_not_sent}'=='sent'
        Patient Received SMS Invitation To Use Noona    ${clinic}
        Delete All Messages In Server    ${mailosaur_sms_server_id}    ${mailosaur_sms_api_key}
        # set to receive on the same day, few mins after the invitation
        Patient Received SMS Invitation Reminder
        ...    ${clinic}
    ELSE
        Email Notification Is Not Received
    END

Set Variables For Multi Clinics
    [Arguments]    ${clinic}
    IF    '${clinic}'=='${spmc_clinic_a}[name]'
        Set Test Variable    ${patient_id}    ${patient_id1}
        Set Test Variable    ${patient_email}    ${patient_email1}
        Set Test Variable    ${login_token}    ${login_token1}
    ELSE
        Set Test Variable    ${patient_id}    ${patient_id2}
        Set Test Variable    ${patient_email}    ${patient_email2}
        Set Test Variable    ${login_token}    ${login_token2}
    END

Verify Scheduled Content Notifications Against Quiet Time
    [Arguments]    ${sent_not_sent}
    Check If Email Notification About Symptom Questionnaire Is Sent
    ...    ${sent_not_sent}
    ...    ${ta_clinic_quiet_hours_set}[name]
    Login As Nurse    email=${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    IF    '${sent_not_sent}'=='not sent'
        Questionnaire Status Is Correct    ${BASELINE_QUESTIONNAIRE}    SCHEDULED
    ELSE
        Answer Symptom Questionnaire As Nurse With Mild Other Symptom
    END
    Close Browser
    Check If Email Notification About Scheduled Message Is Sent
    ...    ${sent_not_sent}
    ...    ${ta_clinic_quiet_hours_set}[name]
    ...    ${ta_clinic_quiet_hours_set}[f07p08_manager_email]
    ...    ${QUIET_HOURS_SET_CLINIC_ID}
    Check If Email Notification About Lab Results Is Sent
    ...    ${sent_not_sent}
    ...    ${ta_clinic_quiet_hours_set}[name]
    ...    ${QUIET_HOURS_SET_EHR_TOKEN}
    # appointment and smart symptom notifications should be added to time contraint tests
