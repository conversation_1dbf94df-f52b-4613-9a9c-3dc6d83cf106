*** Settings ***
Documentation       F01P01 Patient can report symptom
...                 Preconditions:
...                 - User logged in as patient.
...                 - Contact clinic feature must be enabled in clinic settings.
...                 F01P04 Patient can send symptom report to clinic to receive advice

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Suite Teardown      Close All Excel Documents
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p01    patient-web    usecase-f01p04


*** Variables ***
${path_to_modules}      ${EXECDIR}${/}data${/}symptoms_per_module${/}symptoms_per_module.xlsx
${module1}              Chemotherapy 18 symptoms
${module2}              Immuno-oncological pharmacotherapy


*** Test Cases ***
Main Success Scenario - Patient Reports Severe Symptom
    [Documentation]    Also includes F01P04 Patient can send symptom report to clinic to receive advice
    [Tags]    nms9-ver-97-1    nms9-ver-97    native-web    nms9-ver-119    nms9-ver-132
    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-f01p04    module=${CHEMO_11}
    # nms9-ver-97
    Login As Patient    ${patient_email}
    Ask About Symptoms    ${OTHER_SYMPTOM}
    Click Next Button
    If Mandatory Questions Are Unanswered, Error Message Is Displayed
    Report Other Symptom    Severe
    # nms9-ver-132 covering next of kin check in a symptom report's summary before sending
    Information Entered By Caregiver Checkbox Is Visible
    Tick Info Entered By Caregiver Checkbox
    Questionnaire Summary Is Displayed
    Send Symptom To Clinic
    Emergency Priority Symptom Is Displayed
    # nms9-ver-119
    Navigate To Clinic
    Verify Inbox For Severe Symptom Message
    Go To Diary
    Diary Latest Entry Section Contain Symptom    ${OTHER_SYMPTOM}
    Select Latest Symptom Diary Entry    ${OTHER_SYMPTOM}
    Diary Symptom Is Sent To Clinic    ${OTHER_SYMPTOM}
     # nms9-ver-132 covering next of kin check in Diary's symptom card
    Diary Symptom Information Is Entered By A Caregiver/ Next Of Kin
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario - Patient Reports Nodule And Bumps
    [Tags]    nms9-ver-97-2    nms9-ver-97    defect    native-web
    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-2    module=${BREAST_CANCER_RECOVERY}
    Login As Patient    ${patient_email}
    Ask About Symptoms    ${NODULES_AND_BUMPS}
    Page Should Not Contain App Error
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    On which side of your body is the nodule?    Right
    Select Answer To Question    Describe the location of the nodule    Breast
    Select Answer To Question    How many nodules are there?    One
    Select Answer To Question    How does the nodule feel when touched?    Soft
    Click Next Button
    Text Should Not Be In The Page    Please answer all mandatory questions
    Questionnaire Summary Is Displayed
    Send Symptom To Clinic
    Wait Until Page Contains    Symptom entry sent to the clinic
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario - Patient Reports Pain With Checkbox
    [Tags]    nms9-ver-97-3    nms9-ver-97    native-web    defect
    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-2    module=${BREAST_CANCER_RADIATION_THERAPY}
    Login As Patient    ${patient_email}
    Ask About Symptoms    ${PAIN}
    Page Should Not Contain App Error
    Select Answer To Question    Where is the pain located?    Right chest
    Select Answer To Question    Is it in the area in which you received radiation?    No
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you describe the pain?    Throbbing
    Select Answer To Question    Has the pain increased significantly during this time?    No
    Rate With Vertical Slider    7
    Select Answer To Question    Have you tried pain medications?    No
    Click Next Button
    Text Should Not Be In The Page    Please answer all mandatory questions
    Questionnaire Summary Is Displayed
    Send Symptom To Clinic
    Wait Until Page Contains    Symptom entry sent to the clinic
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario - Patient Reports Changes In General State Of Health
    [Tags]    nms9-ver-97-4    nms9-ver-97    defect    native-web
    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-2    module=${BONE_RADIOTHERAPY}
    Login As Patient    ${patient_email}
    Ask About Symptoms    ${CHANGES_IN_GENERAL_STATE_OF_HEALTH}
    Page Should Not Contain App Error
    Select Answer To Question
    ...    How would you rate your general state of health?
    ...    I can take care of myself only partially, and I have to lie down or sit for more than 50% of the time I am awake.
    Click Next Button
    Page Should Not Contain App Error
    Text Should Not Be In The Page    Please answer all mandatory questions
    Questionnaire Summary Is Displayed
    Page Should Not Contain App Error
    Send Symptom To Clinic
    Page Should Not Contain App Error
    Wait Until Page Contains    Symptom entry sent to the clinic
    Page Should Not Contain App Error
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Report Symptom In Diary
    [Tags]    nms9-ver-98    nms9-ver-132    pendo    native-web
    Set Application On Environment
    Open Excel Document    ${path_to_modules}    Symptoms
    Add An Activated Patient Under Default Clinic    f01p01-exta    module=${CHEMO_18_SYMPTOMS}
    # Patient not reported a symptom in previous 30 days
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Symptoms For Current Module Is Listed Correctly    module=${module1}
    Add A Symptom    ${OTHER_SYMPTOM}
    Report Other Symptom    Moderate
    Questionnaire Summary Is Displayed
    Send Symptom To Clinic
    Wait Until Page Contains    Symptom entry saved in your diary
    Click View Your Diary
    Close All App Instances
    # Patient reported a symptom in previous 30 days
    Set Application On Environment
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Previously Reported Symptoms Are Displayed
    Add Difficulty Eating Symptom To Diary    reported_by=caregiver
    Diary Latest Entry Section Contain Symptom    ${DIFFICULTY_EATING}
    Select Latest Symptom Diary Entry    ${DIFFICULTY_EATING}
     # nms9-ver-132 covering next of kin check in Diary's symptom card
    Diary Symptom Information Is Entered By A Caregiver/ Next Of Kin
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Gender Specific Symptoms Forms
    [Documentation]    Also covers test case Extension C - Gender specific symptom icons (web / app)
    [Tags]    nms9-ver-99    nms9-ver-100    pendo    native-web
    Set Application On Environment
    # Male - male genitalia
    Login As Patient    ${f01p01_patient_male}[email]
    Navigate to Clinic
    Select Ask About Symptom Option
    Symptom And Icon Are In The Symptom List    ${SEXUAL_INTEREST_AND_ABILITY}
    ...    ds-icon--symptom_libido_and_erection
    Text Should Not Be In The Page    ${VAGINAL_SYMPTOMS}
    Close All App Instances
    # Female - female genitalia
    Set Application On Environment
    Login As Patient    ${f01p01_patient_female}[email]
    Navigate to Clinic
    Select Ask About Symptom Option
    Symptom And Icon Are In The Symptom List    ${VAGINAL_SYMPTOMS}    ds-icon--symptom_vagina_symptoms
    Text Should Not Be In The Page    ${SEXUAL_INTEREST_AND_ABILITY}

Multiple Active Module Symptom Sorting
    [Tags]    nms9-ver-101    native-web
    Open Excel Document    ${path_to_modules}    Symptoms
    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-multimodule    module=${CHEMO_18_SYMPTOMS}
    Add A Second Treatment Module And Set As Main
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Ask About Symptom Option
    Symptoms In Multiple Modules Are Listed Correctly
    Close The First Module
    Close All App Instances
    Set Application On Environment
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Ask About Symptom Option
    Symptoms For Current Module Is Listed Correctly
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E — Indicating missed Required fields to patients (web/app)
    [Tags]    nms9-ver-407    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01-exte    module=${CHEMO_11}
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Add A Symptom    ${OTHER_SYMPTOM}
    Click Next Button
    Question Is Mandatory Error Is Displayed    4
    Close Modal
    Ask About Symptoms    ${NAUSEA_OR_VOMITING}
    Click Next Button
    Question Is Mandatory Error Is Displayed    3
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
If Mandatory Questions Are Unanswered, Error Message Is Displayed
    Click Next Button
    Question Is Mandatory Error Is Displayed    4

Report Other Symptom
    [Arguments]    ${severity}
    Try To Input Text    ${other_symptom_description_field}    Test comments severe
    Select Symptomatic Day    Symptom is chronic (persistent, long-standing, long-term)
    Select Symptomatic Day    Today
    Select Answer To Question    How would you rate the severity of your symptom?    ${severity}
    Select Answer To Question
    ...    Have you used any medication to alleviate your symptoms?
    ...    Daily
    Select Answer To Question
    ...    Have you used any medication to alleviate your symptoms?
    ...    No
    Sleep    1
    Click Next Button

Add A Second Treatment Module And Set As Main
    IF    'native' in '${ENVIRONMENT}'
        Generate Clinic Token
        ...    ${automated_tests_clinic}[default_user]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        Add Treatment Module Via API    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}    addImmunoOncoPharmaModule
        Get Patient Main Treatment Module Id    ${patient_id}    module_index=1
    ELSE
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Add Treatment Module    with_template=False
        ...    module_or_template=Immuno-oncological pharmacotherapy    start_now=True
        Set Module As Main    ${module2}
    END

Symptoms In Multiple Modules Are Listed Correctly
    Get Symptoms List As Patient
    Get Expected Symptoms Combination From Excel    ${module2}    ${module1}
    Lists Should Be Equal    ${expected_symptoms}    ${actual_symptoms}

Symptoms For Current Module Is Listed Correctly
    [Arguments]    ${module}=${module2}
    Get Symptoms List As Patient
    @{expected_symptoms}    Get Expected Symptoms Per Module    ${module}
    Lists Should Be Equal    ${expected_symptoms}    ${actual_symptoms}

Previously Reported Symptoms Are Displayed
    Get Previously Report Symptoms
    @{expected_symptoms}    Create List    Other symptom    Show more
    Lists Should Be Equal    ${current_symptoms}    ${expected_symptoms}

Close The First Module
    IF    'native' in '${ENVIRONMENT}'
        Generate Clinic Token
        ...    ${automated_tests_clinic}[default_user]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        Close Treatment Module Via API    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}    closeChemo18Module
    ELSE
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Questionnaires Tab
        Set Module As Closed    ${module1}
    END