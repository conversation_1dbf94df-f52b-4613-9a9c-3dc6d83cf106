*** Settings ***
Documentation       F01P01S18 Patient can report symptom and get automated emergency symptom alarm
...
...                 Preconditions:
...                 - User logged in as patient
...                 - Contact clinic feature must be enabled in clinic settings (Except for extension D)
...                 - Note: All alert rules related to patient's active modules apply.
...                 Also alert rules related to recently (within 1 hour) closed module apply.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}timeline.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_group.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p01s18    patient-web


*** Variables ***
${multiple_rule_instructions}               Your symptom description matches multiple automated instructions.
...                                         Please follow the instruction which best suits for your situation.
${first_rule_instruction}
...                                         The symptom you just described indicates that your treatment needs may require urgent assessment.
...                                         Please contact the breast surgery unit immediately or, if the unit is closed, call your local emergency clinic.
${second_rule_instruction_1}
...                                         The symptom you just described indicates that you might require immediate attention from a medical professional.
${second_rule_instruction_2}
...                                         Please seek help from an emergency health service provider in your area immediately.
${symptom_entry_sent_to_clinic_message}
...                                         Your symptom entry will be reviewed by clinic personnel.
...                                         You will get a response in Noona within 2 working days. Symptom description is also saved to your diary.
${clinic_inbox}                             //*[@id="clinic-inbox-header" and contains(text(), "Message")]
${rule_name}                                OTHERSYMPTOM01
${urgent_dialog_text}
...                                         This is a custom urgent rule notification check for both patient and clinic portals.
...                                         Symptom description is also saved to your diary.
${urgent_rule_name}                         OTHERSYMPTOM04
${other_symptom_description_field}          xpath=(//*[@id="description"])[last()]
${more_severe_day}                          //h4[text()="Please select one date when the symptom was most severe from the days you have experienced the symptom."]/../..


*** Test Cases ***
Main - Patient can report a severe symptom and get automated emergency symptom alarm - Most Severe Date Is Current Date
    [Tags]    nms9-ver-111-1    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup
    Patient Asks About Symptom And Sends It To Clinic    most_severe=current_date    including_today=yes
    The emergency rule(s) is/are triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are triggered on nurse's side    ask about symptom
        Close Case
    ELSE
        Get First Patient Case via API    ${patient_id}
        Close first case Via API    ${first_case_id}
    END
    [Teardown]    F01P01S18 - Test Teardown

Main - Patient can report a severe and get automated emergency symptom alarm - Current Date Is Not Included
    [Tags]    nms9-ver-111-2    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup
    Patient Asks About Symptom And Sends It To Clinic    most_severe=not_today    including_today=no
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/diary-timeline
    END
    The emergency rule(s) is/are not triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are not triggered on nurse's side
    END
    [Teardown]    F01P01S18 - Test Teardown

Main - Patient can report a severe and get automated emergency symptom alarm - Current Date Is Included - Most Severe Is Not Today - Symptom As Severe
    [Tags]    nms9-ver-111-3    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup
    Patient Asks About Symptom And Sends It To Clinic    most_severe=not_today    including_today=yes
    Symptom As Severe Question Is Displayed    is_symptom_still_severe=yes
    Sleep    5s
    The emergency rule(s) is/are triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are triggered on nurse's side    ask about symptom
        Close Case
    ELSE
        Get First Patient Case via API    ${patient_id}
        Close first case Via API    ${first_case_id}
    END
    [Teardown]    F01P01S18 - Test Teardown

Main - Patient can report a severe and get automated emergency symptom alarm - Current Date Is Included - Most Severe Is Not Today - Symptom Not As Severe
    [Tags]    nms9-ver-111-4    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup
    Patient Asks About Symptom And Sends It To Clinic    most_severe=not_today    including_today=yes
    Symptom As Severe Question Is Displayed    is_symptom_still_severe=no
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/diary-timeline
    END
    The emergency rule(s) is/are not triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are not triggered on nurse's side
    END
    [Teardown]    F01P01S18 - Test Teardown

Main - Patient can report a symptom that triggers an urgent rule - Most Severe Date Is Current Date
    [Documentation]    When patient treatment module is Urologic surgery, Urologic recovery, or Sarcoma surgery;
    ...    reporting 'Other symptom' as severe triggers an Urgent rule
    [Tags]    nms9-ver-111-5    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup    urgent
    Patient Asks About Symptom And Sends It To Clinic    most_severe=current_date    including_today=yes
    Patient sees clinic specific urgent dialog text
    IF    'native' not in '${ENVIRONMENT}'
        Urgent rule is triggered on nurse's side    ask about symptom
    END
    [Teardown]    F01P01S18 - Test Teardown


Main - Patient can report a symptom that triggers an urgent rule - Current Date Is Not Included
    [Tags]    nms9-ver-111-6    nms9-ver-111    native-web    pendo
    [Setup]    F01P01S18 - Test Setup    urgent
    Patient Asks About Symptom And Sends It To Clinic    most_severe=not_today    including_today=no
    Patient sees clinic specific urgent dialog text
    IF    'native' not in '${ENVIRONMENT}'
        Urgent rule is triggered on nurse's side    ask about symptom
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension A - Symptom triggers multiple same emergency level rules
    [Tags]    nms9-ver-112    native-web    pendo
    [Setup]    Setup App Environment
    Add Multiple Same Level Rules    ${f01p01s18_patient}[email]
    Select Latest Clinic Message
    Wait Until Page Contains    ${current_date}
    Text Should Be In The Page    ${multiple_rule_instructions}
    Text Should Be In The Page    ${first_rule_instruction}
    Text Should Be In The Page    ${second_rule_instruction_1}
    Text Should Be In The Page    ${second_rule_instruction_2}

Extension B - Symptom saved to diary triggers an alarm
    [Tags]    nms9-ver-113-1  nms9-ver-113    native-web    pendo
    [Setup]    Setup App Environment
    Login As Patient    email=${f01p01s18_extb}[email]    remember_login=none
    Click Add Menu Button
    Add Symptom Entry
    Report Severe Symptom Via Diary
    # TODO For Additional Test Scenarios:
    # If the symptom matched with an emergency or semi-emergency rule and:
    # If the most severe date is current date, the emergency rule(s) is/are triggered on nurse's and patient's side
    # If the current date is not included in duration, "saved to diary"
    # ...-dialog is displayed to patient, and the emergency rule(s) is/are not triggered on nurse's nor patient's side.
    # If the most severe date is not current date AND the current date is included in duration, the patient is asked
    # ...if the symptom is still as severe or more severe.
    # If the patient clicks "yes", the emergency rule(s) is/are triggered on nurse's and patient's side.
    # If the patient clicks "no", Noona displays the "saved to diary" dialog, and the emergency rule(s) is/are not triggeredÂ on nurse's nor patient's side.
    Send Symptom To Clinic
    Emergency Priority Symptom Is Displayed
    Go To Clinic
    Patient Is Directed To Inbox
    Verify Inbox For Severe Symptom Message
    # TODO For Additional Test Scenarios For Clinic Side:
    # If an emergency, semi-emergency or urgent rule was triggered on nurse's side:
    # Patient card appears in new messages -tab with high priority (red colour) in nurse's work queue
    # Nurse sees the message "Patient added a symptom to diary, which requires care need assessment." as a part of the message chain
    # Nurse sees the name and the description of the rule(s) as a part of the message chain
    # Nurse can validate automated rule

Extension B - Symptom saved to diary triggers an alarm - urgent symptom
    [Documentation]    When patient treatment module is Urologic surgery, Urologic recovery, or Sarcoma surgery;
    ...    reporting 'Other symptom' as severe triggers an Urgent rule
    ...    This extension checks if Urgent rule is also triggered when a patient reports the symptom
    ...    originating from 'navigation-add-menu-new-entry'
    [Tags]     nms9-ver-113-2     nms9-ver-113    native-web     pendo
    [Setup]    F01P01S18 - Test Setup    urgent
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Report Severe Symptom Via Diary
    Send Symptom To Clinic
    Patient sees clinic specific urgent dialog text
    IF    'native' not in '${ENVIRONMENT}'
        Urgent rule is triggered on nurse's side      navigation-add-menu-new-entry
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension C - Symptom in AEQ triggers an alarm - Most Severe Date Is Current Date
    [Tags]    nms9-ver-114-1    nms9-ver-114    native-web
    [Setup]    Extension C - F01P01S18 - Test Setup
    The patient answers an AEQ and sends it to the clinic
    ...    most_severe=current_date
    ...    including_today=yes
    The emergency rule(s) is/are triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are triggered on nurse's side
        ...    symptom questionnaire
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension C - Symptom in AEQ triggers an alarm - Current Date Is Not Included
    [Tags]    nms9-ver-114-2    nms9-ver-114    native-web
    [Setup]    Extension C - F01P01S18 - Test Setup
    The patient answers an AEQ and sends it to the clinic
    ...    most_severe=not_today
    ...    including_today=no
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/diary-timeline
    END
    The emergency rule(s) is/are not triggered on patient's side
    IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are not triggered on nurse's side
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension C - Symptom in AEQ triggers an alarm - Current Date Is Included - Most Severe Is Today
    [Tags]    nms9-ver-114-3    nms9-ver-114    native-web
    [Setup]    Extension C - F01P01S18 - Test Setup
    The patient answers an AEQ and sends it to the clinic
    ...    most_severe=current_date
    ...    including_today=yes
    The emergency rule(s) is/are triggered on patient's side
     IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are triggered on nurse's side
        ...    symptom questionnaire
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension C - Symptom in AEQ triggers an alarm - Current Date Is Included - Most Severe Is Not Today
    [Tags]    nms9-ver-114-4    nms9-ver-114    native-web
    [Setup]    Extension C - F01P01S18 - Test Setup
    The patient answers an AEQ and sends it to the clinic
    ...    most_severe=not_today
    ...    including_today=yes
    Symptom As Severe Question Is Displayed
    ...    is_symptom_still_severe=no
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/diary-timeline
    END
    The emergency rule(s) is/are not triggered on patient's side
     IF    'native' not in '${ENVIRONMENT}'
        The emergency rule(s) is/are not triggered on nurse's side
    END
    [Teardown]    F01P01S18 - Test Teardown

Extension D - Messaging feature is disabled in clinic and a symptom entry in the diary or in an AEQ triggers an alarm
    [Tags]    nms9-ver-115    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f01p01s18_extd}[email]    remember_login=none
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${approve_elements_checkbox}
    ...    timeout=8s
    IF    ${status}==${TRUE}    Give Consents And E-signature
    Click Add Menu Button
    Add Symptom Entry
    Report Severe Symptom Via Diary
    Send Symptom To Clinic
    Emergency Priority Symptom Is Displayed
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}
    Go To Diary
    Sleep    1
    Select Latest Symptom Diary Entry    Other symptom
    Wait Until Page Contains    ${current_date}
    Text Should Be In The Page    Clinic notified about a severe symptom
    Text Should Not Be In The Page    Patient added a symptom to diary, which requires care need assessment.
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${automated_tests_4_clinic}[user_email]
        Search Patient By Identity Code    ${f01p01s18_extd}[ssn]
        Navigate To Timeline Tab
        Select First Symptom Details From Summary Section
        Wait Until Page Contains    Clinic notified about a severe symptom
        Page Should Not Contain    Patient added a symptom to diary, which requires care need assessment.
    END


*** Keywords ***
F01P01S18 - Test Teardown
    IF    'native' in '${ENVIRONMENT}'
        Close All App Instances
        #TODO: Implement close cases on API level for cases created during native test runs.
    ELSE
        Close Open Cases Per Care Team    ${automated_tests_clinic}[f01p01s18_care_team]
        Remove Patient As Test Teardown
        ...    ${patient_email}
        ...    ${automated_tests_clinic}[default_manager]
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    END

F01P01S18 - Test Setup
    [Arguments]    ${patient_case_priority}=non_urgent
    Setup App Environment
    IF    '${patient_case_priority}' == 'urgent'
        Add An Activated Patient Under Default Clinic    f01p01s18-urgent    module=${UROLOGIC_SURGERY}
        ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P01S18}
    ELSE
        Add An Activated Patient Under Default Clinic    f01p01s18-patient    module=${CHEMO_18_SYMPTOMS}
        ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P01S18}
    END

Extension C - F01P01S18 - Test Setup
    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f01p01s18
    ...    module=${CHEMO_18_SYMPTOMS}
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P01S18}
    Send Symptom Questionnaire Via Api To Patient       ${baseline_quest}

Report Severe Symptom Via Diary
    Add A Symptom    Other symptom
    Input Text    ${other_symptom_description_field}    Other symptom description
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Sleep    1
    Click Next Button

The patient answers an AEQ and sends it to the clinic
    [Arguments]    ${most_severe}    ${including_today}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For Symptoms    Other symptom
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=new_entry
    Select Answer To Question    When did you have this symptom?    Mark symptomatic days
    Mark Symptomatic Days For Symptom Report    including_today=${including_today}
    IF    '${most_severe}'=='current_date'
        Try To Click Element    xpath=(${todays_date})[2]
    ELSE IF    '${most_severe}'=='not_today'
        Try To Click Element    xpath=(${2days_ago})[2]
    END
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    questionnaires.Check And Write To Text Area
    Click Next Button
    Send Symptom Questionnaire To Clinic

Symptom As Severe Question Is Displayed
    [Arguments]    ${is_symptom_still_severe}
    Wait Until Page Contains    You described a severe symptom. Is the symptom as severe or more severe right now?
    Text Should Be In The Page    Other symptom
    IF    '${is_symptom_still_severe}'=='yes'
        Try To Click Element    ${same_severity_button}
    ELSE
        Try To Click Element    ${severity_decreased_button}
    END

The emergency rule(s) is/are triggered on patient's side
    Patient sees emergency dialog that contains a link to the patient's inbox.
    Emergency message is sent to patient's inbox.
    Close Browser

The emergency rule(s) is/are triggered on nurse's side
    [Arguments]    ${symptom_origin}
    Login As Nurse
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01p01s18_care_team]
    IF    '${symptom_origin}'=='symptom questionnaire'
        Select Case Type Filter In Work Queue    Symptom from questionnaire
    ELSE IF    '${symptom_origin}'=='ask about symptom'
        Select Case Type Filter In Work Queue    Message about a symptom
    END
    Patient card appears in new messages -tab with high priority (red colour) in nurse's work queue
    Nurse sees the correct message as a part of the message chain    ${symptom_origin}
    Nurse sees the name and the description of the rule(s) as a part of the message chain

Patient card appears in new messages -tab with high priority (red colour) in nurse's work queue
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Patient Card's Color Is Correct    critical

Nurse sees the correct message as a part of the message chain
    [Arguments]    ${symptom_origin}
    Select Patient Card    ${first_name}${SPACE}${family_name}
    ${message}    Set Variable If
    ...    '${symptom_origin}'=='symptom questionnaire'
    ...    The patient reported the symptom as part of the symptom questionnaire
    ...    '${symptom_origin}'=='ask about symptom'
    ...    Patient sent a symptom entry.
    ...    '${symptom_origin}'=='navigation-add-menu-new-entry'
    ...    Patient added a symptom to diary, which requires care need assessment.
    Wait Until Page Contains    ${message}

Nurse sees the name and the description of the rule(s) as a part of the message chain
    Wait Until Page Contains    ${rule_name}

The emergency rule(s) is/are not triggered on nurse's side
    Verify If Patient With Symptom Is In Nurse's Work Queue
    ...    ${first_name}${SPACE}${family_name}
    ...    ${automated_tests_clinic}[f01p01s18_care_team]
    ...    displayed=no

The emergency rule(s) is/are not triggered on patient's side
    Navigate to Clinic
    New Message Indicator Should Be Not Visible

Patient sees emergency dialog that contains a link to the patient's inbox.
    Emergency Priority Symptom Is Displayed
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/clinic
    END

Emergency message is sent to patient's inbox.
    Select Latest Clinic Message
    Wait Until Page Contains    ${second_rule_instruction_1}
    Close Clinic's Message Dialog

Patient Asks About Symptom And Sends It To Clinic
    [Arguments]    ${most_severe}    ${including_today}
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Other symptom
    Create A New Symptom Entry
    Complete Other Symptom Form    Severe
    Select Answer To Question    When did you have this symptom?    Mark symptomatic days
    Mark Symptomatic Days For Symptom Report    including_today=${including_today}
    IF    '${most_severe}'=='current_date'
        Try To Click Element    ${more_severe_day}${todays_date}
    ELSE IF    '${most_severe}'=='not_today'
        Try To Click Element    ${more_severe_day}${2days_ago}
    END
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    questionnaires.Check And Write To Text Area
    Click Next Button
    Send Symptom To Clinic

Patient sees clinic specific urgent dialog text
    Wait Until Page Contains    ${urgent_dialog_text}
    Wait Until Page Contains    Symptom entry sent to the clinic
    Close Browser

Urgent rule is triggered on nurse's side
    [Arguments]    ${symptom_origin}
    Login As Nurse
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01p01s18_care_team]
    IF    '${symptom_origin}'=='symptom questionnaire'
        Select Case Type Filter In Work Queue    Symptom from questionnaire
    ELSE IF    '${symptom_origin}'=='ask about symptom'
        Select Case Type Filter In Work Queue    Message about a symptom
    ELSE IF    '${symptom_origin}'=='navigation-add-menu-new-entry'
        Select Case Type Filter In Work Queue    Remove all selections
    END
    Patient Card For Urgent Symptom Appears On Work Queue With The Correct Color
    Nurse sees the correct message as a part of the message chain    ${symptom_origin}
    Nurse sees the name and the description of an urgent rule as a part of the message chain

Patient Card For Urgent Symptom Appears On Work Queue With The Correct Color
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Patient Card's Color Is Correct    high

Nurse sees the name and the description of an urgent rule as a part of the message chain
    Wait Until Page Contains    ${urgent_rule_name}
