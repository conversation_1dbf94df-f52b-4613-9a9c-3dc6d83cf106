*** Settings ***
Documentation       F01P01C01 Pat<PERSON> can attach a photo to a symptom
...
...                 Preconditions:
...
...                 Patient is logged in
...
...                 Patient's active/main module contains a symptom that includes attach a photo. Symptoms with photo
...                 - a. Drainage problems (Breast cancer surgery follow up -module)
...                 - b. Abnormal wound appearance (Breast cancer surgery follow up -module)
...                 - c. Wound infection / Reddish wound (Breast cancer surgery follow up -module)
...                 - d. Skin symptoms or itching (Oral medical treatment for renal cancer, Immuno-oncological pharmacotherapy, Pharmacotherapy for bowel cancers -module)
...                 - e. Other symptom (all modules except Breast cancer recovery)
...
...                 Patient is doing one of the following in that symptom
...                 - a. asking a question about symptom
...                 - b. adding an entry to diary
...                 - c. Answering AEQ

Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}upload_or_remove_photo.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p01c01    patient-web


*** Variables ***
${file_size_1MB}        file_example_JPG_1MB.jpg
${file_size_100kB}      file_example_JPG_100kB.jpg
${file_size_5.9MB}      file_example_JPG_5900kB.jpg
${file_size_9.8MB}      file_example_JPG_9800kB.jpg
${invalid_file}         invalid_file.docx


*** Test Cases ***
Patient can attach a photo to a symptom
    [Tags]    nms9-ver-102    photo-upload
    # TODO: Add testing for reporting a symptomin diary and answering AEQ
    # Asking about symptom
    Login As Patient    ${f01p01c01_patient}[email]    remember_login=Yes
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Drainage problems
    Complete Drainage Problems Questionnaire
    Add Photo To Symptom    ${file_size_1MB}
    Remove Uploaded Photo
    # Photo size is too big
    Add Photo To Symptom    ${file_size_9.8MB}    click_add_photo=no    uploaded=no
    Verify Photo Upload Error
    # Photo file format is not accepted
    Add Photo To Symptom    ${invalid_file}    click_add_photo=no    uploaded=no
    Verify Photo Upload Error
    # More photos are attached than is accepted in the symptom specification
    Add Max Number Of Photo
    Write Photo Description
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Scroll Element Into View    ${send_to_clinic_button}
    Wait Until Page Contains    Symptom entry sent to the clinic

Extension A - Patient can remove a photo (web / app)
    [Tags]    nms9-ver-103    native-app-todo    photo-upload
    Login As Patient    ${f01p01c01_patient}[email]    remember_login=Yes
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Drainage problems
    Complete Drainage Problems Questionnaire
    Add Photo To Symptom    ${file_size_1MB}
    Remove Uploaded Photo
    Verify Photo Group Description Is Hidden
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    Symptom entry sent to the clinic

Extension B - Patient can cancel download (web / app)
    [Tags]    nms9-ver-104    native-app-todo
    Login As Patient    ${f01p01c01_patient}[email]    remember_login=Yes
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Drainage problems
    Complete Drainage Problems Questionnaire
    Cancel Photo Upload    ${file_size_5.9MB}
    Page Should Not Contain    ${image_id}

Extension C - Patient can view photo(s) (web / app)
    [Tags]    nms9-ver-105    native-app-todo    photo-upload
    Login As Patient    ${f01p01c01_patient}[email]    remember_login=Yes
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Drainage problems
    Complete Drainage Problems Questionnaire
    Add Photo To Symptom    ${file_size_1MB}
    Add Photo To Symptom    ${file_size_5.9MB}    click_add_photo=no
    Click Photo Thumbnail To View Full Screen
    Click Left/Right Arrow To Move To Previous/Next Photo
    Close Image Full Screen View


*** Keywords ***
Complete Drainage Problems Questionnaire
    Create A New Symptom Entry
    Wait Until Page Contains Element    ${radio_buttons_option_1}
    questionnaires.Check And Select Radio Buttons
    Sleep    1
    Select Answer To Question    Are any of the following problems related to the use of the drain?
    ...    Bleeding / discharge at the base of the drain
    Select Answer To Question    What does the leakage at the base of the drain look like?
    ...    Bleeding
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Select Radio Buttons
    Select Answer To Question    When did you have this symptom?    Today
    Wait Until Element Is Visible    ${questionnaire_next_button}
