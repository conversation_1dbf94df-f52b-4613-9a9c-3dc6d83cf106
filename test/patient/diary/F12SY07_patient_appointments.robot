*** Settings ***
Documentation       F12SY07 <PERSON><PERSON> can display patient's clinic appointments based on information from an external system
...                 Preconditions:
...                 - Clinic appointments integration is enabled for the clinic.
...                 - In Clinic settings Noona admin defines the names for the appointment types which are displayed for the patient (Mapping)
...                 Test:
...                 - Schedule appointment for patient and verify content from the timeline and modal
...                 - Added Timezone scenarios

Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource
Resource            ${EXECDIR}/resources/native_app/app_more.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/patient/calendar.resource
Resource            ${EXECDIR}${/}resources/patient/diary_add_menu/diary_events.resource
Resource            ${EXECDIR}${/}resources/patient/diary.resource
Resource            ${EXECDIR}${/}resources/patient/questionnaires.resource
Resource            ${EXECDIR}${/}resources/patient/clinic.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f12sy07    patient-web    api


*** Variables ***
###Test Data:
${appointment_reason}                   Radiotherapy
${appointment_reason_text}              Radiotherapy Treatment
${location}                             HALO    # location in legacy = treatment units id
${appointment_tunit_text}               Halo
# TODO: need to convert timezone name from clinic integration settings Time zone location (Europe/London)
${future_timezone_BST}                  BST    # BST when daylight saving time, GMT if not dst
${appointment_type_code}                test4
###APPOINTMENT TIMEZONE A:
${appointment_treatment_unit_id_a}      555
# TODO: need to convert timezone name from clinic integration settings Time zone location (Europe/Paris)
${future_timezone_CEST}                 CEST    # CEST when daylight saving time, CET if not dst
${appointment_tunit_text_a}             Paris HUS
${appointment_type_code_a}              test4
${appointment_reason_text_a}            Laboratory Appointment
${patient_mrn_a}                        9NRyFbIf
${patient_email_a}                      <EMAIL>
${patient_id_a}                         1c64e6a3-2b09-41de-8208-7badd8e9ed58
###APPOINTMENT TIMEZONE B:
${appointment_treatment_unit_id_b}      888
# TODO: need to convert timezone name from clinic integration settings Time zone location (Europe/Helsinki)
${future_timezone_EEST}                 EEST    # EEST when daylight saving time, EET if not dst
${appointment_tunit_text_b}             Helsinki HUS
${appointment_type_code_b}              test5
${appointment_reason_text_b}            Imaging Appointment
###APPOINTMENT TIMEZONE C:
${appointment_treatment_unit_id_c}      555
# TODO: need to comvert timezone name from clinic integration settings Time zone location (Europe/Paris)
${future_timezone_CEST}                 CEST    # CEST when daylight saving time, CET if not dst
${appointment_tunit_text_c}             Paris HUS
${appointment_type_code_c}              test4
${appointment_reason_text_c}            Laboratory Appointment
####NOTE: Replace value for manual removal of appointments: e.g. ${appointment_id}    10835685
###APPOINTMENT REMINDER AND CHANGES
${location_d}                           666
${appointment_reason_d}                 F12SY07 Appointment Test
${location_e}                           777
${appointment_reason_e}                 F12SY07 Appointment Change Test


*** Test Cases ***
Send Appointment To Patient
    [Tags]    nms9-ver-277-1    nms9-ver-277    native-web
    ${patient_id}    Set Variable If
    ...    'staging' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_1}[id_staging]
    ...    ${f12sy07_patient_1}[id_test]
    Send X Future Appointment Dates    7 days    ${location}    ${appointment_reason}    ${patient_id}
    Login As Patient    ${f12sy07_patient_1}[email]
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Appointment From The Timeline
    ...    ${appointment_reason_text}
    ...    ${appointment_tunit_text}
    ...    ${future_timezone_BST}
    Open Appointment And Check The Content
    ...    ${appointment_reason_text}
    ...    ${appointment_tunit_text}
    ...    ${future_timezone_BST}
#    [Teardown]    Run Keywords    Appointment Teardown    ${appointment_id}    ${f12sy07_patient_1}[mrn]
#    ...    AND    Close All App Instances

Send And Cancel Appointment
    [Tags]    nms9-ver-277-2    nms9-ver-277    native-web
    ${patient_id}    Set Variable If
    ...    'staging' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_2}[id_staging]
    ...    ${f12sy07_patient_2}[id_test]
    Send X Future Appointment Dates    28 days    ${location}    ${appointment_reason}    ${patient_id}
    Login As Patient    ${f12sy07_patient_2}[email]
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Appointment From The Timeline
    ...    ${appointment_reason_text}
    ...    ${appointment_tunit_text}
    ...    ${future_timezone_BST}
    [Teardown]    Run Keywords
    ...    Remove Appointment Via API    ${appointment_id}    ${f12sy07_patient_2}[mrn]
    ...    AND    Appointment Removed From The Timeline
    ...    AND    Close All App Instances

Schedule Appointment For Tomorrow
    [Tags]    nms9-ver-277-3    nms9-ver-277    native-web
    ${patient_id}    Set Variable If
    ...    'staging' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_3}[id_staging]
    ...    ${f12sy07_patient_3}[id_test]
    Send X Future Appointment Dates    1 day    ${location}    ${appointment_reason}    ${patient_id}
    Login As Patient    ${f12sy07_patient_3}[email]    No
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Tomorrow's Appointment From The Timeline
    [Teardown]    Run Keywords    Appointment Teardown    ${appointment_id}    ${f12sy07_patient_3}[mrn]
    ...    AND    Close All App Instances

Send Appointment To Patient X Days From Now
    [Documentation]    Uses test data APPOINTMENT TIMEZONE C
    ...    Update ${f12sy07_patient_5}[id] and ${f12sy07_patient_5}[email] to test on different patient
    [Tags]    nms9-ver-277-4    fhir-apt    nms9-ver-277    native-web
    # TODO: NOONA-1506 Send 10-11 appointments and verify diary is displayed
    ${patient_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_5}[id_test]
    ...    ${f12sy07_patient_5}[id_staging]
    Send Appointments To Patient X Days
    ...    15 days
    ...    ${appointment_treatment_unit_id_c}
    ...    ${appointment_type_code_c}
    ...    ${patient_id}
    ...    ${f12sy07_patient_5}[email]
    ...    ${appointment_reason_text_c}
    ...    ${appointment_tunit_text_c}
    ...    ${future_timezone_CEST}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Appointment Via FHIR API    ${appointment_type_code_c}    ${patient_id}    ${appointment_id}

Add Appointments From Different Timezones
    [Documentation]    Uses test data APPOINTMENT TIMEZONE A & B
    [Tags]    nms9-ver-279    defect    time-zone    native-web
    ${patient_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_4}[id_test]
    ...    ${f12sy07_patient_4}[id_staging]
    Set Test Variable    ${patient_id}
    Send Appointments From Timezone A    25 days
    IF    'native' in '${ENVIRONMENT}'
        Close Application
        Setup App Environment
    END
    Send Appointments From Timezone B    60 days
    [Teardown]    Close All App Instances

Extension B - Changes To Appointments
    [Documentation]    Generate patient appointment data to test environments. Appointment email server: Single Patient Time Constraints Email.
    ...    Replace patient's variable before running script to set appointments for different patient which uses different communication channels
    ...    f12sy07_patient_6: email (default in script)
    ...    f12sy07_patient_6_sms: sms
    ...    f12sy07_patient_6_pn: push notification. The test case is constructed into 2 parts (PART1 & PART 2). Run the whole test case if verifying new and changes to appointments notification in the same day. Run each part separately if verifying new and changes to appointment notifications in different days.
    [Tags]    nms9-ver-40-1    nms9-ver-40    manual
    # PART 1: RUN THIS PART ONLY (COMMENT OUT PART 2 BELOW) IF VERIFYING NOTIFICATION OF NEW APPOINTMENTS ONLY.
    ${patient_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_6}[id_test]
    ...    ${f12sy07_patient_6}[id_staging]
    # Create 1st appointment
    Send X Future Appointment Dates    5 days    ${location}    ${appointment_reason}    ${patient_id}
    &{appt1_info}    Create Dictionary
    ...    appt_location=${location}
    ...    appt_reason=${appointment_reason}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointment_id}
    Set Suite Variable    &{appt1_info}
    # Create 2nd appointment
    Sleep    5s    # add 5s to the appointment time
    Send X Future Appointment Dates    10 days    ${location_d}    ${appointment_reason_d}    ${patient_id}
    &{appt2_info}    Create Dictionary
    ...    appt_location=${location_d}
    ...    appt_reason=${appointment_reason_d}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointment_id}
    Set Suite Variable    &{appt2_info}
    # Create 3rd appointment
    Send X Future Appointment Dates    12 days    ${location_d}    ${appointment_reason_d}    ${patient_id}
    &{appt3_info}    Create Dictionary
    ...    appt_location=${location_d}
    ...    appt_reason=${appointment_reason_d}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointment_id}
    Set Suite Variable    &{appt3_info}
    Set Suite Variable    ${appointmentid1_location_tobe_changed}    ${appt1_info}[appt_id]
    Set Suite Variable    ${appointmentid2_location_tobe_changed}    ${appt2_info}[appt_id]
    Set Suite Variable    ${appointmentid3_location_tobe_deleted}    ${appt3_info}[appt_id]
    # TODO: Copy & save the 3 appointment ids above for the subsequent test run in the following part.
    # TODO: The patient should receive email notification about new appointments    about 2hrs BEFORE 21.00 (the test clinic's quiet time starts in Staging)
    # PART 2: RUN THIS PART ONLY (COMMENT OUT THE PART 1) IF VERIFYING CHANGES IN ALREADY SENT APPOINTMENTS IN THE SUBSEQUENT DAY. USE THE SAVE APPOINTMENT IDS ABOVE AND REPLACE THEM IN THE PART2 SCRIPT. OTHERWISE RUN THE WHOLE TEST TO VERIFY NEW AND CHANGES NOTIFICATION IN THE SAME DAY.
    # Update 1st appointment: location from HALO -> 777, apppointment reason from Radiotherapy -> F12SY07 Appointment Change Test
    Set Appointment Date In The Future    5 days
    Send Appointments Via FHIR API
    ...    ${location_e}
    ...    ${appointment_reason_e}
    ...    ${patient_id}
    ...    updated
    ...    ${appointmentid1_location_tobe_changed}
    &{updated_appt1_info}    Create Dictionary
    ...    appt_location=${location_e}
    ...    appt_reason=${appointment_reason_e}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointmentid1_location_tobe_changed}
    # Update 2nd appointment: location from 666 -> 777, appointment reason from F12SY07 Appointment Test -> F12SY07 Appointment Change Test
    Set Appointment Date In The Future    10 days
    Send Appointments Via FHIR API
    ...    ${location_e}
    ...    ${appointment_reason_e}
    ...    ${patient_id}
    ...    updated
    ...    ${appointmentid2_location_tobe_changed}
    &{updated_appt2_info}    Create Dictionary
    ...    appt_location=${location_e}
    ...    appt_reason=${appointment_reason_e}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointmentid2_location_tobe_changed}
    # Remove 3rd appointment
    Remove Appointment Via FHIR API
    ...    ${appt3_info}[appt_reason]
    ...    ${patient_id}
    ...    ${appointmentid3_location_tobe_deleted}
    # TODO: The patient should receive email notification "Changes to appointments" about 2hrs BEFORE 21.00 (the test clinic's quiet time starts in Staging)

Extension B - New Appointments with scheduled questionnaire - Notifications about new appointment
    [Documentation]    Replace patient's variable before running script to set appointments for different patient which uses different communication channels
    ...    f12sy07_patient_8: email (default in script)
    ...    f12sy07_patient_8_sms: sms
    ...    f12sy07_patient_8_pn: push notification
    ...    Email/SMS/naive push notifications are similar to "Changes to appointments"
    [Tags]    nms9-ver-40-2    nms9-ver-40    manual
    ${patient_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_8}[id_test]
    ...    ${f12sy07_patient_8}[id_staging]
    # Create 1st appointment without questionnaire
    Send X Future Appointment Dates    5 days    ${location}    ${appointment_reason}    ${patient_id}
    &{appt1_info}    Create Dictionary
    ...    appt_location=${location}
    ...    appt_reason=${appointment_reason}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointment_id}
    # Create 2nd appointment with questionnaire (treatment unit id b, Treatment visit questionnaire, scheduled 2 days before the appointment)
    Send X Future Appointment Dates
    ...    12 days
    ...    ${appointment_treatment_unit_id_b}
    ...    ${appointment_type_code_b}
    ...    ${patient_id}
    &{appt2_info}    Create Dictionary
    ...    appt_location=${appointment_treatment_unit_id_b}
    ...    appt_reason=${appointment_type_code_b}
    ...    appt_date_time=${future_fhir_appointment_datetime}
    ...    appt_id=${appointment_id}
    # TODO: The patient should receive email notification about new appointments about 2hrs BEFORE 23.00 (the test clinic's quiet time starts)

Extension C - Appointment Reminders
    [Documentation]    Generate patient appointment data to test environments. Reminders for 2-day and 7-day appointment. The send appointment API set future dates 3 days & 8 days since the notification comes in the next morning. Appointment email server: Single Patient Time Constraints Email
    ...    Replace patient's variable before running script to set appointments for different patient which uses different communication channels
    ...    f12sy07_patient_7: email (default in script)
    ...    f12sy07_patient_7_sms: sms
    ...    f12sy07_patient_7_pn: push notification
    [Tags]    nms9-ver-384    manual
    ${patient_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${f12sy07_patient_7}[id_test]
    ...    ${f12sy07_patient_7}[id_staging]
    Send X Future Appointment Dates    3 days    ${location_d}    ${appointment_reason_d}    ${patient_id}
    @{in_2day_appt_info}    Create List
    ...    ${location_d}
    ...    ${appointment_reason_d}
    ...    ${future_fhir_appointment_datetime}
    ...    ${appointment_id}
    Sleep    3s
    Send X Future Appointment Dates    8 days    ${location}    ${appointment_reason}    ${patient_id}
    @{in_7day_appt_info}    Create List
    ...    ${location}
    ...    ${appointment_reason}
    ...    ${future_fhir_appointment_datetime}
    ...    ${appointment_id}
    # TODO: Once these above test steps are executed. The patient should receive reminder email notifications for 2-day & 7-day appointments about 1hr AFTER the quite time ends 6.00 (in Staging)

Extension D - Upcoming appointments Calendar view - Appointments
    [Tags]    nms9-ver-399-1    nms9-ver-399    native-web
    Add An Activated Patient Under Appointment Clinic    f12sy07-d1
    Send Multiple Appointments To Patient
    Login As Patient    ${patient_email}
    Wait Until Element Is Visible    ${show_all_button}
    Click Show All Button
    Patient Can See And Press The Calendar Button
    All dates that have an appointment/event will be circled
    All dates that have appointments with unopened content will have a red dot
    Patient can click on the dates to get a list of events that are scheduled for the day
    ...    ${future_30days_1}    ${appointment_reason_text}    ${appointment_reason_text_b}
    Unopened Appointments From List Have Red Dots And Bolded
    Patient can click on the appointments/events to get more details
    ...    ${appointment_reason_text_b}    ${future_30days_1}    ${appointment_tunit_text_b}    appointment
    If an unopened appointment/event is opened and modal is closed, the red dot will disappear
    Patient can navigate to the next or previous month
    Patient can click on the List of event -button to navigate back to the Calendar list view
    [Teardown]    Close All App Instances

Extension D - Upcoming appointments Calendar view - Personal Events
    [Tags]    nms9-ver-399-2    nms9-ver-399
    Add An Activated Patient Under Appointment Clinic    f12sy07-d2
    Send Appointment In X Days
    Login As Patient    ${patient_email}
    Add Personal Events As Patient    Diagnosis    Diagnosis date    3 days
    Set Test Variable    ${future_3days}    ${future_date}    # converts and sets the date 3 days from now
    Add Personal Events As Patient    Medical treatments    Treatment visit    31 days
    Click Show All Button
    Patient Can See And Press The Calendar Button
    Patient can click on the dates to get a list of events that are scheduled for the day
    ...    ${future_3days}    ${appointment_reason_text}    Diagnosis date
    Appointment In List Has Red Dot And Bolded    Diagnosis date    behavior=false
    Patient can click on the appointments/events to get more details
    ...    Diagnosis date    ${future_3days}    ${appointment_tunit_text_b}    event
    Try To Click Element    ${event_details_close_modal}
    Try To Click Element    ${appointment_list_close_modal}
    The Event Moves To Diary Entries When Expires
    [Teardown]    Close All App Instances

Extension E - Deleted appointment with a questionnaire
    [Tags]    nms9-ver-423    native-web
    Generate Random Patient Data    name=f12sy07-e
    Create An Activated Patient Via API
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_F10N01}
    Send X Future Appointment Dates    2 days    777    test2    ${patient_id}
    Answer Symptom Questionnaire From Appointment
    # clinic removes appointment
    Appointment Teardown    ${appointment_id}    ${patient_mrn}
    Close All App Instances
    # patient can see questionnaire in detail after appointment deletion
    Setup App Environment
    Login As Patient    ${patient_email}
    Go To Diary
    Wait Until Element Is Visible    ${latest_questionnaire_in_diary}
    Text Should Not Be In The Page    ${appointment_reason_text}
    Select Latest Questionnaire In Diary
    Questionnaire Header Is Displayed In Modal    ${BASELINE_QUESTIONNAIRE}
    Text Should Be In The Page    ${current_date}
    Set Test Variable    ${login_token}    ${APPOINTMENT_USER_TOKEN}
    [Teardown]    Run Keywords    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    AND    Close All App Instances

*** Keywords ***
Send Appointments To Patient X Days
    [Arguments]    ${x_days}    ${appointment_treatment_unit_id}    ${appointment_type_code}    ${patient_id}
    ...    ${patient_email}    ${appointment_reason_text}    ${appointment_tunit_text}    ${future_timezone_name}
    Send X Future Appointment Dates    ${x_days}    ${appointment_treatment_unit_id}    ${appointment_type_code}
    ...    ${patient_id}
    Login As Patient    ${patient_email}    No
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Appointment From The Timeline
    ...    ${appointment_reason_text}
    ...    ${appointment_tunit_text}
    ...    ${future_timezone_name}
    Open Appointment And Check The Content
    ...    ${appointment_reason_text}
    ...    ${appointment_tunit_text}
    ...    ${future_timezone_name}
    Sleep    5s

Send Appointments From Timezone A
    [Arguments]    ${no_of_days}
    Send Appointments To Patient X Days
    ...    ${no_of_days}
    ...    ${appointment_treatment_unit_id_a}
    ...    ${appointment_type_code_a}
    ...    ${patient_id}
    ...    ${f12sy07_patient_4}[email]
    ...    ${appointment_reason_text_a}
    ...    ${appointment_tunit_text_a}
    ...    ${future_timezone_CEST}
    [Teardown]    FHIR Appointment Teardown    ${appointment_type_code_a}    ${patient_id}

Send Appointments From Timezone B
    [Arguments]    ${no_of_days}
    Send Appointments To Patient X Days
    ...    ${no_of_days}
    ...    ${appointment_treatment_unit_id_b}
    ...    ${appointment_type_code_b}
    ...    ${patient_id}
    ...    ${f12sy07_patient_4}[email]
    ...    ${appointment_reason_text_b}
    ...    ${appointment_tunit_text_b}
    ...    ${future_timezone_EEST}
    [Teardown]    FHIR Appointment Teardown    ${appointment_type_code_b}    ${patient_id}

Send Multiple Appointments To Patient
    Send Appointment And Convert    3 days    ${appointment_reason}    ${location}
    Set Test Variable    ${future_3days}    ${future_date}
    ${full_month_year}    Convert Date    ${future_date}    result_format=%B %Y
    Set Test Variable    ${previous_month1}    ${full_month_year}
    Send Appointment And Convert    30 days    ${appointment_reason}    ${location}
    Set Test Variable    ${future_30days_1}    ${future_date}
    ${full_month_year}    Convert Date    ${future_date}    result_format=%B %Y
    Set Test Variable    ${previous_month2}    ${full_month_year}
    Sleep    3s    # wait for 3s before next appointment is sent to appointment time will be different
    Send Appointment And Convert    30 days    ${appointment_type_code_b}    ${appointment_treatment_unit_id_b}
    Set Test Variable    ${future_30days_2}    ${future_date}
    Send Appointment And Convert    60 days    ${appointment_reason}    ${location}
    Set Test Variable    ${future_60days}    ${future_date}
    ${full_month_year}    Convert Date    ${future_date}    result_format=%B %Y
    Set Test Variable    ${next_month_year}    ${full_month_year}
    Send Appointment And Convert    90 days    ${appointment_reason}    ${location}
    Set Test Variable    ${future_90days}    ${future_date}

Send Appointment In X Days
    [Arguments]    ${days}=3 days
    Send Appointment And Convert    ${days}    ${appointment_reason}    ${location}
    Set Test Variable    ${future_3days}    ${future_date}
    ${full_month_year}    Convert Date    ${future_date}    result_format=%B %Y
    Set Test Variable    ${previous_month1}    ${full_month_year}

Send Appointment And Convert
    [Arguments]    ${days}    ${appointment_reason}    ${location}
    Set Appointment Date In The Future    ${days}
    Send Appointments Via FHIR API    ${location}    ${appointment_reason}    ${patient_id}

All dates that have an appointment/event will be circled
    Appointment Date Is Circled    ${future_3days}
    Appointment Date Is Circled    ${future_30days_1}
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_60days}
    ELSE
        Go To Next Or Previous Month    next
    END
    Appointment Date Is Circled    ${future_60days}
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_60days}
    ELSE
        Go To Next Or Previous Month    previous
    END

All dates that have appointments with unopened content will have a red dot
    Appointment Date Is Unopened    ${future_3days}
    Appointment Date Is Unopened    ${future_30days_1}
    Appointment Date Is Unopened    ${future_30days_2}
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_60days}
    ELSE
        Go To Next Or Previous Month    next
    END
    Appointment Date Is Unopened    ${future_60days}
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_60days}
    ELSE
        Go To Next Or Previous Month    previous
    END

Patient can click on the dates to get a list of events that are scheduled for the day
    [Arguments]    ${days}    ${reason1}    ${reason2}
    Select Date From Calendar    ${days}
    Number Of Appointments Is Correct    2
    Appointment Title In List Is Correct    1    ${reason1}
    Appointment Title In List Is Correct    2    ${reason2}

Unopened Appointments From List Have Red Dots And Bolded
    Appointment In List Has Red Dot And Bolded    ${appointment_reason_text}
    Appointment In List Has Red Dot And Bolded    ${appointment_reason_text_b}

Patient can click on the appointments/events to get more details
    [Arguments]    ${reason}    ${days}    ${tunit}    ${event_or_appointment}
    Select Appointment From List    ${reason}
    IF    '${event_or_appointment}'=='appointment'
        Verify Appointment Details From Modal    ${reason}    ${days}    ${tunit}
    ELSE
        Verify Event Details In Modal    ${reason}    ${days}    ${tunit}
    END

If an unopened appointment/event is opened and modal is closed, the red dot will disappear
    Try To Click Element    ${appointment_details_close_modal}
    Try To Click Element    ${appointment_list_close_modal}
    Appointment Date Is Unopened    ${future_30days_1}
    Appointment Date Is Unopened    ${future_30days_1}
    Select Date From Calendar    ${future_30days_1}
    Appointment In List Has Red Dot And Bolded    ${appointment_reason_text}
    Appointment In List Has Red Dot And Bolded    ${appointment_reason_text_b}    behavior=false
    Select Appointment From List    ${appointment_reason_text}
    Try To Click Element    ${appointment_details_close_modal}
    Try To Click Element    ${appointment_list_close_modal}
    Wait Until Page Does Not Contain Element    ${appointment_list_close_modal}
    Appointment Date Is Unopened    ${future_30days_1}    behavior=false

Patient can navigate to the next or previous month
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_30days_1}
    ELSE
        Go To Next Or Previous Month    next
    END
    Text Should Be In The Page    ${next_month_year}
    “You have reached the end of your events.” is displayed when last month is reached
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_3days}
    ELSE
        Go To Next Or Previous Month    previous
    END
    Text Should Be In The Page    ${previous_month1}
    Text Should Be In The Page    ${previous_month2}

“You have reached the end of your events.” is displayed when last month is reached
    IF    'native' in '${ENVIRONMENT}'
        Go To Appointment Date    ${future_60days}
    ELSE
        Go To Next Or Previous Month    next
    END
    Text Should Be In The Page    You have reached the end of your events.

Patient can click on the List of event -button to navigate back to the Calendar list view
    Try To Click Element    ${switch_to_calendar_list_view_button}
    Wait Until Element Is Visible    ${upcoming_appointment_list_view}
    Patient Can See And Press The Calendar Button

Add Personal Events As Patient
    [Documentation]    Put "today" as days value if event is today
    [Arguments]    ${phase}    ${type}    ${days}
    Select Event From Add Menu
    Select Treatment Phase    ${phase}
    Select Event Type    ${type}
    IF    '${days}'=='today'
        Wait Until Element Is Visible    ${select_event_date_dropdown}
        Wait Until Element Is Visible    ${select_event_date_dropdown}
        Click Element    ${select_event_date_dropdown}
        Wait Until Element Is Visible    ${calendar}
        Set Event Date
        IF    'native' in '${ENVIRONMENT}'
            Try To Input Text    ${event_hour_selector}    1    # to empty the field
            Try To Input Text    ${event_hour_selector}    ${hour}
            Try To Input Text    ${event_minute_selector}    1    # to empty the field
            Try To Input Text    ${event_minute_selector}    ${minute}
        ELSE
            Try To Input Text    ${event_hour_selector}    ${hour}
            Try To Input Text    ${event_minute_selector}    ${minute}
        END
    ELSE
        Get Appointment Date    ${days}
        Select Event Date    ${future_date}
    END
    Add Event Location    ${appointment_tunit_text_b}
    Add Event Notes    Please bring your latest symptom form
    Save Event

The Event Moves To Diary Entries When Expires
    Add Personal Events As Patient    Follow-up phase    Clinic appointment    today
    Click Show All Button
    Patient Can See And Press The Calendar Button
    Sleep    1
    Select Date From Calendar    ${future_date}    with_red_dot=${FALSE}
    Appointment Title In List Is Correct    1    Clinic appointment
    Try To Click Element    ${appointment_list_close_modal}
    Sleep    130s    # wait until event expires
    Go To Diary
    Events Are Displayed In Diary Timeline
    Click Show All Button
    Patient Can See And Press The Calendar Button
    Appointment Date Is Circled    ${future_date}    behavior=false
    Appointment Date Is Unopened    ${future_date}    behavior=false

Events Are Displayed In Diary Timeline
    Wait Until Element Is Visible    xpath=(${diary_entries_section}//h6)[1]
    ${actual_month_year}    Get Text    xpath=(${diary_entries_section}//h6)[1]
    Should Be Equal    ${full_month_year}    ${actual_month_year}
    Generic: Element Should Contain    xpath=(${diary_entries_section}//h6)[2]    ${weekday}
    Generic: Element Should Contain    xpath=(${diary_entries_section}//h6)[2]    ${month_date}
    Generic: Element Should Contain    xpath=(${diary_entries_section}//h6)[2]/../..//div[@class='timeline-day-items']
    ...    Clinic appointment
    Generic: Element Should Contain    xpath=(${diary_entries_section}//h6)[2]/../..//div[@class='timeline-day-items']
    ...    PERSONAL

Set Event Date
    Get Appointment Date    2 minutes
    ${hour}    Convert Date    ${future_date}    result_format=%H
    Set Test Variable    ${hour}
    ${minute}    Convert Date    ${future_date}    result_format=%M
    Set Test Variable    ${minute}
    ${weekday}    Convert Date    ${future_date}    result_format=%a
    Set Test Variable    ${weekday}
    ${month_date}    Convert Date    ${future_date}    result_format=%-d.%-m
    Set Test Variable    ${month_date}
    ${full_month_year}    Convert Date    ${future_date}    result_format=%B %Y
    Set Test Variable    ${full_month_year}

Answer Symptom Questionnaire From Appointment
    Sleep    120s    # wait for questionnaire to be sent
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Complete Other Symptom Questionnaire With Severe Symptom
    Click Emergency Symptom Ok Button
    Select Latest Clinic Message
    Wait Until Page Contains    ${severe_symptom_inbox_text}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}
