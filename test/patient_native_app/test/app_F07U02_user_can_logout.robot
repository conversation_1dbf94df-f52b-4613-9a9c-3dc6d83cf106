*** Settings ***
Documentation       F07U02 User can logout
...
...                 F07U02 User can logout - Main success scenario (app)
...                 Preconditions:
...                 - Patient account has been created
...                 - Patient has already setup a password
...                 Steps:
...                 - Select log out from More menu.
...                 - Confirmation dialog is displayed.
...                 - Select log out.
...                 - User is logged out.
...                 - App: User is taken back to app landing screen.
...
...                 F07P02 Patient can be logged out automatically - Extension D — Two factor authentication (app)
...                 Preconditions specific to this extension:
...                 - The clinic has set two-factor authentication on.
...                 Steps:
...                 - The patient gets logged out automatically, or logs out manually.
...                 - The patient can log in again by entering just his e-mail address
...                 - and the inputs required by two-factor authentication

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_more.resource

Suite Setup         Set Libraries Order
Test Setup          Run Keywords    Setup Native App In Browserstack
...                     AND    Delete All Messages In Server    ${sms_server_id}    ${sms_api_key}
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}

Force Tags          native-app    usecase-f07u02-app


*** Test Cases ***
F07U02 User can logout: Main success scenario (app)
    [Tags]    nms9-ver-371-app
    Login To Native App In Browserstack    ${f07u02app_patient_main}[email]
    Logout From Patient App

F07P02 Patient can be logged out automatically - Extension D — Two factor authentication (app)
    [Tags]    nms9-ver-227-app    patient-2fa    sms
    Login To Native App With 2FA In Browserstack    email=${f07u02app_patient_extD}[email]    autho=yes
    The Patient Gets Logged Out Automatically Or Logs Out Manually
    Sleep    2s
    Re-login To Native App With 2FA In Browserstack    email=${f07u02app_patient_extD}[email]    autho=yes


*** Keywords ***
The Patient Gets Logged Out Automatically Or Logs Out Manually
    [Documentation]    In this case because of time contstraint only the manual logout part is automated
    Logout From Patient App
