# noona-system-test
System test repository for Noona

## Setup

### Prerequisites
1. Enable 2 Factor Authentication in [GitLab](https://docs.gitlab.com/ee/user/profile/account/two_factor_authentication.html#enabling-2fa)
2. Generate a [SSH keypair for GitLab](https://docs.gitlab.com/ee/ssh/#generate-an-ssh-key-pair)
3. Clone this repository:
```
<NAME_EMAIL>:varian-noona/testing/noona-system-test.git
```

### Robot Framework Installation on macOS

1. Run the following command in the repository root:
```
make configure
```

This make target:
- installs Homebrew
- installs Python 3.9
- configures a venv for Python
- configures a webdriver for Chrome

> **_NOTE:_**  As of September 2023, we should manually install the above tools/softwares. The `make configure` will be updated later.

### How to install webdriver manually for Chrome
Sometimes we are facing a situation when our local chromedriver is not compatible with the Chrome browser's version we are having on our machine. It's time to update our Chromedriver.
1. Go to this website to download [ChromeDriver for testing](https://googlechromelabs.github.io/chrome-for-testing/)
2. Scroll down to the Stable table and download the chromedriver binary file which matches your OS (e.g MacOS)
3. Locate to the folder where this binary file was downloaded
4. Move this binary file to these following locations
`/usr/local/bin` and `/opt/homebrew/bin` (given that Homebrew has been installed)

### Native App Automation Setup

### Install Appium on macOS

1. Go to https://appium.io/docs/en/latest/quickstart/install/
2. Follow installation steps as per operating system
3. Install Appium and open it. It populates the default host (0.0.0.0) and port (4723) option which you can change. It also mentions the version of Appium being used.

### Install Robot Framework AppiumLibrary
1. Refer to https://pypi.org/project/robotframework-appiumlibrary/

### Tip:
1. In interpreter make sure to have Appium-Python-Client with version 2.8.1 if you are getting errors while running TA locally.

For API 28 and below: 
`dumpsys window windows | grep -E 'mCurrentFocus'`

For API 29 and up:
`dumpsys window displays | grep -E 'mCurrentFocus'`

To see the information about app package info and app activity info:
```
1|generic_x86_arm:/ $ dumpsys window windows | grep -E 'mCurrentFocus'                                         
  mCurrentFocus=Window{fc42053 u0 com.noona.application.staging/com.noona.application.staging.MainActivity}
```
For Noona staging application the following informations are needed for the script to open the app:
```
appPackage = com.noona.application.staging
appActivity = com.noona.application.staging.MainActivity
```

## Development
Any text editor can be used for development. Robot Framework website has a few [recommendations](https://robotframework.org/#tools) including [Atom](https://atom.io/packages/language-robot-framework), [Sublime3,](https://github.com/andriyko/sublime-robot-framework-assistant) etc...
[PyCharm Community Edition](https://www.jetbrains.com/pycharm/) (and the IntelliBot @SeleniumLibrary Patched plugin) is also an alternative.
**Note**: IDE/Text editors require to be approved by Varian IT.

### How to run tests for Web Automation
The runner-common.sh script expects to have the Robot framework in the path so first run the command `source ~/.virtualenvs/noona-system-test/bin/activate` to configure your local environment.

*    add **Tags** to robot test suites. They can be added as **[Tags]** to the test case or specified in **Force Tags** in the **Settings** of the test suite. **runner-common.sh** runs tests based on the tags given as parameters when running the script.
*    **runner-common.sh** reads tag and environment information from the environment variables **INCLUDE_TAGS**, **EXCLUDE_TAGS** and **ENVIRONMENT**. On top of that you can include any additional parameters you'd like to use as additional arguments for the execution command of the **runner-common.sh** file.
*    For example, if you want to run all tests tagged with **smoke** while excluding tests tagged with **wip**, using the **dev1** environment, you can set those values to the before mentioned environment variables and then simply run **./runner-common.sh**. Alternatively you may set the tag values as additional arguments for the actual script.
*    Example scripts which work identically (using tag environment variables and tag additional parameters at the same time is not recommended): 
```
export ENVIRONMENT=test
export INCLUDE_TAGS=smoke
export EXCLUDE_TAGS=wip 
./runner-common.sh
```
```
export ENVIRONMENT=test
./runner-common.sh --include smoke --exclude wip
```
```
Oneliner:
export ENVIRONMENT=dev1 && ./runner-common.sh --include smoke --exclude wip
```
*    **runner-common.sh** will automatically include the variable file **data/vars-ENVIRONMENT.yaml** file into the test runs based on which environment was set into the **ENVIRONMENT** variable (defaults to **test**). For additional variable files you can add them as arguments for the **runner-common.sh** script.
*    This example script would add **data/vars-test.yaml** as additional variable file to be used in the test run:
```
export ENVIRONMENT=test
./runner-common.sh --include smoke --exclude wip --variablefile data/vars-test.yaml
```
*    open a terminal in noona-system-test root directory and run **runner-common.sh**. You can also run a specific test suite. eg: ./runner-common.sh --include login_all
*    results of the test are stored in the directory **output** with timestamped folders. Remember to cleanup older results as needed.
*   ***To run tests locally***, before execution go to Test Repository -> data folder -> vars-test.yaml file
Comment following line
```
REMOTE_URL: http://selenium-hub:4444/wd/hub
```

### Running tests with local frontends proxied to test backend and data

As a developer changing frontend code, you can get additional test exposure before merging to master by running a subset of robot tests locally against your changes while developing.

Principle: have Noona's patient frontend run locally. Set proxy to map all API calls to test environment where test data sets are prepared.  

Known issues currently:
* Tests involving following login links will not work

#### Preparations:

Patient frontend
* Prepare and launch patient frontend running as described in https://gitlab.com/varian-noona/development/core/noona/-/blob/master/noona-web/src/main/resources/patient/README.md#local-development
* At patient frontend, execute to start: `npm run web:test`

git Local tests are configured to execute in a larger browser window to help to understand failures faster. 

#### Steps:

1. To run tests at **noona-system-test** execute `./run-local-patient.sh --include [TAG]`
1.1 Replace [TAG] with e.g "patient-web"
2. Open test report at `output/local-patient/latest/report.html`
2.1 Tip: after rerun, just reload this in browser
3. To debug robot test with single browser run  `./debug-local-patient.sh --include [TAG]`
3.1 Open test report at `output/local-patient/latest-debug/report.html`

#### Tips

Having both patient & clinic frontends running in localdev is not strictly necessary. You can
then set for example clinic urls in `vars-local.yaml` to point directly to test environment.

You can always run test(s) against branching-off point (master) and see comparison what failing tests your code change caused.

## Native App Automation (iOS & Android) with Browserstack

### Prerequisites:

1. Latest native app builds
- a. For **nightly run**, native app urls are already defined in Gitlab
- b. For **local run**, latest artifacts are stored in [sharepoint](https://varian.sharepoint.com/:f:/s/noona/EtRyFsmFohxJhnnjS2nXpfYBF-0hpJDKHdJcojAQ6CCiwA?e=DMBgEh).
2. Browserstack Noona account with **App Live** & **App Automate** access

### How to run tests for iOS & Android Automation locally with Browserstack

### Android Studio Installation on macOS

1. Install Android Studio (https://developer.android.com/studio)
2. Go to “Configure” > “AVD Manager” after completing the installation
3. Click “Create Virtual Device”
4. Select Hardware e.g. Pixel 2 - API 29 and click "Next" button
5. Download system image
6. Wait and complete Android Virtual Device (ADV) configuration
7. Click Run (Play) icon and launch the device
8. AVD should be displayed on your screen

### Add ANDROID_HOME in Environment Variables
* MAC Environment Variable  (Add below lines inside ~/.profile OR ~/.bash_profile OR ~/.zshrc): 
Note: include JAVA_HOME if not yet set (e.g. v1.8)
```
export JAVA_HOME=$(/usr/libexec/java_home -v 1.8)
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$JAVA_HOME/bin 
```

### Configure Android Virtual Device (AVD)

* Open Android Virtual Device (AVD)
* Drag and drop [Noona_staging apk](https://varian.sharepoint.com/:u:/s/noona/EczyXfr4j41Hhcw_4sIencYBozOXm1lXny69AuJm0Js5Xg?e=FY0MxS) installer on AVD
* Complete the installation and open the app
* Open a command prompt and write `adb devices` command to see connected devices and get the device ID:
```
List of devices attached 
emulator-5554.   Device
```
* Write command `adb shell`

### Steps:

1. Define the values of the following under **runner-native.sh** if necessary:
   - a. DEVICE_NAME
   - b. APP
   - c. PLATFORM_NAME
   - d. PLATFORM_VERSION
   - e. ENABLE_PASSCODE
2. Update the values of the following under **vars-native.yaml** if necessary:
   - SERVICE_ACCOUNT_USERNAME
   - SERVICE_ACCOUNT_PASSWORD
   - REMOTE_URL
3. Run tests by command ```./runner-native.sh --i native-app``` locally
4. Check in **Browserstack** > **App Automate** > **Test Mobile** the run sessions and live/replay video

### How to run tests for Android Automation in Android Virtual Device (AVD)
###### NOTE: Use this as backup for Android tests in case Browserstack is not working 
### Prerequisites:

1. AVD is up and running
2. Noona app is installed on AVD (e.g. Noona-staging app)
3. Appium server is up and running
*  Note of matching server and device configuration in **common_mobile.resource**

### Steps:

1. Open Terminal
2. Run command `./runner-native.sh --include native-app`
   *  Results of the test are stored in the directory output with timestamped folders same as the web automation tests results. 
   *  Remember to cleanup older results as needed.
   *  To run tests locally, before execution go to `Test Repository -> data folder -> vars-<*environment*>.yaml` file Comment following line
```
REMOTE_URL: http://selenium-hub:4444/wd/hub
```

#### Points For Developing Native App Test Scripts

* Use **resources/native_app** to **store/find** native app resources
* Use **resources/common_mobile.resource** to **store/find** common keywords for native app automation
* Use **test/native_app** to **store/find** native app test cases
* Native app local test results is found at **output/native**
* Adding **Tags** is the different with web automation e.g. **JAMA-ID-app** or **NMS7-VER-123-app** and add another tag **native-app**
* Configuration for mobile environment was set in the file **common_mobile.resource**

### Inspecting Elements Via Browserstack (iOS only)

:warning: For Android - device PIN code cannot be configured hence this can not applicable. 
- Refer to **Inspecting Elements Via Chrome’s Inspect Devices (Android only)**

:warning: For iOS, Privacy Screen can be disabled in `Noona Log in > About box > PS`

1. In Browserstack, go to the App Live feature
2. Upload the latest **iOS .ipa** file under test if not yet uploaded
3. Select a platform **iOS REAL DEVICES**
4. Select a device
5. Noona app will start on the selected device
6. In the **Dev Tools** in the right panel of the website, select **Inspect(Beta)**
7. Initiate **UI inspector mode**.
8. Select an element to inspect by hovering and clicking on the device 
9. Locators will display on the right panel of the website
10. Get the type and other related values
11. IOS element locator will usually look like this :
```
//XCUIElementTypeButton[contains(@name, 'LOG IN')] 
```

### Inspecting Elements Via Chrome’s Inspect Devices (Android Device only)

### Prerequisites:
1. Enable Developer mode on Android device
2. Connect Android device to laptop via usb cable
3. Allow permissions on USB debugging

### Steps:
1. In Chrome, go to `chrome://inspect/#devices`
2. Click **Inspect**
3. Webview elements should be displayed on separate window
4. Use the inspect icon (same with web) to select/inspect UI elements
5. Locators have the same format with web

### Tagging conventions

We use tags to categorize test cases as well as following statistics like what test cases are (currently) manual.

**Each test case must be tagged with the use case id the test case relates to.** Use prefix `usecase-` and small letters in the tag name. E.g. `usecase-f01n16`
Additionally use the JAMA ids equivalent to each test case. Use prefix `NMS7-VER-` and numeric id in the tag name. E.g. `NMS7-VER-254`

You can set tags to individual test case with `[Tags]` setting. You can also set tags to many test cases with `Force Tags` and `Default Tags` in the suite settings. Read more about available options in [Robot Framework User Guide](http://robotframework.org/robotframework/latest/RobotFrameworkUserGuide.html#tagging-test-cases).

Following tags are optional to use when deemed suitable:
* If the test case is not yet automated, use tag `manual`
* To denote that the test case is a smoke test, use tag `smoke`
* To denote that the test case is region-specific, use prefix `region-` and small letters in the tag name. Eg. `region-us`, `region-europe`
* To denote that the test case relates to an accessibility standard, use prefix `accessibility` and small letters in the tag name. Eg. `accessibility-wcag-a`
* To denote that the test case relates to some on-going ticket in JIRA (eg. bug report), use the JIRA ticket id directly as a tag. Eg. `NOONA-8242` or `TESTING-124`
* To denote that the test case relates to native app, use prefix `NMS7-VER-` and prefix `-app` E.g. `NMS7-VER-40-app`
* If the test case is related to native app but not yet done, use tag `native-app-todo`
* If the test case is related to Pendo guides, use tag `pendo`
* If the test case is not in use (as of now or not at all but we want to keep it documented), use tag `disabled`
* If the test case already created should be run locally (due to time constraint), use tag `local`
* If the test case to be created should be run locally (due to time constraint), use tag `local-wip`
* If the test case cannot be automated as of the moment and can be tested only manually (native app-related), use tag `non-automated`

### How to run PROD smoke tests locally
* Navigate to appropriate `vars-prod-x.yaml` and uncomment `REMOTE_URL` variable. Since, we are running tests locally so we don't 
need to enable remote server. 
* Navigate to `runner-common.sh` file and uncomment 4 variables and provide appropriate values. These variables can be used only for PROD smoke tests. 
  * NURSE_USERNAME
  * NURSE_PASSWORD
  * PATIENT_USERNAME
  * PATIENT_PASSWORD
* Run this command to run `prod_smoke_tests`
```
e.g: Run prod smoke tests in EU
export ENVIRONMENT=prod-eu                     
./runner-common.sh --include prod_smoke_tests
```

### Contributing
Please read our [Contribution guidelines for this project](docs/CONTRIBUTING.md).

### License
This project and all the source code contained is proprietary software. It is owned by Varian Medical Systems and is confidential. Redistribution of the source code is strictly forbidden.
```
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
```